module.exports = [
    {
        config: {
            minAmountPerAuction: ethers.utils.parseUnits("1", 18),
            auctionLength: "5",
            blockTime: "1000",
            syncFee: "0",
            fillFee: "0",
            minPositionWidth: "1",
            minAmountLowerPriced: true
        },
        twapSource: "******************************************", // uniswapV3Source
        curveMath: "******************************************", // constantProduct
        inputPool: "******************************************", // uniswapV3PoolMock
        owner: "******************************************", // coverPoolManager
        token0: "******************************************", // token0
        token1: "******************************************", // token1
        tickSpread: "20", // tickSpread
        twapLength: "5" // twapLength
    }
];