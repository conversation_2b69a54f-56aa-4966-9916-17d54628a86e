{"arb_goerli": {"token0": {"contractName": "Token20", "contractAddress": "******************************************", "constructorArguments": ["USD Coin", "USDC", 6], "created": "2023-08-23T15:56:24.723Z"}, "token1": {"contractName": "Token20", "contractAddress": "******************************************", "constructorArguments": ["Wrapped Ether", "WETH", 18], "created": "2023-08-23T15:56:24.715Z"}, "tickMapLib": {"contractName": "TickMap", "contractAddress": "******************************************", "constructorArguments": [], "created": "2023-11-25T12:44:23.611Z"}, "ticksLib": {"contractName": "Ticks", "contractAddress": "******************************************", "constructorArguments": [], "created": "2023-11-25T12:44:25.752Z"}, "rangePositionsLib": {"contractName": "RangePositions", "contractAddress": "******************************************", "constructorArguments": [], "created": "2023-11-25T12:44:27.302Z"}, "limitPositionsLib": {"contractName": "LimitPositions", "contractAddress": "******************************************", "constructorArguments": [], "created": "2023-11-25T12:44:28.911Z"}, "limitPoolManager": {"contractName": "LimitPoolManager", "contractAddress": "******************************************", "constructorArguments": [], "created": "2023-11-25T12:44:30.623Z"}, "limitPoolFactory": {"contractName": "LimitPoolFactory", "contractAddress": "******************************************", "constructorArguments": ["0x6D3AF137E75097B892683832a2c0132f99625d0e"], "created": "2023-11-25T12:44:32.085Z"}, "swapCall": {"contractName": "SwapCall", "contractAddress": "0x4017cade72ea448a46ee0176eacef49c0e91d6f5", "constructorArguments": [], "created": "2023-11-25T12:44:33.671Z"}, "mintRangeCall": {"contractName": "MintRangeCall", "contractAddress": "0x767ace5b40c9c70159051b55895f1dc4d5ad770d", "constructorArguments": [], "created": "2023-11-25T12:44:35.331Z"}, "burnRangeCall": {"contractName": "BurnRangeCall", "contractAddress": "0x9d9102bb05a935c5aa2cd350e7fa956aed2e283c", "constructorArguments": [], "created": "2023-11-25T12:44:36.992Z"}, "mintLimitCall": {"contractName": "MintLimitCall", "contractAddress": "0x57b6524810baaacd9726c26942bfe04500569271", "constructorArguments": [], "created": "2023-11-25T12:44:38.947Z"}, "burnLimitCall": {"contractName": "BurnLimitCall", "contractAddress": "0x581a56e3140beb3ff2f34d549daff05ac5553d36", "constructorArguments": [], "created": "2023-11-25T12:44:40.594Z"}, "snapshotLimitCall": {"contractName": "SnapshotLimitCall", "contractAddress": "0x33bb5144306c7e29abf6fc5663cf2f370648f651", "constructorArguments": [], "created": "2023-11-25T12:44:42.153Z"}, "quoteCall": {"contractName": "QuoteCall", "contractAddress": "0x1bcb9a43ea1a76c326341167e2d3b06267740dad", "constructorArguments": [], "created": "2023-11-25T12:44:43.948Z"}, "feesCall": {"contractName": "FeesCall", "contractAddress": "0x22c2269dc214d93d61afceb7e9228ae8e3d27eac", "constructorArguments": [], "created": "2023-11-25T12:44:45.406Z"}, "sampleCall": {"contractName": "SampleCall", "contractAddress": "0x41fc7610d53358fb9aa75e7e4c1c4558a3509905", "constructorArguments": [], "created": "2023-11-25T12:44:47.083Z"}, "snapshotRangeCall": {"contractName": "SnapshotRangeCall", "contractAddress": "0xf6485573b3e88e9906d87bc51f5e526990a8209d", "constructorArguments": [], "created": "2023-11-25T12:44:48.554Z"}, "limitPoolImpl": {"contractName": "LimitPool", "contractAddress": "0x50903014ec42471b8f628ce137acec4f860b2d8c", "constructorArguments": ["0x1b215002E688135549CC0290d6Cf1F94E3AA425c"], "created": "2023-11-25T12:44:50.187Z"}, "positionERC1155": {"contractName": "PositionERC1155", "contractAddress": "0xcf5b954159719a3eb768d18b16d23d85d516ec39", "constructorArguments": ["0x1b215002E688135549CC0290d6Cf1F94E3AA425c"], "created": "2023-11-25T12:44:51.809Z"}, "poolRouter": {"contractName": "PoolsharkRouter", "contractAddress": "0x24757e9d68bfcc99a9dba0a62737703cb1a32e06", "constructorArguments": ["******************************************", "******************************************", "******************************************"], "created": "2023-11-29T19:48:26.040Z"}, "limitPool": {"contractName": "LimitPool", "contractAddress": "0x4998545e13a668a884272aaebff14ab21c5b4e89", "constructorArguments": ["******************************************", "0xEfb283eF3167CA2eE9D93B201af15e2af3f6e8c7", "1000", 0], "created": "2023-11-25T12:45:02.690Z"}, "coverPoolFactory": {"contractName": "CoverPoolFactory", "contractAddress": "******************************************", "constructorArguments": ["******************************************"], "created": "2023-10-01T13:30:41.488Z"}, "tokenA": {"contractName": "Token20", "contractAddress": "******************************************", "constructorArguments": ["ChainLink Token", "LINK", 18], "created": "2023-10-20T14:15:25.994Z"}, "tokenB": {"contractName": "Token20", "contractAddress": "******************************************", "constructorArguments": ["Wrapped BTC", "WBTC", 8], "created": "2023-10-20T14:15:30.017Z"}, "weth9": {"contractName": "WETH9", "contractAddress": "******************************************", "constructorArguments": [], "created": "2023-11-25T12:32:49.206Z"}, "rangeStaker": {"contractName": "RangeStaker", "contractAddress": "******************************************", "constructorArguments": [{"limitPoolFactory": "******************************************", "startTime": 0, "endTime": 2000707154}], "created": "2023-11-28T22:19:18.570Z"}}, "scrollSepolia": {"token0": {"contractName": "Token20", "contractAddress": "******************************************", "constructorArguments": ["Wrapped Ether", "WETH", 18], "created": "2023-08-27T20:41:20.451Z"}, "token1": {"contractName": "Token20", "contractAddress": "******************************************", "constructorArguments": ["Dai Stablecoin", "DAI", 18], "created": "2023-08-27T20:41:20.460Z"}, "tickMapLib": {"contractName": "TickMap", "contractAddress": "******************************************", "constructorArguments": [], "created": "2023-09-27T23:07:01.909Z"}, "ticksLib": {"contractName": "Ticks", "contractAddress": "******************************************", "constructorArguments": [], "created": "2023-09-27T23:07:07.419Z"}, "rangePositionsLib": {"contractName": "RangePositions", "contractAddress": "******************************************", "constructorArguments": [], "created": "2023-09-27T23:07:16.463Z"}, "limitPositionsLib": {"contractName": "LimitPositions", "contractAddress": "******************************************", "constructorArguments": [], "created": "2023-09-27T23:07:22.458Z"}, "limitPoolManager": {"contractName": "LimitPoolManager", "contractAddress": "0x4e2f0a57ed373261451c1ef30215232be352020e", "constructorArguments": [], "created": "2023-09-27T23:07:27.645Z"}, "limitPoolFactory": {"contractName": "LimitPoolFactory", "contractAddress": "0x55feb5281c4097142870745462775b93c5eef412", "constructorArguments": ["0x4e2f0a57eD373261451C1ef30215232bE352020E"], "created": "2023-09-27T23:07:32.660Z"}, "swapCall": {"contractName": "SwapCall", "contractAddress": "0x139e58aca96cd776e3d8e3e20dfc30154c485b46", "constructorArguments": [], "created": "2023-09-27T23:07:37.787Z"}, "mintRangeCall": {"contractName": "MintRangeCall", "contractAddress": "0x358242aac3e75da72a4c091c091c80a84f115966", "constructorArguments": [], "created": "2023-09-27T23:07:46.994Z"}, "burnRangeCall": {"contractName": "BurnRangeCall", "contractAddress": "0x5f7d98a4443517c3928cb925d7202412b1e34886", "constructorArguments": [], "created": "2023-09-27T23:07:52.436Z"}, "mintLimitCall": {"contractName": "MintLimitCall", "contractAddress": "0xef8be53e21994fa411c1a6fb9cbc9c8ad6125f52", "constructorArguments": [], "created": "2023-09-27T23:08:01.808Z"}, "burnLimitCall": {"contractName": "BurnLimitCall", "contractAddress": "0x33df95efe07a3b3e69ba31438ae511d360d89b32", "constructorArguments": [], "created": "2023-09-27T23:08:11.042Z"}, "snapshotLimitCall": {"contractName": "SnapshotLimitCall", "contractAddress": "0x62e0671022af1b2e705f08b282767c57d29c7c4c", "constructorArguments": [], "created": "2023-09-27T23:08:20.200Z"}, "quoteCall": {"contractName": "QuoteCall", "contractAddress": "0xcd453b942f35adf0364d89c05a892518825c1c3b", "constructorArguments": [], "created": "2023-09-27T23:08:29.653Z"}, "feesCall": {"contractName": "FeesCall", "contractAddress": "0x61e80b7f57f3613ce30153c85d29ad571e7e9032", "constructorArguments": [], "created": "2023-09-27T23:08:38.692Z"}, "sampleCall": {"contractName": "SampleCall", "contractAddress": "0x5f6de12756b3e6df4db21bcc4855b4eb52a5d794", "constructorArguments": [], "created": "2023-09-27T23:08:47.875Z"}, "snapshotRangeCall": {"contractName": "SnapshotRangeCall", "contractAddress": "0xd47965e2357f60b4e2062df945afdcc655b5ce3a", "constructorArguments": [], "created": "2023-09-27T23:08:53.010Z"}, "limitPoolImpl": {"contractName": "LimitPool", "contractAddress": "0x0d4371c6d53e10986ba23929e02de11e60ba3b3c", "constructorArguments": ["0x55FEB5281C4097142870745462775B93C5EeF412"], "created": "2023-09-27T23:09:02.338Z"}, "positionERC1155": {"contractName": "PositionERC1155", "contractAddress": "0xed0ef8101900dfed208ac99ce47b723debb6bfe6", "constructorArguments": ["0x55FEB5281C4097142870745462775B93C5EeF412"], "created": "2023-09-27T23:09:11.427Z"}, "limitPool": {"contractName": "LimitPool", "contractAddress": "0xA3374d366C33E803A3fF4a3db4B38e5Aa4A1f2E5", "constructorArguments": ["0x434f4e5354414e542d50524f4455435400000000000000000000000000000000", "0x26323C7e4F000227932d3ed640EA79D6027701DE", "0x912c3e7d140e4EC85eb8e3910447ACDA8654D523", "500"], "created": "2023-09-27T23:09:50.038Z"}, "coverPoolFactory": {"contractName": "CoverPoolFactory", "contractAddress": "0x4b0288914ea8ab81da888e74f93da0062212ddcc", "constructorArguments": ["0xF76088F4C5Ad2a138f68870449f019e0E93E1510"], "created": "2023-09-25T01:59:46.159Z"}, "poolRouter": {"contractName": "PoolsharkRouter", "contractAddress": "0xf9aa2fb38bf96903b578cc98e6579b5d96d37b89", "constructorArguments": ["0x55feb5281c4097142870745462775b93c5eef412", "0x4b0288914ea8ab81da888e74f93da0062212ddcc"], "created": "2023-10-02T23:25:47.853Z"}}}