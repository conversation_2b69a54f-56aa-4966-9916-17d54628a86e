{"name": "poolshark-protocol", "scripts": {"format": "npx prettier --write .", "format:check": "npx prettier --check '*/**/*.{js,sol,json,md,ts}'", "lint:sol": "npx solhint 'contracts/**/*.sol'", "clean": "rm -rf ./artifacts ./cache ./typechain; npx hardhat clean", "compile": "npx hardhat compile", "test": "npx hardhat test", "deploy": "rm -rf ./artifacts ./cache ./typechain; npx hardhat clean; npx hardhat deploy-limitpools --network arb_goerli;", "verify": "npx hardhat verify-contracts", "all": "yarn clean; yarn compile; yarn test; yarn deploy;"}, "devDependencies": {"@nomicfoundation/hardhat-network-helpers": "1.0.8", "@nomiclabs/hardhat-ethers": "2.2.2", "@nomiclabs/hardhat-etherscan": "3.1.7", "@nomiclabs/hardhat-waffle": "2.0.3", "@nomiclabs/hardhat-web3": "2.0.0", "@openzeppelin/contracts": "4.7.3", "@typechain/ethers-v5": "7.2.0", "@typechain/hardhat": "2.3.1", "@types/chai": "4.3.3", "@types/mocha": "9.1.1", "@types/node": "12.20.55", "@typescript-eslint/eslint-plugin": "4.33.0", "@typescript-eslint/parser": "4.33.0", "chai": "4.3.6", "dotenv": "10.0.0", "eslint": "7.32.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "16.0.3", "eslint-plugin-import": "2.26.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.4.1", "eslint-plugin-promise": "5.2.0", "ethereum-waffle": "3.4.4", "ethers": "5.6.1", "hardhat": "2.12.6", "hardhat-contract-sizer": "2.6.1", "hardhat-gas-reporter": "1.0.9", "mythxjs": "1.3.13", "prettier": "2.7.1", "prettier-plugin-solidity": "1.0.0-beta.24", "solhint": "3.3.7", "solidity-coverage": "0.7.22", "ts-node": "10.9.1", "typechain": "5.2.0", "typescript": "4.8.3"}, "version": "1.0.0", "description": "", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}