[profile.default]
src = "contracts"
out = "out"
libs = ["lib"]
test = "test"
cache_path = "cache_forge"

# Solidity compiler settings
solc_version = "0.8.13"
optimizer = true
optimizer_runs = 200
via_ir = false

# EVM version
evm_version = "london"

# Remappings for dependencies
remappings = [
    "@openzeppelin/=lib/openzeppelin-contracts/",
    "ds-test/=lib/ds-test/src/",
    "forge-std/=lib/forge-std/src/"
]

# Gas reporting
gas_reports = ["*"]

# Fuzz testing
fuzz = { runs = 256 }

# RPC endpoints
[rpc_endpoints]
mainnet = "https://eth-mainnet.alchemyapi.io/v2/${ALCHEMY_API_KEY}"
goerli = "https://eth-goerli.alchemyapi.io/v2/${ALCHEMY_API_KEY}"
sepolia = "https://eth-sepolia.alchemyapi.io/v2/${ALCHEMY_API_KEY}"
arbitrum = "https://arb-mainnet.g.alchemy.com/v2/${ALCHEMY_API_KEY}"
arbitrum_goerli = "https://arb-goerli.g.alchemy.com/v2/${ALCHEMY_API_KEY}"
polygon = "https://polygon-mainnet.g.alchemy.com/v2/${ALCHEMY_API_KEY}"

# Etherscan API keys for verification
[etherscan]
mainnet = { key = "${ETHERSCAN_API_KEY}" }
goerli = { key = "${ETHERSCAN_API_KEY}" }
sepolia = { key = "${ETHERSCAN_API_KEY}" }
arbitrum = { key = "${ARBISCAN_API_KEY}" }
arbitrum_goerli = { key = "${ARBISCAN_API_KEY}" }
polygon = { key = "${POLYGONSCAN_API_KEY}" }
