/* Hardhat */
export const CLEAN_TEST_ARG = 'clean'
export const COMPILE_TEST_ARG = 'compile'
export const TYPECHAIN_TEST_ARG = 'typechain'
export const UNIT_TEST_ARG = 'test'
export const COVERAGE_TEST_ARG = 'coverage'
export const VERIFY_CONTRACTS = 'verify-contracts'

/* Deploy */
export const DEPLOY_LIMITPOOLS = 'deploy-limitpools'
export const DEPLOY_LIMITPOOL  = 'deploy-pool'
export const INCREASE_SAMPLES  = 'increase-samples'
export const ADD_VOLATILITY_TIER = 'add-tier'

/* Utils */
export const MINT_TOKENS = 'mint-tokens'
export const MINT_POSITION = 'mint-position'
