on: push

jobs:
    build:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@master
            - run: yarn install
            - run: npx hardhat compile
            # - run: npx hardhat test
            - run: npx hardhat coverage
            - uses: codecov/codecov-action@v3
              with:
                  token: ${{ secrets.CODECOV_TOKEN }} # not required for public repos
                  # flags: contracttests # optional
                  name: limit-contract-tests # optional
                  fail_ci_if_error: true # optional (default = false)
                  verbose: true # optional (default = false)
