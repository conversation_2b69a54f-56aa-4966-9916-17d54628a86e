/* eslint-disable */
import { BigInt, BigDecimal, Address } from '@graphprotocol/graph-ts'
import { LimitPoolFactory as FactoryContract } from '../../generated/templates/LimitPoolTemplate/LimitPoolFactory'
export let FACTORY_ADDRESS = '******************************************'
export let RANGE_STAKER_ADDRESS = '******************************************'
export let WETH_ADDRESS = '******************************************'

// tokens where USD value is safe to use for globals
export let WHITELISTED_TOKENS: string[] = [
  '******************************************', // WETH
  '******************************************', // DAI
  '******************************************' // USDC
]

export let WHITELISTED_PAIRS: string[] = [
  '******************************************-******************************************' // WETH - USDC
]

export let SEASON_1_START_TIME = BigInt.fromString('0')
export let SEASON_1_END_TIME = BigInt.fromString('2000707154')

// used for safe eth pricing 
export let STABLE_COINS: string[] = [
  '******************************************', //DAI
  '******************************************' //USDC
]

// used for safe eth pricing 
export const STABLE_POOL_ADDRESS = '******************************************'

// determines which token to use for eth <-> usd rate, true means stable is token0 in pool above 
export const STABLE_IS_TOKEN_0 = true

// minimum eth required in pool to count usd values towards global prices 
export let MINIMUM_ETH_LOCKED = BigDecimal.fromString('0')

// pool that breaks with subgraph logic 
export const ERROR_POOL = '******************************************'

export let ZERO_ADDRESS = '******************************************'

export let ZERO_BI = BigInt.fromI32(0)
export let ONE_BI = BigInt.fromI32(1)
export let ZERO_BD = BigDecimal.fromString('0')
export let ONE_BD = BigDecimal.fromString('1')
export let TWO_BD = BigDecimal.fromString('2')
export let BI_18 = BigInt.fromI32(18)

export let factoryContract = FactoryContract.bind(Address.fromString(FACTORY_ADDRESS))