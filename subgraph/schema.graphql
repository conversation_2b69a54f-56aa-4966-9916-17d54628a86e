type LimitPoolFactory @entity {
    # poolAddress
    id: ID!
    manager: LimitPoolManager!
    poolCount: BigInt!
    txnCount: BigInt!
    volumeUsdTotal: BigDecimal!
    volumeEthTotal: BigDecimal!
    feesUsdTotal: BigDecimal!
    feesEthTotal: BigDecimal!
    totalValueLockedUsd: BigDecimal!
    totalValueLockedEth: BigDecimal!
}

type LimitPoolManager @entity {
    id: ID!
    owner: Bytes!
    feeTo: Bytes!
    factory: LimitPoolFactory!
    feeTiers: [FeeTier!]!
    poolTypes: [LimitPoolType!]!
}

type BasePrice @entity {
    id: ID!
    USD: BigDecimal!
}

type FeeTier @entity {
    # fee amount + tick spread + twap length + auction length
    id: ID!
    feeAmount: BigInt!
    tickSpacing: BigInt!

    # creation stats
    createdAtTimestamp: BigInt!
    createdAtBlockNumber: BigInt!
}

type Token @entity {
  # token address
  id: ID!

  # mirrored from the smart contract
  symbol: String!
  name: String!
  decimals: BigInt!

  # price
  ethPrice: BigDecimal!
  usdPrice: BigDecimal!
  
  pools: [LimitPool!]!

  # token specific volume
  volume: BigDecimal!
  volumeUsd: BigDecimal!
  volumeEth: BigDecimal!
  txnCount: BigInt!

  feesUsdTotal: BigDecimal!
  feesEthTotal: BigDecimal!

  # liquidity across all pairs
  totalValueLocked: BigDecimal!
  totalValueLockedEth: BigDecimal!
  totalValueLockedUsd: BigDecimal!

  # for usd price tracking
  whitelistPools: [LimitPool!]!
}

type LimitPool @entity {
    # poolAddress
    id: ID!

    # pool tokens
    token0: Token!
    token1: Token!

    # pool setup
    feeTier: FeeTier!
    swapFee: BigInt!
    tickSpacing: BigInt!
    factory: LimitPoolFactory!
    poolType: String!
    poolToken: Bytes!

    liquidity: BigInt!
    liquidityGlobal: BigInt!
    positionIdNext: BigInt!
    epoch: BigInt!
    poolPrice: BigInt!
    pool0Price: BigInt!
    pool1Price: BigInt!
    poolLiquidity: BigInt!
    pool0Liquidity: BigInt!
    pool1Liquidity: BigInt!
    tickAtPrice: BigInt!

    # fee growth
    feeGrowthGlobal0: BigInt!
    feeGrowthGlobal1: BigInt!

    # samples
    samplesLength: BigInt!

    # price and volume states
    price0: BigDecimal!
    price1: BigDecimal!
    volumeToken0: BigDecimal!
    volumeToken1: BigDecimal!
    volumeUsd: BigDecimal!
    volumeEth: BigDecimal!
    feesUsd: BigDecimal!
    feesEth: BigDecimal!
    txnCount: BigInt!

    # tvl stats
    totalValueLocked0: BigDecimal!
    totalValueLocked1: BigDecimal!
    totalValueLockedUsd: BigDecimal!
    totalValueLockedEth: BigDecimal!

    # creation stats
    createdAtTimestamp: BigInt!
    createdAtBlockNumber: BigInt!
    updatedAtTimestamp: BigInt!
    updatedAtBlockNumber: BigInt!
}

type LimitPoolToken @entity {
    # poolTokenAddress
    id: ID!

    pool: LimitPool!
}

type LimitPosition @entity {
    # poolAddress + owner + lower + upper
    id: ID!
    positionId: BigInt!
    owner: Bytes!
    lower: BigInt!
    upper: BigInt!
    zeroForOne: Boolean!
    liquidity: BigInt!
    pool: LimitPool!
    epochLast: BigInt!
    claimPriceLast: BigInt!
    amountIn: BigInt!
    amountFilled: BigInt!
    tokenIn: Token!
    tokenOut: Token!

    txnHash: Bytes!
    createdBy: Bytes!
    createdAtTimestamp: BigInt!
}

type RangePosition @entity {
    id: ID!

    # position data
    owner: Bytes!
    positionId: BigInt!
    lower: BigInt!
    upper: BigInt!
    liquidity: BigInt!
    pool: LimitPool!
    staked: Boolean!

    # creation stats
    createdAtTimestamp: BigInt!
    createdAtBlockNumber: BigInt!
    updatedAtTimestamp: BigInt!
    updatedAtBlockNumber: BigInt!
}

type Swap @entity {
    # txn hash + '-' + indexed count
    id: ID!
    transaction: Transaction!
    recipient: Bytes!
    timestamp: BigInt!
    pool: LimitPool!
    zeroForOne: Boolean!
    amount0: BigDecimal!
    amount1: BigDecimal!
    amountUsd: BigDecimal!
    priceAfter: BigInt!
    tickAfter: BigInt!
    txnIndex: BigInt
}

type Transaction @entity {
    # txn hash
    id: ID!
    sender: Bytes!
    blockNumber: BigInt!
    timestamp: BigInt!
    gasLimit: BigInt!
    gasPrice: BigInt!
    swaps: [Swap!]! @derivedFrom(field: "transaction")
}

type LimitPoolType @entity {
    # poolTypeId
    id: ID!

    name: String!
    poolImpl: Bytes!
    tokenImpl: Bytes!
}

type RangeTick @entity {
    # poolAddress + index
    id: ID!
    pool: LimitPool!
    index: BigInt!
    price0: BigDecimal!
    price1: BigDecimal!
    
    liquidityDelta: BigInt!
    liquidityAbsolute: BigInt!

    # fee growth
    feeGrowthOutside0: BigInt!
    feeGrowthOutside1: BigInt!
}

type LimitTick @entity {
    # poolAddress + index
    id: ID!
    pool: LimitPool!
    index: BigInt!
    # whether the tick is active in the tickMap
    active: Boolean!
    price0: BigDecimal!
    price1: BigDecimal!

    liquidityDelta: BigInt!
    liquidityAbsolute: BigInt!

    # epoch last
    epochLast0: BigInt!
    epochLast1: BigInt!
}

type PoolRouter @entity {
    id: ID!

    limitPoolFactory: Bytes!
    coverPoolFactory: Bytes!
}

type TvlUpdateLog @entity {
    id: ID!
    pool: LimitPool!
    eventName: String!
    txnHash: Bytes!
    txnBlockNumber: BigInt!
    amount0Change: BigDecimal!
    amount1Change: BigDecimal!
    amount0Total: BigDecimal!
    amount1Total: BigDecimal!
    token0UsdPrice: BigDecimal!
    token1UsdPrice: BigDecimal!
    amountUsdChange: BigDecimal!
    amountUsdTotal: BigDecimal!
}

type MintLimitLog @entity {
    id: ID!

    sender: Bytes!
    recipient: Bytes!
    lower: BigInt!
    upper: BigInt!
    positionId: BigInt!
    liquidityMinted: BigInt!
    pool: LimitPool!
}

type BurnLimitLog @entity {
    id: ID!

    owner: Bytes!
    recipient: Bytes!
    lower: BigInt!
    upper: BigInt!
    positionId: BigInt!
    liquidityBurned: BigInt!
    pool: LimitPool!
}

type MintRangeLog @entity {
    id: ID!

    sender: Bytes!
    recipient: Bytes!
    lower: BigInt!
    upper: BigInt!
    positionId: BigInt!
    liquidityMinted: BigInt!
    pool: LimitPool!
}

type BurnRangeLog @entity {
    id: ID!

    owner: Bytes!
    recipient: Bytes!
    lower: BigInt!
    upper: BigInt!
    positionId: BigInt!
    liquidityBurned: BigInt!
    pool: LimitPool!
}

type CompoundRangeLog @entity {
    id: ID!

    sender: Bytes!
    pool: LimitPool!
    liquidityCompounded: BigInt!
    positionId: BigInt!
}

type HistoricalOrder @entity {
    # poolAddress + zeroForOne + txnHash + owner
    id: ID!

    owner: Bytes!

    pool: LimitPool!
    tokenIn: Token!
    tokenOut: Token!
    txnHash: Bytes!

    amountIn: BigDecimal!
    amountOut: BigDecimal!
    averagePrice: BigDecimal!
    completedAtTimestamp: BigInt!

    usdValue: BigDecimal!

    positionId: BigInt!
    touches: BigInt!

    completed: Boolean!
}

type TotalSeasonReward @entity {
    # factoryAddress
    id: ID!

    whitelistedFeesUsd: BigDecimal!
    nonWhitelistedFeesUsd: BigDecimal!
    volumeTradedUsd: BigDecimal!
    stakingPoints: BigInt!
}

type UserSeasonReward @entity {
    # userAddress
    id: ID!

    whitelistedFeesUsd: BigDecimal!
    nonWhitelistedFeesUsd: BigDecimal!
    volumeTradedUsd: BigDecimal!
    stakingPoints: BigInt!
}

