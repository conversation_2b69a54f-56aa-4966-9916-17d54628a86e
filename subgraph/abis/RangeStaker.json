[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousFeeTo", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newFeeTo", "type": "address"}], "name": "FeeToTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnerTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "pool", "type": "address"}, {"indexed": false, "internalType": "uint32", "name": "positionId", "type": "uint32"}, {"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "feeGrowthInside0Last", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "feeGrowthInside1Last", "type": "uint256"}, {"indexed": false, "internalType": "uint128", "name": "liquidity", "type": "uint128"}], "name": "StakeRange", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "pool", "type": "address"}, {"indexed": false, "internalType": "uint32", "name": "positionId", "type": "uint32"}, {"indexed": false, "internalType": "uint256", "name": "feeGrowth0Accrued", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "feeGrowth1Accrued", "type": "uint256"}], "name": "StakeRangeAccrued", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "pool", "type": "address"}, {"indexed": false, "internalType": "uint32", "name": "positionId", "type": "uint32"}, {"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}], "name": "UnstakeRange", "type": "event"}]