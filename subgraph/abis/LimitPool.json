[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint32", "name": "positionId", "type": "uint32"}, {"indexed": false, "internalType": "int24", "name": "lower", "type": "int24"}, {"indexed": false, "internalType": "int24", "name": "upper", "type": "int24"}, {"indexed": false, "internalType": "int24", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "int24"}, {"indexed": false, "internalType": "int24", "name": "newClaim", "type": "int24"}, {"indexed": false, "internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"indexed": false, "internalType": "uint128", "name": "liquidityBurned", "type": "uint128"}, {"indexed": false, "internalType": "uint128", "name": "tokenInClaimed", "type": "uint128"}, {"indexed": false, "internalType": "uint128", "name": "tokenOutBurned", "type": "uint128"}], "name": "BurnLimit", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "positionId", "type": "uint256"}, {"indexed": false, "internalType": "uint128", "name": "liquidityBurned", "type": "uint128"}, {"indexed": false, "internalType": "int128", "name": "amount0", "type": "int128"}, {"indexed": false, "internalType": "int128", "name": "amount1", "type": "int128"}], "name": "BurnRange", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint128", "name": "amount0", "type": "uint128"}], "name": "CollectRange0", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint128", "name": "amount1", "type": "uint128"}], "name": "CollectRange1", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint32", "name": "positionId", "type": "uint32"}, {"indexed": false, "internalType": "uint128", "name": "liquidityCompounded", "type": "uint128"}], "name": "CompoundRange", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "int24", "name": "minTick", "type": "int24"}, {"indexed": false, "internalType": "int24", "name": "maxTick", "type": "int24"}, {"indexed": false, "internalType": "uint160", "name": "startPrice", "type": "uint160"}, {"indexed": false, "internalType": "int24", "name": "startTick", "type": "int24"}], "name": "Initialize", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "int24", "name": "lower", "type": "int24"}, {"indexed": false, "internalType": "int24", "name": "upper", "type": "int24"}, {"indexed": false, "internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"indexed": false, "internalType": "uint32", "name": "positionId", "type": "uint32"}, {"indexed": false, "internalType": "uint32", "name": "epochLast", "type": "uint32"}, {"indexed": false, "internalType": "uint128", "name": "amountIn", "type": "uint128"}, {"indexed": false, "internalType": "uint128", "name": "liquidityMinted", "type": "uint128"}], "name": "MintLimit", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "int24", "name": "lower", "type": "int24"}, {"indexed": false, "internalType": "int24", "name": "upper", "type": "int24"}, {"indexed": true, "internalType": "uint32", "name": "positionId", "type": "uint32"}, {"indexed": false, "internalType": "uint128", "name": "liquidityMinted", "type": "uint128"}, {"indexed": false, "internalType": "int128", "name": "amount0Delta", "type": "int128"}, {"indexed": false, "internalType": "int128", "name": "amount1Delta", "type": "int128"}], "name": "MintRange", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint16", "name": "newSampleCountMax", "type": "uint16"}], "name": "SampleCountIncreased", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "int56", "name": "tickSecondsAccum", "type": "int56"}, {"indexed": false, "internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160"}], "name": "SampleRecorded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amountOut", "type": "uint256"}, {"indexed": false, "internalType": "uint200", "name": "feeGrowthGlobal0", "type": "uint200"}, {"indexed": false, "internalType": "uint200", "name": "feeGrowthGlobal1", "type": "uint200"}, {"indexed": false, "internalType": "uint160", "name": "price", "type": "uint160"}, {"indexed": false, "internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"indexed": false, "internalType": "uint128", "name": "feeAmount", "type": "uint128"}, {"indexed": false, "internalType": "int24", "name": "tickAtPrice", "type": "int24"}, {"indexed": true, "internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"indexed": true, "internalType": "bool", "name": "exactIn", "type": "bool"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint128", "name": "liquidityAdded", "type": "uint128"}, {"indexed": false, "internalType": "int24", "name": "tick", "type": "int24"}, {"indexed": false, "internalType": "bool", "name": "zeroForOne", "type": "bool"}], "name": "SyncLimitLiquidity", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint160", "name": "price", "type": "uint160"}, {"indexed": false, "internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"indexed": false, "internalType": "uint32", "name": "epoch", "type": "uint32"}, {"indexed": false, "internalType": "int24", "name": "tickAtPrice", "type": "int24"}, {"indexed": false, "internalType": "bool", "name": "isPool0", "type": "bool"}], "name": "SyncLimitPool", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint32", "name": "epoch", "type": "uint32"}, {"indexed": false, "internalType": "int24", "name": "tick", "type": "int24"}, {"indexed": false, "internalType": "bool", "name": "zeroForOne", "type": "bool"}], "name": "SyncLimitTick", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint200", "name": "feeGrowthOutside0", "type": "uint200"}, {"indexed": false, "internalType": "uint200", "name": "feeGrowthOutside1", "type": "uint200"}, {"indexed": false, "internalType": "int24", "name": "tick", "type": "int24"}], "name": "SyncRangeTick", "type": "event"}]