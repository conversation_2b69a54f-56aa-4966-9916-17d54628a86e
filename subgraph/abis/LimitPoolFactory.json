[{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "pool", "type": "address"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "token0", "type": "address"}, {"indexed": true, "internalType": "address", "name": "token1", "type": "address"}, {"indexed": true, "internalType": "uint16", "name": "swapFee", "type": "uint16"}, {"indexed": false, "internalType": "int16", "name": "tickSpacing", "type": "int16"}, {"indexed": false, "internalType": "uint16", "name": "poolTypeId", "type": "uint16"}], "name": "PoolCreated", "type": "event"}]