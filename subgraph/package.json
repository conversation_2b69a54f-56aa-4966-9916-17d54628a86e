{"name": "poolsharkhedgepool-subgraph", "version": "0.0.1", "repository": "https://github.com/poolshark-protocol/limit", "license": "MIT", "prettier": {"trailingComma": "es5", "tabWidth": 4, "semi": false, "singleQuote": true}, "scripts": {"studio-auth": "graph auth  --studio", "auth": "graph auth --product hosted-service ", "create": "graph create example --node https://api.thegraph.com/deploy/", "create-local": "graph create example --node http://127.0.0.1:3001", "codegen": "graph codegen", "build": "graph build", "deploy": "graph deploy --product hosted-service alphak3y/poolshark-limit", "deploy-sats": "graph deploy limit-arbitrumGoerli --version-label v0.1.0 --node https://app.satsuma.xyz/api/subgraphs/deploy --deploy-key 7NoUUXPcOGfBX --ipfs https://ipfs.satsuma.xyz", "deploy-staging": "graph deploy --version-label v0.3.1 --node https://api.graph-eu.p2pify.com/cc955503f93d46512a78fb9a70796dac/deploy --ipfs https://api.graph-eu.p2pify.com/cc955503f93d46512a78fb9a70796dac/ipfs staging-limit-arbitrumGoerli", "deploy-beta2": "graph deploy --version-label v0.4.0 --node https://api.graph-ams.p2pify.com/3bd8a0adf573b40768f68ef92c7bd843/deploy --ipfs https://api.graph-ams.p2pify.com/3bd8a0adf573b40768f68ef92c7bd843/ipfs limit-arbitrumGoerli-beta2", "deploy-test": "graph deploy --version-label v0.3.0 --node https://api.graph-ams.p2pify.com/abafff8142f8181262d18b7dfeac1236/deploy --ipfs https://api.graph-ams.p2pify.com/abafff8142f8181262d18b7dfeac1236/ipfs limit-arbitrumGoerli-test", "deploy-ordertest": "graph deploy --version-label v0.1.0 --node https://api.graph-ams.p2pify.com/719c33840eec545dba4ed49d362448f0/deploy --ipfs https://api.graph-ams.p2pify.com/719c33840eec545dba4ed49d362448f0/ipfs limit-arbitrumGoerli-order-testing", "deploy-chainstack": "graph deploy --node https://api.graph-eu.p2pify.com/cc955503f93d46512a78fb9a70796dac/deploy --ipfs https://api.graph-eu.p2pify.com/cc955503f93d46512a78fb9a70796dac/ipfs staging-limit-arbitrumGoerli", "deploy-op-test": "graph deploy --product hosted-service alphak3y/poolshark-cover-op-goerli", "deploy-hs": "graph deploy --product hosted-service alphak3y/poolshark-limit", "deploy-local": "graph deploy example --ipfs http://localhost:5001 --node http://127.0.0.1:8020", "test": "graph codegen; graph test -v 0.2.0"}, "devDependencies": {"@graphprotocol/graph-cli": "0.25.1", "@graphprotocol/graph-ts": "0.24.1", "eslint": "7.32.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "16.0.3", "eslint-plugin-prettier": "3.4.1", "libpq": "1.8.12", "matchstick-as": "0.2.0"}, "dependencies": {"assemblyscript": "0.20.19", "assemblyscript-json": "1.1.0", "json-as": "0.2.6", "node-gyp": "9.1.0", "source-map-support": "0.5.21"}}