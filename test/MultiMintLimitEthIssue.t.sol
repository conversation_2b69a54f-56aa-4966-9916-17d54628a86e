// SPDX-License-Identifier: MIT
pragma solidity 0.8.13;

import 'forge-std/Test.sol';
import '../contracts/utils/PoolsharkRouter.sol';
import '../contracts/test/WETH9.sol';
import '../contracts/test/Token20.sol';
import '../contracts/LimitPoolFactory.sol';
import '../contracts/LimitPool.sol';

contract MultiMintLimitEthIssueTest is Test {
    PoolsharkRouter router;
    WETH9 weth;
    Token20 token0;
    Token20 token1;
    LimitPoolFactory factory;
    address pool1;
    address pool2;

    address alice = makeAddr('alice');
    address bob = makeAddr('bob');

    uint256 constant INITIAL_ETH = 10 ether;
    uint256 constant MINT_AMOUNT = 1 ether;

    function setUp() public {
        // Deploy WETH
        weth = new WETH9();

        // Deploy tokens
        token0 = new Token20('Token0', 'TK0', 18);
        token1 = new Token20('Token1', 'TK1', 18);

        // Ensure WETH has lower address than other tokens for consistent ordering
        if (address(weth) > address(token0)) {
            Token20 temp = token0;
            token0 = Token20(address(weth));
            weth = WETH9(payable(address(temp)));
        }

        // Deploy factory (simplified - would need proper implementation)
        factory = new LimitPoolFactory(alice); // owner parameter

        // Deploy router
        router = new PoolsharkRouter(
            address(weth),
            address(factory),
            address(0) // coverPoolFactory - not needed for this test
        );

        // Create two pools: WETH/Token0 and WETH/Token1
        // Note: In real implementation, you'd use factory.createLimitPool()
        // For this test, we'll mock the pool addresses
        pool1 = makeAddr('pool1');
        pool2 = makeAddr('pool2');

        // Give Alice some ETH
        vm.deal(alice, INITIAL_ETH);

        // Give Alice some tokens
        token0.mint(alice, 1000 ether);
        token1.mint(alice, 1000 ether);

        vm.startPrank(alice);
        token0.approve(address(router), type(uint256).max);
        token1.approve(address(router), type(uint256).max);
        vm.stopPrank();
    }

    function testMultiMintLimitEthIssue() public {
        // This test demonstrates the issue with msg.value being used in a loop

        vm.startPrank(alice);

        // Prepare parameters for minting in two different pools
        address[] memory pools = new address[](2);
        pools[0] = pool1;
        pools[1] = pool2;

        PoolsharkStructs.MintLimitParams[] memory params = new PoolsharkStructs.MintLimitParams[](2);

        // First mint: WETH/Token0 pool - should use 1 ETH
        params[0] = PoolsharkStructs.MintLimitParams({
            to: alice,
            amount: uint128(MINT_AMOUNT),
            mintPercent: 1e6, // 100%
            positionId: 0,
            lower: -887220,
            upper: 887220,
            zeroForOne: true,
            callbackData: ''
        });

        // Second mint: WETH/Token1 pool - should use 1 ETH
        params[1] = PoolsharkStructs.MintLimitParams({
            to: alice,
            amount: uint128(MINT_AMOUNT),
            mintPercent: 1e6, // 100%
            positionId: 0,
            lower: -887220,
            upper: 887220,
            zeroForOne: true,
            callbackData: ''
        });

        // The issue: Alice sends 2 ETH but both pools will think they can use all 2 ETH
        // because msg.value > 0 is checked in the loop, not the actual available balance

        uint256 ethSent = 2 ether;
        uint256 aliceBalanceBefore = alice.balance;

        // This call should work correctly, but the logic is flawed
        // Each iteration sets wrapped: msg.value > 0, which is true for both iterations
        // However, the first pool might consume more ETH than intended

        // Mock the pool calls to demonstrate the issue
        vm.mockCall(pool1, abi.encodeWithSelector(ILimitPool.mintLimit.selector), '');
        vm.mockCall(pool2, abi.encodeWithSelector(ILimitPool.mintLimit.selector), '');

        // Call multiMintLimit with 2 ETH
        router.multiMintLimit{value: ethSent}(pools, params);

        vm.stopPrank();

        // The issue is in the callback data preparation:
        // Both pools get wrapped: true because msg.value > 0
        // But there's no tracking of how much ETH has been consumed

        console.log('ETH sent:', ethSent);
        console.log('Alice balance before:', aliceBalanceBefore);
        console.log('Alice balance after:', alice.balance);
        console.log('Router balance:', address(router).balance);
    }

    function testDemonstrateTheActualIssue() public {
        // This test shows the actual vulnerability more clearly

        // The issue is that in multiMintLimit, each iteration does:
        // wrapped: msg.value > 0
        //
        // This means if user sends 1 ETH for 3 pools, all 3 pools will have wrapped=true
        // But only the first pool that needs WETH will actually get the ETH
        // The subsequent pools will fail when trying to wrap ETH that's no longer available

        vm.startPrank(alice);

        address[] memory pools = new address[](3);
        pools[0] = makeAddr('wethPool1');
        pools[1] = makeAddr('wethPool2');
        pools[2] = makeAddr('wethPool3');

        PoolsharkStructs.MintLimitParams[] memory params = new PoolsharkStructs.MintLimitParams[](3);

        for (uint i = 0; i < 3; i++) {
            params[i] = PoolsharkStructs.MintLimitParams({
                to: alice,
                amount: uint128(MINT_AMOUNT),
                mintPercent: 1e6,
                positionId: 0,
                lower: -887220,
                upper: 887220,
                zeroForOne: true,
                callbackData: ''
            });
        }

        // User sends only 1 ETH but expects to mint in 3 pools
        // All 3 pools will get wrapped=true, but only 1 ETH is available
        uint256 insufficientEth = 1 ether;

        // Mock successful calls - in reality, the 2nd and 3rd would fail
        // when the callback tries to wrap ETH that's not available
        for (uint i = 0; i < 3; i++) {
            vm.mockCall(pools[i], abi.encodeWithSelector(ILimitPool.mintLimit.selector), '');
        }

        // This demonstrates the flaw: all pools think they can use ETH
        router.multiMintLimit{value: insufficientEth}(pools, params);

        vm.stopPrank();

        // In a real scenario without mocking, this would fail on the 2nd or 3rd pool
        // when wrapEth() is called but insufficient ETH balance exists
    }

    function testCorrectBehaviorShouldBe() public {
        // This test shows what the correct behavior should be

        console.log('\n=== VULNERABILITY ANALYSIS ===');
        console.log('Current flawed logic in multiMintLimit:');
        console.log('for (uint i = 0; i < pools.length;) {');
        console.log('    params[i].callbackData = abi.encode(MintLimitCallbackData({');
        console.log('        sender: msg.sender,');
        console.log('        wrapped: msg.value > 0  // <-- ISSUE: Always true if any ETH sent');
        console.log('    }));');
        console.log('    ILimitPool(pools[i]).mintLimit(params[i]);');
        console.log('}');
        console.log('');
        console.log('PROBLEM:');
        console.log('- If user sends 1 ETH for 3 pools, ALL pools get wrapped=true');
        console.log('- First pool consumes the 1 ETH via wrapEth()');
        console.log('- Second and third pools fail when trying to wrap non-existent ETH');
        console.log('');
        console.log('CORRECT LOGIC SHOULD BE:');
        console.log('- Track remaining ETH balance');
        console.log('- Only set wrapped=true for pools that actually need ETH');
        console.log('- Or distribute ETH proportionally among pools');

        assertTrue(true, 'This test documents the vulnerability');
    }
}
