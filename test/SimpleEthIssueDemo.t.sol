// SPDX-License-Identifier: MIT
pragma solidity 0.8.13;

import 'forge-std/Test.sol';

contract EthHandlingVulnerabilityTest is Test {
    function testMsgValueRemainsConstantInLoop() public {
        // Demonstrate that msg.value doesn't change during execution
        uint256 initialValue = 1 ether;

        // Simulate the flawed logic
        bool[] memory wrappedFlags = new bool[](3);

        // This is what happens in multiMintLimit - msg.value check in loop
        for (uint i = 0; i < 3; i++) {
            wrappedFlags[i] = initialValue > 0; // This simulates msg.value > 0
        }

        // All flags are true, even though ETH would be consumed
        assertTrue(wrappedFlags[0], 'First pool gets wrapped=true');
        assertTrue(wrappedFlags[1], 'Second pool gets wrapped=true');
        assertTrue(wrappedFlags[2], 'Third pool gets wrapped=true');

        console.log('Issue: All pools get wrapped=true regardless of available ETH');
    }

    function testEthConsumptionScenario() public {
        uint256 totalEth = 1 ether;
        uint256 ethPerPool = 0.5 ether;
        uint256 poolCount = 3;

        // Simulate ETH consumption
        uint256 remainingEth = totalEth;
        bool[] memory shouldWrap = new bool[](poolCount);

        for (uint i = 0; i < poolCount; i++) {
            // Current flawed logic: wrapped = msg.value > 0 (always true)
            bool flawedLogic = totalEth > 0;

            // Correct logic: check if enough ETH remains
            bool correctLogic = remainingEth >= ethPerPool;

            shouldWrap[i] = flawedLogic;

            if (correctLogic) {
                remainingEth -= ethPerPool;
            }
        }

        // Flawed logic: all pools think they can use ETH
        assertTrue(shouldWrap[0] && shouldWrap[1] && shouldWrap[2], 'Flawed: all pools get wrapped=true');

        // Reality: only first pool has enough ETH
        assertEq(remainingEth, 0, 'All ETH consumed by first pool');

        console.log('Remaining ETH after first pool:', remainingEth);
        console.log('Second and third pools will fail when trying to wrap ETH');
    }

    function testAffectedFunctions() public {
        // Document all functions with the same vulnerability
        string[4] memory affectedFunctions = [
            'multiMintLimit()',
            'multiMintRange()',
            'multiMintCover()',
            'createLimitPoolAndMint()'
        ];

        for (uint i = 0; i < affectedFunctions.length; i++) {
            console.log('Vulnerable function:', affectedFunctions[i]);
        }

        assertTrue(true, "All functions use flawed 'wrapped: msg.value > 0' logic");
    }
}
