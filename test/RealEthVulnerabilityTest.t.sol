// SPDX-License-Identifier: MIT
pragma solidity 0.8.13;

import 'forge-std/Test.sol';
import '../contracts/utils/PoolsharkRouter.sol';
import '../contracts/test/WETH9.sol';
import '../contracts/test/Token20.sol';
import '../contracts/LimitPoolFactory.sol';
import '../contracts/LimitPool.sol';

contract RealEthVulnerabilityTest is Test {
    PoolsharkRouter router;
    WETH9 weth;
    Token20 token0;
    Token20 token1;
    LimitPoolFactory factory;
    LimitPool pool1;
    LimitPool pool2;

    address alice = makeAddr('alice');

    function setUp() public {
        // Deploy WETH
        weth = new WETH9();

        // Deploy tokens
        token0 = new Token20('Token0', 'TK0', 18);
        token1 = new Token20('Token1', 'TK1', 18);

        // Deploy factory
        factory = new LimitPoolFactory(alice);

        // Deploy router
        router = new PoolsharkRouter(
            address(weth),
            address(factory),
            address(0) // coverPoolFactory
        );

        // Create real pools
        vm.startPrank(alice);

        // Ensure WETH is token0 for both pools by using lower address
        address wethAddr = address(weth);
        address token0Addr = address(token0);
        address token1Addr = address(token1);

        // Create pool1: WETH/Token0
        (address poolAddr1, ) = factory.createLimitPool(
            PoolsharkStructs.LimitPoolParams({
                tokenIn: wethAddr < token0Addr ? wethAddr : token0Addr,
                tokenOut: wethAddr < token0Addr ? token0Addr : wethAddr,
                startPrice: 79228162514264337593543950336, // sqrt(1) * 2^96
                swapFee: 3000,
                poolTypeId: 0 // Default pool type
            })
        );
        pool1 = LimitPool(poolAddr1);

        // Create pool2: WETH/Token1
        (address poolAddr2, ) = factory.createLimitPool(
            PoolsharkStructs.LimitPoolParams({
                tokenIn: wethAddr < token1Addr ? wethAddr : token1Addr,
                tokenOut: wethAddr < token1Addr ? token1Addr : wethAddr,
                startPrice: 79228162514264337593543950336, // sqrt(1) * 2^96
                swapFee: 3000,
                poolTypeId: 0 // Default pool type
            })
        );
        pool2 = LimitPool(poolAddr2);

        vm.stopPrank();

        // Setup Alice with funds
        vm.deal(alice, 100 ether);
        token0.mint(alice, 1000 ether);
        token1.mint(alice, 1000 ether);

        vm.startPrank(alice);
        token0.approve(address(router), type(uint256).max);
        token1.approve(address(router), type(uint256).max);
        vm.stopPrank();
    }

    function testMultiMintLimitWithInsufficientEth() public {
        vm.startPrank(alice);

        // Prepare parameters for 2 pools, each expecting to use ETH
        address[] memory pools = new address[](2);
        pools[0] = address(pool1);
        pools[1] = address(pool2);

        PoolsharkStructs.MintLimitParams[] memory params = new PoolsharkStructs.MintLimitParams[](2);

        // Both mints will try to use WETH (zeroForOne = true means selling token0 for token1)
        // If WETH is token0, this means we're selling WETH, so we need ETH to wrap
        bool wethIsToken0Pool1 = address(weth) < address(token0);
        bool wethIsToken0Pool2 = address(weth) < address(token1);

        params[0] = PoolsharkStructs.MintLimitParams({
            to: alice,
            amount: uint128(1 ether),
            mintPercent: 1e6, // 100%
            positionId: 0,
            lower: -887220, // Full range
            upper: 887220,
            zeroForOne: wethIsToken0Pool1, // Sell WETH if it's token0
            callbackData: '' // Will be overwritten by router
        });

        params[1] = PoolsharkStructs.MintLimitParams({
            to: alice,
            amount: uint128(1 ether),
            mintPercent: 1e6, // 100%
            positionId: 0,
            lower: -887220, // Full range
            upper: 887220,
            zeroForOne: wethIsToken0Pool2, // Sell WETH if it's token0
            callbackData: '' // Will be overwritten by router
        });

        uint256 aliceEthBefore = alice.balance;
        uint256 routerEthBefore = address(router).balance;

        console.log('Alice ETH before:', aliceEthBefore);
        console.log('Router ETH before:', routerEthBefore);

        // Send only 1 ETH but try to mint in 2 pools that both need ETH
        // This should demonstrate the vulnerability
        uint256 ethSent = 1 ether;

        // The vulnerability: both pools will get wrapped=true because msg.value > 0
        // But only 1 ETH is available, so the second pool should fail

        try router.multiMintLimit{value: ethSent}(pools, params) {
            console.log('Transaction succeeded - this might indicate the vulnerability');
        } catch Error(string memory reason) {
            console.log('Transaction failed with reason:', reason);
        } catch (bytes memory lowLevelData) {
            console.log('Transaction failed with low level error');
            console.logBytes(lowLevelData);
        }

        uint256 aliceEthAfter = alice.balance;
        uint256 routerEthAfter = address(router).balance;

        console.log('Alice ETH after:', aliceEthAfter);
        console.log('Router ETH after:', routerEthAfter);
        console.log('ETH consumed:', aliceEthBefore - aliceEthAfter);

        vm.stopPrank();
    }

    function testDemonstrateCallbackDataIssue() public {
        // This test shows that both pools get wrapped=true even with insufficient ETH

        vm.startPrank(alice);

        address[] memory pools = new address[](3);
        pools[0] = address(pool1);
        pools[1] = address(pool2);
        pools[2] = address(pool1); // Same pool again

        PoolsharkStructs.MintLimitParams[] memory params = new PoolsharkStructs.MintLimitParams[](3);

        for (uint i = 0; i < 3; i++) {
            params[i] = PoolsharkStructs.MintLimitParams({
                to: alice,
                amount: uint128(0.1 ether), // Small amount
                mintPercent: 1e6,
                positionId: 0,
                lower: -887220,
                upper: 887220,
                zeroForOne: true,
                callbackData: ''
            });
        }

        // Send minimal ETH - just 1 wei
        uint256 ethSent = 1 wei;

        console.log('Sending', ethSent, 'wei for 3 pool operations');
        console.log('Each pool will get wrapped=true because msg.value > 0');
        console.log('But only', ethSent, 'wei is actually available');

        try router.multiMintLimit{value: ethSent}(pools, params) {
            console.log('Unexpected success - vulnerability confirmed');
        } catch Error(string memory reason) {
            console.log('Expected failure:', reason);
        } catch (bytes memory) {
            console.log('Expected failure with low level error');
        }

        vm.stopPrank();
    }
}
