{"abi": [{"type": "function", "name": "MAX_PROTOCOL_FILL_FEE", "inputs": [], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}], "stateMutability": "view"}, {"type": "function", "name": "MAX_PROTOCOL_SWAP_FEE", "inputs": [], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}], "stateMutability": "view"}, {"type": "function", "name": "ethAdd<PERSON>", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}], "bytecode": {"object": "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", "sourceMap": "254:3267:50:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;254:3267:50;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "254:3267:50:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;912:2607;;;;;;;;;;-1:-1:-1;912:2607:50;;;;;:::i;:::-;;:::i;:::-;;;;-1:-1:-1;;;;;4391:15:78;;;4373:34;;4443:15;;;;4438:2;4423:18;;4416:43;4293:18;912:2607:50;;;;;;;;776:47;;821:1;776:47;;;;;-1:-1:-1;;;;;4751:32:78;;;4733:51;;4721:2;4706:18;776:47:50;4579:211:78;307:51:50;;355:3;307:51;;;;;5072:6:78;5060:19;;;5042:38;;5030:2;5015:18;307:51:50;4890:196:78;397:51:50;;445:3;397:51;;912:2607;1225:19;;;;1130:18;;;;558:4;1225:41;1224:47;1220:270;;1291:23;;355:3;1291:47;;;;;1287:119;;;1356:50;;-1:-1:-1;;;1356:50:50;;5293:2:78;1356:50:50;;;5275:21:78;;;5312:18;;;5305:30;5371:34;5351:18;;;5344:62;5423:18;;1356:50:50;;;;;;;;;1456:23;;1420:33;;;:59;;;;;;-1:-1:-1;;;1420:59:50;-1:-1:-1;;;;1420:59:50;;;;;;;;;1220:270;1531:19;;;;614:4;1531:41;1530:47;1526:270;;355:3;1597:47;;:6;:23;;;:47;;;1593:119;;;1662:50;;-1:-1:-1;;;1662:50:50;;5293:2:78;1662:50:50;;;5275:21:78;;;5312:18;;;5305:30;5371:34;5351:18;;;5344:62;5423:18;;1662:50:50;5091:356:78;1662:50:50;1762:23;;;;1726:33;;;:59;;;;;;-1:-1:-1;;;1726:59:50;-1:-1:-1;;;;1726:59:50;;;;;;;;;1526:270;1837:19;;;;670:4;1837:41;1836:47;1832:270;;445:3;1903:47;;:6;:23;;;:47;;;1899:119;;;1968:50;;-1:-1:-1;;;1968:50:50;;5654:2:78;1968:50:50;;;5636:21:78;;;5673:18;;;5666:30;5732:34;5712:18;;;5705:62;5784:18;;1968:50:50;5452:356:78;1968:50:50;2068:23;;;;2032:33;;;:59;;-1:-1:-1;;2032:59:50;;;;;;;;;;;1832:270;2143:19;;;;726:4;2143:41;2142:47;2138:270;;445:3;2209:47;;:6;:23;;;:47;;;2205:119;;;2274:50;;-1:-1:-1;;;2274:50:50;;5654:2:78;2274:50:50;;;5636:21:78;;;5673:18;;;5666:30;5732:34;5712:18;;;5705:62;5784:18;;2274:50:50;5452:356:78;2274:50:50;2374:23;;;;2338:33;;;:59;;-1:-1:-1;;2338:59:50;;;;;;;;;;;2138:270;2417:13;2451:9;:15;;;-1:-1:-1;;;;;2433:40:50;;:42;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2558:30;;;;;2670;;;;;-1:-1:-1;;;;;2710:34:50;;;;;;2754;;;;;;-1:-1:-1;;;2558:30:50;;;;;;;-1:-1:-1;2670:30:50;;;;-1:-1:-1;2417:58:50;-1:-1:-1;2803:14:50;;2799:94;;2831:62;2857:5;2864:9;:16;;;2882:10;-1:-1:-1;;;;;2831:62:50;:25;:62::i;:::-;-1:-1:-1;;;;;2907:14:50;;;2903:94;;2935:62;2961:5;2968:9;:16;;;2986:10;-1:-1:-1;;;;;2935:62:50;:25;:62::i;:::-;3064:21;:25;3060:164;;3146:67;3172:5;821:1;3191:21;3146:25;:67::i;:::-;3304:19;;;;-1:-1:-1;;;;;3296:36:50;;:40;3292:179;;3410:9;:19;;;-1:-1:-1;;;;;3393:49:50;;3443:5;3450:9;3393:67;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3292:179;3481:31;912:2607;;;;;;:::o;876:1313:64:-;989:12;-1:-1:-1;;;;;1015:19:64;;1011:190;;1064:26;;-1:-1:-1;;;;;1064:7:64;;;1079:6;;1064:26;;;;1079:6;1064:7;:26;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1050:40;;;;;1109:7;1104:66;;1118:52;;-1:-1:-1;;;1118:52:64;;8355:2:78;1118:52:64;;;8337:21:78;8394:2;8374:18;;;8367:30;8433:34;8413:18;;;8406:62;-1:-1:-1;;;8484:18:78;;;8477:32;8526:19;;1118:52:64;8153:398:78;1118:52:64;1184:7;876:1313;;;:::o;1011:190::-;1214:6;1224:1;1214:11;1210:24;;1227:7;876:1313;;;:::o;1210:24::-;1499:31;;-1:-1:-1;;;1499:31:64;;-1:-1:-1;;;;;8748:32:78;;;1499:31:64;;;8730:51:78;8797:18;;;8790:34;;;1270:5:64;;1499:19;;;;;;8703:18:78;;1499:31:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;1551:5:64;;-1:-1:-1;1596:16:64;1625:123;;;;1766:2;1761:193;;;;2076:1;2065:12;;1589:502;;1625:123;1710:1;1699:12;;1625:123;;1761:193;1854:2;1851:1;1848;1833:24;1891:1;1885:8;1874:19;;1589:502;;2115:7;2110:72;;2124:58;;-1:-1:-1;;;2124:58:64;;9319:2:78;2124:58:64;;;9301:21:78;9358:2;9338:18;;;9331:30;9397:34;9377:18;;;9370:62;-1:-1:-1;;;9448:18:78;;;9441:38;9496:19;;2124:58:64;9117:404:78;2124:58:64;979:1210;;876:1313;;;:::o;14:352:78:-;86:2;80:9;128:6;116:19;;165:18;150:34;;186:22;;;147:62;144:185;;;251:10;246:3;242:20;239:1;232:31;286:4;283:1;276:15;314:4;311:1;304:15;144:185;345:2;338:22;14:352;:::o;371:345::-;438:2;432:9;480:4;468:17;;515:18;500:34;;536:22;;;497:62;494:185;;;601:10;596:3;592:20;589:1;582:31;636:4;633:1;626:15;664:4;661:1;654:15;721:159;788:20;;848:6;837:18;;827:29;;817:57;;870:1;867;860:12;817:57;721:159;;;:::o;885:131::-;-1:-1:-1;;;;;960:31:78;;950:42;;940:70;;1006:1;1003;996:12;940:70;885:131;:::o;1021:134::-;1089:20;;1118:31;1089:20;1118:31;:::i;1160:723::-;1218:5;1266:4;1254:9;1249:3;1245:19;1241:30;1238:50;;;1284:1;1281;1274:12;1238:50;1317:4;1311:11;1361:4;1353:6;1349:17;1432:6;1420:10;1417:22;1396:18;1384:10;1381:34;1378:62;1375:185;;;1482:10;1477:3;1473:20;1470:1;1463:31;1517:4;1514:1;1507:15;1545:4;1542:1;1535:15;1375:185;1576:4;1569:24;1611:6;-1:-1:-1;1611:6:78;1641:23;;1673:33;1641:23;1673:33;:::i;:::-;1715:23;;1790:2;1775:18;;1762:32;1803:33;1762:32;1803:33;:::i;:::-;1864:2;1852:15;;;;1845:32;1160:723;;-1:-1:-1;;1160:723:78:o;1888:163::-;1955:20;;2015:10;2004:22;;1994:33;;1984:61;;2041:1;2038;2031:12;2056:160;2122:20;;2182:1;2171:20;;;2161:31;;2151:59;;2206:1;2203;2196:12;2221:940;2283:5;2331:6;2319:9;2314:3;2310:19;2306:32;2303:52;;;2351:1;2348;2341:12;2303:52;2373:22;;:::i;:::-;2364:31;;2418:29;2437:9;2418:29;:::i;:::-;2411:5;2404:44;2480:38;2514:2;2503:9;2499:18;2480:38;:::i;:::-;2475:2;2468:5;2464:14;2457:62;2551:38;2585:2;2574:9;2570:18;2551:38;:::i;:::-;2546:2;2539:5;2535:14;2528:62;2622:54;2672:3;2667:2;2656:9;2652:18;2622:54;:::i;:::-;2617:2;2610:5;2606:14;2599:78;2711:39;2745:3;2734:9;2730:19;2711:39;:::i;:::-;2704:4;2697:5;2693:16;2686:65;2784:39;2818:3;2807:9;2803:19;2784:39;:::i;:::-;2778:3;2771:5;2767:15;2760:64;2857:39;2891:3;2880:9;2876:19;2857:39;:::i;:::-;2851:3;2844:5;2840:15;2833:64;2916:3;2952:37;2985:2;2974:9;2970:18;2952:37;:::i;:::-;2946:3;2939:5;2935:15;2928:62;3009:3;3044:36;3076:2;3065:9;3061:18;3044:36;:::i;:::-;3039:2;3032:5;3028:14;3021:60;3113:41;3146:6;3135:9;3131:22;3113:41;:::i;:::-;3097:14;;;3090:65;-1:-1:-1;3101:5:78;2221:940;-1:-1:-1;;2221:940:78:o;3166:967::-;3334:6;3342;3350;3394:9;3385:7;3381:23;3424:3;3420:2;3416:12;3413:32;;;3441:1;3438;3431:12;3413:32;3464:23;;;-1:-1:-1;3521:4:78;-1:-1:-1;;3503:16:78;;3499:27;3496:47;;;3539:1;3536;3529:12;3496:47;;3565:17;;:::i;:::-;3605:37;3638:2;3627:9;3623:18;3605:37;:::i;:::-;3598:5;3591:52;3675:37;3708:2;3697:9;3693:18;3675:37;:::i;:::-;3670:2;3663:5;3659:14;3652:61;3745:37;3778:2;3767:9;3763:18;3745:37;:::i;:::-;3740:2;3733:5;3729:14;3722:61;3815:38;3848:3;3837:9;3833:19;3815:38;:::i;:::-;3810:2;3803:5;3799:14;3792:62;3906:4;3895:9;3891:20;3878:34;3956:4;3947:7;3943:18;3934:7;3931:31;3921:59;;3976:1;3973;3966:12;3921:59;4007:3;3996:15;;3989:32;4000:5;-1:-1:-1;4064:63:78;4119:7;4113:3;4098:19;;4064:63;:::i;:::-;4054:73;;3166:967;;;;;:::o;5813:251::-;5883:6;5936:2;5924:9;5915:7;5911:23;5907:32;5904:52;;;5952:1;5949;5942:12;5904:52;5984:9;5978:16;6003:31;6028:5;6003:31;:::i;:::-;6053:5;5813:251;-1:-1:-1;;;5813:251:78:o;6477:1461::-;-1:-1:-1;;;;;6736:32:78;;;6718:51;;6797:13;;4536:31;6827:2;6812:18;;4524:44;6705:3;6690:19;;6878:2;6866:15;;6860:22;-1:-1:-1;;;;;4536:31:78;;6939:2;6924:18;;4524:44;-1:-1:-1;6992:2:78;6980:15;;6974:22;-1:-1:-1;;;;;4536:31:78;;7055:2;7040:18;;4524:44;-1:-1:-1;7108:2:78;7096:15;;7090:22;6188:12;;-1:-1:-1;;;;;6184:21:78;;;7182:3;7167:19;;6172:34;6259:4;6248:16;;6242:23;6238:32;6222:14;;;6215:56;-1:-1:-1;7236:3:78;7224:16;;7218:23;-1:-1:-1;;;;;4536:31:78;;7300:3;7285:19;;4524:44;-1:-1:-1;7354:4:78;7342:17;;7336:24;-1:-1:-1;;;;;4536:31:78;;7419:3;7404:19;;4524:44;7369:55;7473:3;7465:6;7461:16;7455:23;7497:3;7509:54;7559:2;7548:9;7544:18;7528:14;-1:-1:-1;;;;;4536:31:78;4524:44;;4470:104;7509:54;7612:3;7604:6;7600:16;7594:23;7572:45;;7636:3;7648:53;7697:2;7686:9;7682:18;7666:14;6358:10;6347:22;6335:35;;6282:94;7648:53;7750:2;7742:6;7738:15;7732:22;7710:44;;7763:53;7811:3;7800:9;7796:19;7780:14;6456:1;6445:20;6433:33;;6381:91;7763:53;7853:15;;7847:22;4871:6;4860:18;;7927:3;7912:19;;4848:31;7847:22;-1:-1:-1;7878:54:78;;-1:-1:-1;4795:90:78;7878:54;;6477:1461;;;;;:::o;8835:277::-;8902:6;8955:2;8943:9;8934:7;8930:23;8926:32;8923:52;;;8971:1;8968;8961:12;8923:52;9003:9;8997:16;9056:5;9049:13;9042:21;9035:5;9032:32;9022:60;;9078:1;9075;9068:12", "linkReferences": {}}, "methodIdentifiers": {"MAX_PROTOCOL_FILL_FEE()": "c1114ed1", "MAX_PROTOCOL_SWAP_FEE()": "84bc10cc", "ethAddress()": "41398b15", "perform(PoolsharkStructs.GlobalState storage,PoolsharkStructs.FeesParams,PoolsharkStructs.LimitImmutables)": "2322cded"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"MAX_PROTOCOL_FILL_FEE\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_PROTOCOL_SWAP_FEE\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"ethAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"perform(PoolsharkStructs.GlobalState storage,PoolsharkStructs.FeesParams,PoolsharkStructs.LimitImmutables)\":{\"details\":\"- LimitPoolManager (i.e. constants.owner) emits events in aggregate\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/pool/FeesCall.sol\":\"FeesCall\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/IPositionERC1155.sol\":{\"keccak256\":\"0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11\",\"dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe\"]},\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/limit/ILimitPoolManager.sol\":{\"keccak256\":\"0x3569f41d49e23238d34b3dc91e7fe7587c8fa141cacc97bf382caa1b580837de\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://aa5545c53994d586c1e7e6aaeb7d0522a678ae012867268e93e3f65060e7360f\",\"dweb:/ipfs/QmU34ZDnYxCDV6i8fxq13AU5LptW3rXgY5ZYpLy2t8sLqk\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/libraries/pool/FeesCall.sol\":{\"keccak256\":\"0xb47d915fa1158de36d4d0345d9ce7355078eeec7141521eb2835ce0747b08dd0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ecc13f3b564cf10b71b5c6329338a233227db0ee02caad88131b21ac3791778f\",\"dweb:/ipfs/QmZXeyFULJKtBYnJBnGL628tgdkc5oWwzBcxb27bSbAZge\"]},\"contracts/libraries/utils/SafeTransfers.sol\":{\"keccak256\":\"0x1dcd632dc5123e4f15ad19c8c88ba0a3bf42f0a5b91211169aecfcc8523ed0f8\",\"license\":\"Unlicense\",\"urls\":[\"bzz-raw://67467c0c05b10847e84f3f7229027de3f80f448cb8589a0b4314b45c76220dd1\",\"dweb:/ipfs/QmS17vjUJ16LmtZ7cKdQAGZKcdEYck5NsoAhjGgZdc3b9S\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0\",\"dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34\",\"dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd\",\"dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92\",\"dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f\",\"dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "MAX_PROTOCOL_FILL_FEE", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MAX_PROTOCOL_SWAP_FEE", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ethAdd<PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"perform(PoolsharkStructs.GlobalState storage,PoolsharkStructs.FeesParams,PoolsharkStructs.LimitImmutables)": {"details": "- LimitPoolManager (i.e. constants.owner) emits events in aggregate"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/pool/FeesCall.sol": "FeesCall"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/IPositionERC1155.sol": {"keccak256": "0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c", "urls": ["bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11", "dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe"], "license": "MIT"}, "contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPoolManager.sol": {"keccak256": "0x3569f41d49e23238d34b3dc91e7fe7587c8fa141cacc97bf382caa1b580837de", "urls": ["bzz-raw://aa5545c53994d586c1e7e6aaeb7d0522a678ae012867268e93e3f65060e7360f", "dweb:/ipfs/QmU34ZDnYxCDV6i8fxq13AU5LptW3rXgY5ZYpLy2t8sLqk"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/libraries/pool/FeesCall.sol": {"keccak256": "0xb47d915fa1158de36d4d0345d9ce7355078eeec7141521eb2835ce0747b08dd0", "urls": ["bzz-raw://ecc13f3b564cf10b71b5c6329338a233227db0ee02caad88131b21ac3791778f", "dweb:/ipfs/QmZXeyFULJKtBYnJBnGL628tgdkc5oWwzBcxb27bSbAZge"], "license": "MIT"}, "contracts/libraries/utils/SafeTransfers.sol": {"keccak256": "0x1dcd632dc5123e4f15ad19c8c88ba0a3bf42f0a5b91211169aecfcc8523ed0f8", "urls": ["bzz-raw://67467c0c05b10847e84f3f7229027de3f80f448cb8589a0b4314b45c76220dd1", "dweb:/ipfs/QmS17vjUJ16LmtZ7cKdQAGZKcdEYck5NsoAhjGgZdc3b9S"], "license": "Unlicense"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238", "urls": ["bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0", "dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b", "urls": ["bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34", "dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca", "urls": ["bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd", "dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7", "urls": ["bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92", "dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1", "urls": ["bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f", "dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy"], "license": "MIT"}}, "version": 1}, "id": 50}