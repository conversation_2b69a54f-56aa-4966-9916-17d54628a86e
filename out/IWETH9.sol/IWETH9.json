{"abi": [{"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "deposit", "inputs": [], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "transfer", "inputs": [{"name": "dst", "type": "address", "internalType": "address"}, {"name": "wad", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "wad", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"balanceOf(address)": "70a08231", "deposit()": "d0e30db0", "transfer(address,uint256)": "a9059cbb", "withdraw(uint256)": "2e1a7d4d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"dst\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"wad\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"wad\",\"type\":\"uint256\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"balanceOf(address)\":{\"notice\":\"Returns balance for address\"},\"deposit()\":{\"notice\":\"Deposits ether in return for wrapped ether\"},\"transfer(address,uint256)\":{\"notice\":\"Withdraws ether from wrapped ether balance\"},\"withdraw(uint256)\":{\"notice\":\"Withdraws ether from wrapped ether balance\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/IWETH9.sol\":\"IWETH9\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/IWETH9.sol\":{\"keccak256\":\"0x3e091b161d32f6e3dbd36f3a1357fdaae7d95d89cae8e88910a5b2701886e580\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://c3ee081283f5b4418ec5e0c35ee46a399162a78989dda2cdca2d493761506285\",\"dweb:/ipfs/QmX7hss934AGRFhLaW32ttcTGmEni8A2DvUXz3vKNvzT58\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "payable", "type": "function", "name": "deposit"}, {"inputs": [{"internalType": "address", "name": "dst", "type": "address"}, {"internalType": "uint256", "name": "wad", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "wad", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {"balanceOf(address)": {"notice": "Returns balance for address"}, "deposit()": {"notice": "Deposits ether in return for wrapped ether"}, "transfer(address,uint256)": {"notice": "Withdraws ether from wrapped ether balance"}, "withdraw(uint256)": {"notice": "Withdraws ether from wrapped ether balance"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/IWETH9.sol": "IWETH9"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/IWETH9.sol": {"keccak256": "0x3e091b161d32f6e3dbd36f3a1357fdaae7d95d89cae8e88910a5b2701886e580", "urls": ["bzz-raw://c3ee081283f5b4418ec5e0c35ee46a399162a78989dda2cdca2d493761506285", "dweb:/ipfs/QmX7hss934AGRFhLaW32ttcTGmEni8A2DvUXz3vKNvzT58"], "license": "GPL-2.0-or-later"}}, "version": 1}, "id": 20}