{"abi": [{"type": "function", "name": "globalState", "inputs": [], "outputs": [{"name": "pool", "type": "tuple", "internalType": "struct PoolsharkStructs.RangePoolState", "components": [{"name": "samples", "type": "tuple", "internalType": "struct PoolsharkStructs.SampleState", "components": [{"name": "index", "type": "uint16", "internalType": "uint16"}, {"name": "count", "type": "uint16", "internalType": "uint16"}, {"name": "countMax", "type": "uint16", "internalType": "uint16"}]}, {"name": "feeGrowthGlobal0", "type": "uint200", "internalType": "uint200"}, {"name": "feeGrowthGlobal1", "type": "uint200", "internalType": "uint200"}, {"name": "secondsPerLiquidityAccum", "type": "uint160", "internalType": "uint160"}, {"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "tickSecondsAccum", "type": "int56", "internalType": "int56"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}, {"name": "protocolSwapFee0", "type": "uint16", "internalType": "uint16"}, {"name": "protocolSwapFee1", "type": "uint16", "internalType": "uint16"}]}, {"name": "pool0", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitPoolState", "components": [{"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFees", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFillFee", "type": "uint16", "internalType": "uint16"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}]}, {"name": "pool1", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitPoolState", "components": [{"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFees", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFillFee", "type": "uint16", "internalType": "uint16"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}]}, {"name": "liquidityGlobal", "type": "uint128", "internalType": "uint128"}, {"name": "positionIdNext", "type": "uint32", "internalType": "uint32"}, {"name": "epoch", "type": "uint32", "internalType": "uint32"}, {"name": "unlocked", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "limitTickMap", "inputs": [], "outputs": [{"name": "blocks", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "positions", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "feeGrowthInside0Last", "type": "uint256", "internalType": "uint256"}, {"name": "feeGrowthInside1Last", "type": "uint256", "internalType": "uint256"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "positions0", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "epochLast", "type": "uint32", "internalType": "uint32"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}, {"name": "crossedInto", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "positions1", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "epochLast", "type": "uint32", "internalType": "uint32"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}, {"name": "crossedInto", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "rangeTickMap", "inputs": [], "outputs": [{"name": "blocks", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "samples", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "blockTimestamp", "type": "uint32", "internalType": "uint32"}, {"name": "tickSecondsAccum", "type": "int56", "internalType": "int56"}, {"name": "secondsPerLiquidityAccum", "type": "uint160", "internalType": "uint160"}], "stateMutability": "view"}, {"type": "function", "name": "ticks", "inputs": [{"name": "", "type": "int24", "internalType": "int24"}], "outputs": [{"name": "range", "type": "tuple", "internalType": "struct PoolsharkStructs.RangeTick", "components": [{"name": "feeGrowthOutside0", "type": "uint200", "internalType": "uint200"}, {"name": "feeGrowthOutside1", "type": "uint200", "internalType": "uint200"}, {"name": "secondsPerLiquidityAccumOutside", "type": "uint160", "internalType": "uint160"}, {"name": "tickSecondsAccumOutside", "type": "int56", "internalType": "int56"}, {"name": "liquidityDelta", "type": "int128", "internalType": "int128"}, {"name": "liquidityAbsolute", "type": "uint128", "internalType": "uint128"}]}, {"name": "limit", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitTick", "components": [{"name": "priceAt", "type": "uint160", "internalType": "uint160"}, {"name": "liquidityDelta", "type": "int128", "internalType": "int128"}, {"name": "liquidityAbsolute", "type": "uint128", "internalType": "uint128"}]}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"globalState()": "e76c01e4", "limitTickMap()": "60f9b0d3", "positions(uint256)": "99fbab88", "positions0(uint256)": "aedb4197", "positions1(uint256)": "256b9f39", "rangeTickMap()": "a99c9dd5", "samples(uint256)": "07e72129", "ticks(int24)": "f30dba93"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"globalState\",\"outputs\":[{\"components\":[{\"components\":[{\"internalType\":\"uint16\",\"name\":\"index\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"count\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"countMax\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.SampleState\",\"name\":\"samples\",\"type\":\"tuple\"},{\"internalType\":\"uint200\",\"name\":\"feeGrowthGlobal0\",\"type\":\"uint200\"},{\"internalType\":\"uint200\",\"name\":\"feeGrowthGlobal1\",\"type\":\"uint200\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"int56\",\"name\":\"tickSecondsAccum\",\"type\":\"int56\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"},{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee0\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee1\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.RangePoolState\",\"name\":\"pool\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"protocolFees\",\"type\":\"uint128\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee\",\"type\":\"uint16\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"}],\"internalType\":\"struct PoolsharkStructs.LimitPoolState\",\"name\":\"pool0\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"protocolFees\",\"type\":\"uint128\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee\",\"type\":\"uint16\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"}],\"internalType\":\"struct PoolsharkStructs.LimitPoolState\",\"name\":\"pool1\",\"type\":\"tuple\"},{\"internalType\":\"uint128\",\"name\":\"liquidityGlobal\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"positionIdNext\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"},{\"internalType\":\"uint8\",\"name\":\"unlocked\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"limitTickMap\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blocks\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"positions\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"feeGrowthInside0Last\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"feeGrowthInside1Last\",\"type\":\"uint256\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"positions0\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"epochLast\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"crossedInto\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"positions1\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"epochLast\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"crossedInto\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rangeTickMap\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blocks\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"samples\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"blockTimestamp\",\"type\":\"uint32\"},{\"internalType\":\"int56\",\"name\":\"tickSecondsAccum\",\"type\":\"int56\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int24\",\"name\":\"\",\"type\":\"int24\"}],\"name\":\"ticks\",\"outputs\":[{\"components\":[{\"internalType\":\"uint200\",\"name\":\"feeGrowthOutside0\",\"type\":\"uint200\"},{\"internalType\":\"uint200\",\"name\":\"feeGrowthOutside1\",\"type\":\"uint200\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccumOutside\",\"type\":\"uint160\"},{\"internalType\":\"int56\",\"name\":\"tickSecondsAccumOutside\",\"type\":\"int56\"},{\"internalType\":\"int128\",\"name\":\"liquidityDelta\",\"type\":\"int128\"},{\"internalType\":\"uint128\",\"name\":\"liquidityAbsolute\",\"type\":\"uint128\"}],\"internalType\":\"struct PoolsharkStructs.RangeTick\",\"name\":\"range\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"priceAt\",\"type\":\"uint160\"},{\"internalType\":\"int128\",\"name\":\"liquidityDelta\",\"type\":\"int128\"},{\"internalType\":\"uint128\",\"name\":\"liquidityAbsolute\",\"type\":\"uint128\"}],\"internalType\":\"struct PoolsharkStructs.LimitTick\",\"name\":\"limit\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"stateVariables\":{\"limitTickMap\":{\"details\":\"- tick bitmap for range ticks\"},\"positions\":{\"details\":\"- range and limit tick data\"},\"positions0\":{\"details\":\"- range positions token0 <> token1\"},\"positions1\":{\"details\":\"- limit positions token0 -> token1\"},\"rangeTickMap\":{\"details\":\"- holds pool state and other contract storage\"},\"samples\":{\"details\":\"- tick bitmap for limit ticks\"},\"ticks\":{\"details\":\"- oracle TWAP samples\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/base/storage/LimitPoolStorage.sol\":\"LimitPoolStorage\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/base/storage/LimitPoolFactoryStorage.sol\":{\"keccak256\":\"0xec8efbb85bf6d831dc0c3910eccbe1b6def550ac0b88eef62b4a7c0c314a31ae\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://363a51daf8ce8d46c62bb8d9821b99ea2ec7d4f81a0512f5b83901d3a9a4631f\",\"dweb:/ipfs/QmeKyWJwsX9LwB5A8ovGm7J2nToJvR1dnE3sMww46ERb55\"]},\"contracts/base/storage/LimitPoolStorage.sol\":{\"keccak256\":\"0x063aa09f6535188ff3bbe5ca25af30433460e10786c9fc4ea061a572d04b7ca3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://878402ff2c89a596e9d8a18278bc484df2301296a0725af83747809227bbe315\",\"dweb:/ipfs/QmSKz1tQRxPiP5cgJo8kg8RieStHHzbMk3zSCjVCUYHKKL\"]},\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/limit/ILimitPoolFactory.sol\":{\"keccak256\":\"0x89fc412319c30451896a1db8d77bb528c6df383523cf7f278534a645a4a49733\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://495509c3a546e0b85b34a8ef7fc114ca5bb98d831c8bbd176b42d36703f00fd0\",\"dweb:/ipfs/QmcFR6a32Fp7s4hRDmJfp7GaC65uQUtRo6VhE6ygCpZefm\"]},\"contracts/interfaces/limit/ILimitPoolStorageView.sol\":{\"keccak256\":\"0x81173f20ed82249830e6af5b51505e4373c86f2cba9ea7785d4f41cc0b54ee52\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://7905082bb0bf3bc4191e6fccdedd30bf2d662edf446a87a14df5c4cc5422759c\",\"dweb:/ipfs/QmdTppuhdTJjgLL6THPpown2Km6ya5bh6Q1qxAiw9oGo9b\"]},\"contracts/interfaces/structs/LimitPoolStructs.sol\":{\"keccak256\":\"0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef\",\"dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/interfaces/structs/RangePoolStructs.sol\":{\"keccak256\":\"0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9\",\"dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "globalState", "outputs": [{"internalType": "struct PoolsharkStructs.RangePoolState", "name": "pool", "type": "tuple", "components": [{"internalType": "struct PoolsharkStructs.SampleState", "name": "samples", "type": "tuple", "components": [{"internalType": "uint16", "name": "index", "type": "uint16"}, {"internalType": "uint16", "name": "count", "type": "uint16"}, {"internalType": "uint16", "name": "countMax", "type": "uint16"}]}, {"internalType": "uint200", "name": "feeGrowthGlobal0", "type": "uint200"}, {"internalType": "uint200", "name": "feeGrowthGlobal1", "type": "uint200"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160"}, {"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "int56", "name": "tickSecondsAccum", "type": "int56"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}, {"internalType": "uint16", "name": "protocolSwapFee0", "type": "uint16"}, {"internalType": "uint16", "name": "protocolSwapFee1", "type": "uint16"}]}, {"internalType": "struct PoolsharkStructs.LimitPoolState", "name": "pool0", "type": "tuple", "components": [{"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint128", "name": "protocolFees", "type": "uint128"}, {"internalType": "uint16", "name": "protocolFillFee", "type": "uint16"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}]}, {"internalType": "struct PoolsharkStructs.LimitPoolState", "name": "pool1", "type": "tuple", "components": [{"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint128", "name": "protocolFees", "type": "uint128"}, {"internalType": "uint16", "name": "protocolFillFee", "type": "uint16"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}]}, {"internalType": "uint128", "name": "liquidityGlobal", "type": "uint128"}, {"internalType": "uint32", "name": "positionIdNext", "type": "uint32"}, {"internalType": "uint32", "name": "epoch", "type": "uint32"}, {"internalType": "uint8", "name": "unlocked", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "limitTickMap", "outputs": [{"internalType": "uint256", "name": "blocks", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "positions", "outputs": [{"internalType": "uint256", "name": "feeGrowthInside0Last", "type": "uint256"}, {"internalType": "uint256", "name": "feeGrowthInside1Last", "type": "uint256"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "positions0", "outputs": [{"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint32", "name": "epochLast", "type": "uint32"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}, {"internalType": "bool", "name": "crossedInto", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "positions1", "outputs": [{"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint32", "name": "epochLast", "type": "uint32"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}, {"internalType": "bool", "name": "crossedInto", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rangeTickMap", "outputs": [{"internalType": "uint256", "name": "blocks", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "samples", "outputs": [{"internalType": "uint32", "name": "blockTimestamp", "type": "uint32"}, {"internalType": "int56", "name": "tickSecondsAccum", "type": "int56"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160"}]}, {"inputs": [{"internalType": "int24", "name": "", "type": "int24"}], "stateMutability": "view", "type": "function", "name": "ticks", "outputs": [{"internalType": "struct PoolsharkStructs.RangeTick", "name": "range", "type": "tuple", "components": [{"internalType": "uint200", "name": "feeGrowthOutside0", "type": "uint200"}, {"internalType": "uint200", "name": "feeGrowthOutside1", "type": "uint200"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccumOutside", "type": "uint160"}, {"internalType": "int56", "name": "tickSecondsAccumOutside", "type": "int56"}, {"internalType": "int128", "name": "liquidityDelta", "type": "int128"}, {"internalType": "uint128", "name": "liquidityAbsolute", "type": "uint128"}]}, {"internalType": "struct PoolsharkStructs.LimitTick", "name": "limit", "type": "tuple", "components": [{"internalType": "uint160", "name": "priceAt", "type": "uint160"}, {"internalType": "int128", "name": "liquidityDelta", "type": "int128"}, {"internalType": "uint128", "name": "liquidityAbsolute", "type": "uint128"}]}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/base/storage/LimitPoolStorage.sol": "LimitPoolStorage"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/base/storage/LimitPoolFactoryStorage.sol": {"keccak256": "0xec8efbb85bf6d831dc0c3910eccbe1b6def550ac0b88eef62b4a7c0c314a31ae", "urls": ["bzz-raw://363a51daf8ce8d46c62bb8d9821b99ea2ec7d4f81a0512f5b83901d3a9a4631f", "dweb:/ipfs/QmeKyWJwsX9LwB5A8ovGm7J2nToJvR1dnE3sMww46ERb55"], "license": "BUSL-1.1"}, "contracts/base/storage/LimitPoolStorage.sol": {"keccak256": "0x063aa09f6535188ff3bbe5ca25af30433460e10786c9fc4ea061a572d04b7ca3", "urls": ["bzz-raw://878402ff2c89a596e9d8a18278bc484df2301296a0725af83747809227bbe315", "dweb:/ipfs/QmSKz1tQRxPiP5cgJo8kg8RieStHHzbMk3zSCjVCUYHKKL"], "license": "BUSL-1.1"}, "contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPoolFactory.sol": {"keccak256": "0x89fc412319c30451896a1db8d77bb528c6df383523cf7f278534a645a4a49733", "urls": ["bzz-raw://495509c3a546e0b85b34a8ef7fc114ca5bb98d831c8bbd176b42d36703f00fd0", "dweb:/ipfs/QmcFR6a32Fp7s4hRDmJfp7GaC65uQUtRo6VhE6ygCpZefm"], "license": "BUSL-1.1"}, "contracts/interfaces/limit/ILimitPoolStorageView.sol": {"keccak256": "0x81173f20ed82249830e6af5b51505e4373c86f2cba9ea7785d4f41cc0b54ee52", "urls": ["bzz-raw://7905082bb0bf3bc4191e6fccdedd30bf2d662edf446a87a14df5c4cc5422759c", "dweb:/ipfs/QmdTppuhdTJjgLL6THPpown2Km6ya5bh6Q1qxAiw9oGo9b"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/LimitPoolStructs.sol": {"keccak256": "0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4", "urls": ["bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef", "dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/interfaces/structs/RangePoolStructs.sol": {"keccak256": "0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd", "urls": ["bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9", "dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx"], "license": "GPLv3"}}, "version": 1}, "id": 11}