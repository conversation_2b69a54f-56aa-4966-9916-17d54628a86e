{"abi": [{"type": "constructor", "inputs": [{"name": "factory_", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "burnLimit", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.BurnLimitParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "burnPercent", "type": "uint128", "internalType": "uint128"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "claim", "type": "int24", "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "burn<PERSON>ang<PERSON>", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.BurnRangeParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "burnPercent", "type": "uint128", "internalType": "uint128"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "factory", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "fees", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.FeesParams", "components": [{"name": "protocolSwapFee0", "type": "uint16", "internalType": "uint16"}, {"name": "protocolSwapFee1", "type": "uint16", "internalType": "uint16"}, {"name": "protocolFillFee0", "type": "uint16", "internalType": "uint16"}, {"name": "protocolFillFee1", "type": "uint16", "internalType": "uint16"}, {"name": "set<PERSON><PERSON><PERSON><PERSON>s", "type": "uint8", "internalType": "uint8"}]}], "outputs": [{"name": "token0Fees", "type": "uint128", "internalType": "uint128"}, {"name": "token1Fees", "type": "uint128", "internalType": "uint128"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "genesisTime", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "pure"}, {"type": "function", "name": "globalState", "inputs": [], "outputs": [{"name": "pool", "type": "tuple", "internalType": "struct PoolsharkStructs.RangePoolState", "components": [{"name": "samples", "type": "tuple", "internalType": "struct PoolsharkStructs.SampleState", "components": [{"name": "index", "type": "uint16", "internalType": "uint16"}, {"name": "count", "type": "uint16", "internalType": "uint16"}, {"name": "countMax", "type": "uint16", "internalType": "uint16"}]}, {"name": "feeGrowthGlobal0", "type": "uint200", "internalType": "uint200"}, {"name": "feeGrowthGlobal1", "type": "uint200", "internalType": "uint200"}, {"name": "secondsPerLiquidityAccum", "type": "uint160", "internalType": "uint160"}, {"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "tickSecondsAccum", "type": "int56", "internalType": "int56"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}, {"name": "protocolSwapFee0", "type": "uint16", "internalType": "uint16"}, {"name": "protocolSwapFee1", "type": "uint16", "internalType": "uint16"}]}, {"name": "pool0", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitPoolState", "components": [{"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFees", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFillFee", "type": "uint16", "internalType": "uint16"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}]}, {"name": "pool1", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitPoolState", "components": [{"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFees", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFillFee", "type": "uint16", "internalType": "uint16"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}]}, {"name": "liquidityGlobal", "type": "uint128", "internalType": "uint128"}, {"name": "positionIdNext", "type": "uint32", "internalType": "uint32"}, {"name": "epoch", "type": "uint32", "internalType": "uint32"}, {"name": "unlocked", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "immutables", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitImmutables", "components": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "poolImpl", "type": "address", "internalType": "address"}, {"name": "factory", "type": "address", "internalType": "address"}, {"name": "bounds", "type": "tuple", "internalType": "struct PoolsharkStructs.PriceBounds", "components": [{"name": "min", "type": "uint160", "internalType": "uint160"}, {"name": "max", "type": "uint160", "internalType": "uint160"}]}, {"name": "token0", "type": "address", "internalType": "address"}, {"name": "token1", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "genesisTime", "type": "uint32", "internalType": "uint32"}, {"name": "tickSpacing", "type": "int16", "internalType": "int16"}, {"name": "swapFee", "type": "uint16", "internalType": "uint16"}]}], "stateMutability": "view"}, {"type": "function", "name": "increaseSampleCount", "inputs": [{"name": "newSampleCountMax", "type": "uint16", "internalType": "uint16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "startPrice", "type": "uint160", "internalType": "uint160"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "limitTickMap", "inputs": [], "outputs": [{"name": "blocks", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "maxPrice", "inputs": [], "outputs": [{"name": "", "type": "uint160", "internalType": "uint160"}], "stateMutability": "pure"}, {"type": "function", "name": "minPrice", "inputs": [], "outputs": [{"name": "", "type": "uint160", "internalType": "uint160"}], "stateMutability": "pure"}, {"type": "function", "name": "mintLimit", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.MintLimitParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}, {"name": "mintPercent", "type": "uint96", "internalType": "uint96"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}, {"name": "callbackData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mintRange", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.MintRangeParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "amount0", "type": "uint128", "internalType": "uint128"}, {"name": "amount1", "type": "uint128", "internalType": "uint128"}, {"name": "callbackData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "original", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "poolToken", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "positions", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "feeGrowthInside0Last", "type": "uint256", "internalType": "uint256"}, {"name": "feeGrowthInside1Last", "type": "uint256", "internalType": "uint256"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "positions0", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "epochLast", "type": "uint32", "internalType": "uint32"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}, {"name": "crossedInto", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "positions1", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "epochLast", "type": "uint32", "internalType": "uint32"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}, {"name": "crossedInto", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "priceBounds", "inputs": [{"name": "tickSpacing", "type": "int16", "internalType": "int16"}], "outputs": [{"name": "", "type": "uint160", "internalType": "uint160"}, {"name": "", "type": "uint160", "internalType": "uint160"}], "stateMutability": "pure"}, {"type": "function", "name": "quote", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.QuoteParams", "components": [{"name": "priceLimit", "type": "uint160", "internalType": "uint160"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}, {"name": "exactIn", "type": "bool", "internalType": "bool"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}]}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint160", "internalType": "uint160"}], "stateMutability": "view"}, {"type": "function", "name": "rangeTickMap", "inputs": [], "outputs": [{"name": "blocks", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "sample", "inputs": [{"name": "secondsAgo", "type": "uint32[]", "internalType": "uint32[]"}], "outputs": [{"name": "tickSecondsAccum", "type": "int56[]", "internalType": "int56[]"}, {"name": "secondsPerLiquidityAccum", "type": "uint160[]", "internalType": "uint160[]"}, {"name": "averagePrice", "type": "uint160", "internalType": "uint160"}, {"name": "averageLiquidity", "type": "uint128", "internalType": "uint128"}, {"name": "averageTick", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "samples", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "blockTimestamp", "type": "uint32", "internalType": "uint32"}, {"name": "tickSecondsAccum", "type": "int56", "internalType": "int56"}, {"name": "secondsPerLiquidityAccum", "type": "uint160", "internalType": "uint160"}], "stateMutability": "view"}, {"type": "function", "name": "snapshotLimit", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.SnapshotLimitParams", "components": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "burnPercent", "type": "uint128", "internalType": "uint128"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "claim", "type": "int24", "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}]}], "outputs": [{"name": "", "type": "uint128", "internalType": "uint128"}, {"name": "", "type": "uint128", "internalType": "uint128"}], "stateMutability": "view"}, {"type": "function", "name": "snapshotRange", "inputs": [{"name": "positionId", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "tickSecondsAccum", "type": "int56", "internalType": "int56"}, {"name": "secondsPerLiquidityAccum", "type": "uint160", "internalType": "uint160"}, {"name": "feesOwed0", "type": "uint128", "internalType": "uint128"}, {"name": "feesOwed1", "type": "uint128", "internalType": "uint128"}], "stateMutability": "view"}, {"type": "function", "name": "swap", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.SwapParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "priceLimit", "type": "uint160", "internalType": "uint160"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}, {"name": "exactIn", "type": "bool", "internalType": "bool"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}, {"name": "callbackData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "", "type": "int256", "internalType": "int256"}, {"name": "", "type": "int256", "internalType": "int256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "swapFee", "inputs": [], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}], "stateMutability": "pure"}, {"type": "function", "name": "tickSpacing", "inputs": [], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "pure"}, {"type": "function", "name": "ticks", "inputs": [{"name": "", "type": "int24", "internalType": "int24"}], "outputs": [{"name": "range", "type": "tuple", "internalType": "struct PoolsharkStructs.RangeTick", "components": [{"name": "feeGrowthOutside0", "type": "uint200", "internalType": "uint200"}, {"name": "feeGrowthOutside1", "type": "uint200", "internalType": "uint200"}, {"name": "secondsPerLiquidityAccumOutside", "type": "uint160", "internalType": "uint160"}, {"name": "tickSecondsAccumOutside", "type": "int56", "internalType": "int56"}, {"name": "liquidityDelta", "type": "int128", "internalType": "int128"}, {"name": "liquidityAbsolute", "type": "uint128", "internalType": "uint128"}]}, {"name": "limit", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitTick", "components": [{"name": "priceAt", "type": "uint160", "internalType": "uint160"}, {"name": "liquidityDelta", "type": "int128", "internalType": "int128"}, {"name": "liquidityAbsolute", "type": "uint128", "internalType": "uint128"}]}], "stateMutability": "view"}, {"type": "function", "name": "token0", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "token1", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "error", "name": "ReentrancyGuardInvalidState", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReadOnlyReentrantCall", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}], "bytecode": {"object": "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__$6dea53a5b1a3c14df3d68f8a290554c5af$__9063a07aea75906109c857620100196109cd565b620100185b620100166017600d601260008a896040518963ffffffff1660e01b81526004016109fe989796959493929190613516565b60006040518083038186803b158015610a1657600080fd5b505af4158015610a2a573d6000803e3d6000fd5b5050505050610a388161190b565b5050565b60006109586078611950565b6000806000610a556120c3565b610a5d611556565b6020820152604051635fdf9d5560e01b815273__$9e4c229d336278bda7a6b638eb4f56779a$__90635fdf9d5590610aa9906201001690600d906012906000908c9089906004016135fa565b606060405180830381865af4158015610ac6573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610aea9190613669565b935093509350509193909250565b6000610958607e611973565b60008060008073__$286c9a6ab289b16a925f6a63d27fed5f3d$__6368b3fe3062010017620100166000610b36611556565b8a6040518663ffffffff1660e01b8152600401610b5795949392919061380d565b608060405180830381865af4158015610b74573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610b98919061386b565b93509350935093509193509193565b6000610bb2816116c7565b610bba61170e565b610bc2612163565b610bca611556565b816080018190525073__$1214492daac452055e29b230bdd72e484d$__63f15bc8cc8460800151610bfe5762010019610c03565b620100185b620100166012600088876040518763ffffffff1660e01b81526004016109fe96959493929190613911565b600080610c39611996565b6000610c44816116c7565b610c4c61170e565b73__$f0f62380d12e0e837b46ba5507a2cc3702$__632322cded600086610c71611556565b6040518463ffffffff1660e01b8152600401610c8f93929190613a3f565b6040805180830381865af4158015610cab573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610ccf9190613a9e565b92509250610cdc8161190b565b50915091565b60008073__$06f006a50281b95ada637fcca2c3169e65$__6363fbcd438460800151610d115762010019610d16565b620100185b6201001660126000610d26611556565b896040518763ffffffff1660e01b8152600401610d4896959493929190613ad8565b6040805180830381865af4158015610d64573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906109699190613a9e565b6000610958600061168f565b6000610958603c61168f565b6000610958607c611973565b6000610958602861168f565b6000610dc3816116c7565b610dcb61170e565b610dd860176000846119ee565b610a388161190b565b6000806000610def816116c7565b610df761170e565b610dff6120c3565b610e07611556565b602082015260405163a835b0a360e01b815273__$8397ab196537c2cfcd89b6140849e98c7e$__9063a835b0a390610e56906201001690601790600d906012906000908d908a90600401613b17565b6040805180830381865af4158015610e72573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610e969190613bb4565b9350935050610cdc8161190b565b6000610958606461168f565b6000610958605061168f565b6000610ec7816116c7565b610ecf61170e565b610ed761222e565b610edf611556565b6040808301919091525163bccd276b60e01b815273__$14b14c61645669b42e011afd470eeccf72$__9063bccd276b906109fe9062010017906201001690600d9060179060009089908c90600401613c8b565b606080600080600073__$82e69f90fc4689a77ae11a0614a00f0cb8$__631fb854c06000610f5e611556565b896040518463ffffffff1660e01b8152600401610f7d93929190613d77565b600060405180830381865af4158015610f9a573d6000803e3d6000fd5b505050506040513d6000823e601f3d908101601f19168201604052610fc29190810190613e5f565b939a9299509097509550909350915050565b6000610fdf816116c7565b610fe7611ac6565b610fef61170e565b73__$dc25dd3a5fe6a540f35c01c335c2ccfd23$__63d9195ff2600d601260176000611019611556565b886040518763ffffffff1660e01b815260040161103b96959493929190613f53565b61034060405180830381865af4158015611059573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061107d91906140c0565b6000808201518160000160008201518160000160008201518160000160006101000a81548161ffff021916908361ffff16021790555060208201518160000160026101000a81548161ffff021916908361ffff16021790555060408201518160000160046101000a81548161ffff021916908361ffff160217905550505060208201518160010160006101000a8154816001600160c81b0302191690836001600160c81b0316021790555060408201518160020160006101000a8154816001600160c81b0302191690836001600160c81b0316021790555060608201518160030160006101000a8154816001600160a01b0302191690836001600160a01b0316021790555060808201518160040160006101000a8154816001600160a01b0302191690836001600160a01b0316021790555060a08201518160050160006101000a8154816001600160801b0302191690836001600160801b0316021790555060c08201518160050160106101000a81548166ffffffffffffff021916908360060b66ffffffffffffff16021790555060e08201518160050160176101000a81548162ffffff021916908360020b62ffffff16021790555061010082015181600501601a6101000a81548161ffff021916908361ffff16021790555061012082015181600501601c6101000a81548161ffff021916908361ffff160217905550505060208201518160060160008201518160000160006101000a8154816001600160a01b0302191690836001600160a01b0316021790555060208201518160010160006101000a8154816001600160801b0302191690836001600160801b0316021790555060408201518160010160106101000a8154816001600160801b0302191690836001600160801b0316021790555060608201518160020160006101000a81548161ffff021916908361ffff16021790555060808201518160020160026101000a81548162ffffff021916908360020b62ffffff160217905550505060408201518160090160008201518160000160006101000a8154816001600160a01b0302191690836001600160a01b0316021790555060208201518160010160006101000a8154816001600160801b0302191690836001600160801b0316021790555060408201518160010160106101000a8154816001600160801b0302191690836001600160801b0316021790555060608201518160020160006101000a81548161ffff021916908361ffff16021790555060808201518160020160026101000a81548162ffffff021916908360020b62ffffff1602179055505050606082015181600c0160006101000a8154816001600160801b0302191690836001600160801b03160217905550608082015181600c0160106101000a81548163ffffffff021916908363ffffffff16021790555060a082015181600c0160146101000a81548163ffffffff021916908363ffffffff16021790555060c082015181600c0160186101000a81548160ff021916908360ff160217905550905050610a388161190b565b60006114eb816116c7565b6114f361170e565b6114fb6122bc565b611503611556565b6040808301919091525163b04d4a4960e01b815273__$16f93f2e1504eec0c942977e18129be78e$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", "sourceMap": "1060:6956:0:-:0;;;1494:113;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1567:4;1548:24;;-1:-1:-1;;;;;1582:18:0;;;1060:6956;;14:290:78;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;179:16;;-1:-1:-1;;;;;224:31:78;;214:42;;204:70;;270:1;267;260:12;204:70;293:5;14:290;-1:-1:-1;;;14:290:78:o;:::-;1060:6956:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {"contracts/libraries/Ticks.sol": {"Ticks": [{"start": 4285, "length": 20}]}, "contracts/libraries/limit/pool/BurnLimitCall.sol": {"BurnLimitCall": [{"start": 3232, "length": 20}]}, "contracts/libraries/limit/pool/MintLimitCall.sol": {"MintLimitCall": [{"start": 2669, "length": 20}]}, "contracts/libraries/limit/pool/SnapshotLimitCall.sol": {"SnapshotLimitCall": [{"start": 3507, "length": 20}]}, "contracts/libraries/pool/FeesCall.sol": {"FeesCall": [{"start": 3354, "length": 20}]}, "contracts/libraries/pool/QuoteCall.sol": {"QuoteCall": [{"start": 2877, "length": 20}]}, "contracts/libraries/pool/SampleCall.sol": {"SampleCall": [{"start": 4104, "length": 20}]}, "contracts/libraries/pool/SwapCall.sol": {"SwapCall": [{"start": 3815, "length": 20}]}, "contracts/libraries/range/pool/BurnRangeCall.sol": {"BurnRangeCall": [{"start": 5605, "length": 20}]}, "contracts/libraries/range/pool/MintRangeCall.sol": {"MintRangeCall": [{"start": 4033, "length": 20}]}, "contracts/libraries/range/pool/SnapshotRangeCall.sol": {"SnapshotRangeCall": [{"start": 3032, "length": 20}]}}}, "deployedBytecode": {"object": "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__$6dea53a5b1a3c14df3d68f8a290554c5af$__9063a07aea75906109c857620100196109cd565b620100185b620100166017600d601260008a896040518963ffffffff1660e01b81526004016109fe989796959493929190613516565b60006040518083038186803b158015610a1657600080fd5b505af4158015610a2a573d6000803e3d6000fd5b5050505050610a388161190b565b5050565b60006109586078611950565b6000806000610a556120c3565b610a5d611556565b6020820152604051635fdf9d5560e01b815273__$9e4c229d336278bda7a6b638eb4f56779a$__90635fdf9d5590610aa9906201001690600d906012906000908c9089906004016135fa565b606060405180830381865af4158015610ac6573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610aea9190613669565b935093509350509193909250565b6000610958607e611973565b60008060008073__$286c9a6ab289b16a925f6a63d27fed5f3d$__6368b3fe3062010017620100166000610b36611556565b8a6040518663ffffffff1660e01b8152600401610b5795949392919061380d565b608060405180830381865af4158015610b74573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610b98919061386b565b93509350935093509193509193565b6000610bb2816116c7565b610bba61170e565b610bc2612163565b610bca611556565b816080018190525073__$1214492daac452055e29b230bdd72e484d$__63f15bc8cc8460800151610bfe5762010019610c03565b620100185b620100166012600088876040518763ffffffff1660e01b81526004016109fe96959493929190613911565b600080610c39611996565b6000610c44816116c7565b610c4c61170e565b73__$f0f62380d12e0e837b46ba5507a2cc3702$__632322cded600086610c71611556565b6040518463ffffffff1660e01b8152600401610c8f93929190613a3f565b6040805180830381865af4158015610cab573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610ccf9190613a9e565b92509250610cdc8161190b565b50915091565b60008073__$06f006a50281b95ada637fcca2c3169e65$__6363fbcd438460800151610d115762010019610d16565b620100185b6201001660126000610d26611556565b896040518763ffffffff1660e01b8152600401610d4896959493929190613ad8565b6040805180830381865af4158015610d64573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906109699190613a9e565b6000610958600061168f565b6000610958603c61168f565b6000610958607c611973565b6000610958602861168f565b6000610dc3816116c7565b610dcb61170e565b610dd860176000846119ee565b610a388161190b565b6000806000610def816116c7565b610df761170e565b610dff6120c3565b610e07611556565b602082015260405163a835b0a360e01b815273__$8397ab196537c2cfcd89b6140849e98c7e$__9063a835b0a390610e56906201001690601790600d906012906000908d908a90600401613b17565b6040805180830381865af4158015610e72573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610e969190613bb4565b9350935050610cdc8161190b565b6000610958606461168f565b6000610958605061168f565b6000610ec7816116c7565b610ecf61170e565b610ed761222e565b610edf611556565b6040808301919091525163bccd276b60e01b815273__$14b14c61645669b42e011afd470eeccf72$__9063bccd276b906109fe9062010017906201001690600d9060179060009089908c90600401613c8b565b606080600080600073__$82e69f90fc4689a77ae11a0614a00f0cb8$__631fb854c06000610f5e611556565b896040518463ffffffff1660e01b8152600401610f7d93929190613d77565b600060405180830381865af4158015610f9a573d6000803e3d6000fd5b505050506040513d6000823e601f3d908101601f19168201604052610fc29190810190613e5f565b939a9299509097509550909350915050565b6000610fdf816116c7565b610fe7611ac6565b610fef61170e565b73__$dc25dd3a5fe6a540f35c01c335c2ccfd23$__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__$16f93f2e1504eec0c942977e18129be78e$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", "sourceMap": "1060:6956:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;605:28:11;;;;;;:::i;:::-;;:::i;:::-;;;;729:10:78;717:23;;;699:42;;788:1;777:21;;;;772:2;757:18;;750:49;-1:-1:-1;;;;;835:32:78;815:18;;;808:60;687:2;672:18;605:28:11;;;;;;;;253:90:10;;;:::i;:::-;;;-1:-1:-1;;;;;1043:32:78;;;1025:51;;1013:2;998:18;253:90:10;879:203:78;6822:159:0;;;;;;:::i;:::-;;:::i;:::-;;;;-1:-1:-1;;;;;1595:15:78;;;1577:34;;1647:15;;;;1642:2;1627:18;;1620:43;1512:18;6822:159:0;1365:304:78;946:51:11;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;;;;;946:51:11;;;-1:-1:-1;;;946:51:11;;;;;-1:-1:-1;;;946:51:11;;;;;;;-1:-1:-1;;;946:51:11;;;;;;-1:-1:-1;;;946:51:11;;;;;;;;;;-1:-1:-1;;;;;2251:47:78;;;2233:66;;2347:10;2335:23;;;2330:2;2315:18;;2308:51;2406:1;2395:21;;;2375:18;;;2368:49;;;;2453:21;;2448:2;2433:18;;2426:49;2519:14;2512:22;2506:3;2491:19;;2484:51;2220:3;2205:19;946:51:11;1990:551:78;2849:472:0;;;;;;:::i;:::-;;:::i;:::-;;741:94:10;;;:::i;:::-;;;7466:10:78;7454:23;;;7436:42;;7424:2;7409:18;741:94:10;7292:192:78;4829:399:0;;;;;;:::i;:::-;;:::i;:::-;;;;8484:25:78;;;8540:2;8525:18;;8518:34;;;;-1:-1:-1;;;;;8588:32:78;8568:18;;;8561:60;8472:2;8457:18;4829:399:0;8282:345:78;1416:33:0;;;;;947:90:10;;;:::i;:::-;;;8911:6:78;8899:19;;;8881:38;;8869:2;8854:18;947:90:10;8737:188:78;5656:393:0;;;;;;:::i;:::-;;:::i;:::-;;;;9436:1:78;9425:21;;;;9407:40;;-1:-1:-1;;;;;9483:32:78;;;9478:2;9463:18;;9456:60;-1:-1:-1;;;;;9605:15:78;;;9585:18;;;9578:43;;;;9657:15;9652:2;9637:18;;9630:43;9394:3;9379:19;5656:393:0;9180:499:78;534:27:11;;;;;;;;;;9830:25:78;;;9818:2;9803:18;534:27:11;9684:177:78;3327:426:0;;;;;;:::i;:::-;;:::i;4488:335::-;;;;;;:::i;:::-;;:::i;:::-;;;;-1:-1:-1;;;;;12452:15:78;;;12434:34;;12504:15;;;;12499:2;12484:18;;12477:43;12354:18;4488:335:0;12207:319:78;6055:366:0;;;;;;:::i;:::-;;:::i;159:88:10:-;;;:::i;746:50:11:-;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;746:50:11;;;-1:-1:-1;;;746:50:11;;;;;-1:-1:-1;;;746:50:11;;;;;;;;;;13035:25:78;;;13091:2;13076:18;;13069:34;;;;-1:-1:-1;;;;;13139:47:78;;;13119:18;;;13112:75;;;;13234:1;13223:21;;;13218:2;13203:18;;13196:49;13282:21;;13276:3;13261:19;;13254:50;13022:3;13007:19;746:50:11;12784:526:78;463:27:11;;;;;;;846:51;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;;;;;846:51:11;;;-1:-1:-1;;;846:51:11;;;;;-1:-1:-1;;;846:51:11;;;;;;;-1:-1:-1;;;846:51:11;;;;;;-1:-1:-1;;;846:51:11;;;;;;1455:32:0;;;;;445:93:10;;;:::i;841:100::-;;;:::i;:::-;;;13582:1:78;13571:21;;;;13553:40;;13541:2;13526:18;841:100:10;13411:188:78;349:90:10;;;:::i;4219:263:0:-;;;;;;:::i;:::-;;:::i;3759:454::-;;;;;;:::i;:::-;;:::i;:::-;;;;15159:25:78;;;15215:2;15200:18;;15193:34;;;;15132:18;3759:454:0;14989:244:78;642:93:10;;;:::i;544:92::-;;;:::i;373:30:11:-;;;;;;;;;-1:-1:-1;373:30:11;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;373:30:11;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;373:30:11;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;373:30:11;;;;;;;;;;;-1:-1:-1;;;373:30:11;;;;;;;-1:-1:-1;373:30:11;;;-1:-1:-1;;;373:30:11;;;;-1:-1:-1;373:30:11;;;-1:-1:-1;;;373:30:11;;;;-1:-1:-1;373:30:11;;;-1:-1:-1;;;373:30:11;;;;;-1:-1:-1;373:30:11;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;373:30:11;;;;;;-1:-1:-1;;;373:30:11;;;;;;;;;;;;;;;;;;;:::i;1997:412:0:-;;;;;;:::i;:::-;;:::i;669:35:11:-;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;669:35:11;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;669:35:11;;;;;;;-1:-1:-1;;;669:35:11;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;669:35:11;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;5234:416:0:-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;;:::i;1613:378::-;;;;;;:::i;:::-;;:::i;2415:412::-;;;;;;:::i;:::-;;:::i;6427:389::-;;;:::i;:::-;;;;;;;:::i;605:28:11:-;;;;;;;;;;;;;;;;;;-1:-1:-1;605:28:11;;;;;;-1:-1:-1;;;605:28:11;;-1:-1:-1;;;;;605:28:11;;:::o;253:90:10:-;292:7;318:18;333:2;318:14;:18::i;:::-;311:25;;253:90;:::o;6822:159:0:-;6899:7;6908;6934:40;6962:11;6934:27;:40::i;:::-;6927:47;;;;6822:159;;;:::o;2849:472::-;2944:11;2591:26:13;2611:5;2591:19;:26::i;:::-;1370:22:0::1;:20;:22::i;:::-;2993:27:::2;;:::i;:::-;3048:12;:10;:12::i;:::-;3030:15;::::0;::::2;:30:::0;3105:17:::2;::::0;::::2;::::0;3070:13:::2;::::0;:21:::2;::::0;3105:43:::2;;3138:10;3105:43;;;3125:10;3105:43;3162:5;3181:7;3202:12;3228;3254:11;3279:6;3299:5;3070:244;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;::::0;::::2;;;;;;;;;2983:338;2638:25:13::0;2657:5;2638:18;:25::i;:::-;2849:472:0;;:::o;741:94:10:-;785:6;810:18;824:3;810:13;:18::i;4829:399:0:-;4922:7;4939;4956;4980:22;;:::i;:::-;5030:12;:10;:12::i;:::-;5012:15;;;:30;5059:162;;-1:-1:-1;;;5059:162:0;;:9;;:17;;:162;;5090:5;;5109:12;;5135;;5161:11;;5186:6;;5012:5;;5059:162;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5052:169;;;;;;;4829:399;;;;;:::o;947:90:10:-;987:6;1012:18;1026:3;1012:13;:18::i;5656:393:0:-;5746:24;5780:32;5822:17;5849;5890;:25;5929:9;5952:5;5971:11;5996:12;:10;:12::i;:::-;6022:10;5890:152;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5883:159;;;;;;;;5656:393;;;;;:::o;3327:426::-;3422:11;2591:26:13;2611:5;2591:19;:26::i;:::-;1370:22:0::1;:20;:22::i;:::-;3471:27:::2;;:::i;:::-;3526:12;:10;:12::i;:::-;3508:5;:15;;:30;;;;3548:13;:21;3583:6;:17;;;:43;;3616:10;3583:43;;;3603:10;3583:43;3640:5;3659:12;3685:11;3710:6;3731:5;3548:198;;;;;;;;;;;;;;;;;;;;:::i;4488:335::-:0;4649:18;4677;1228:12;:10;:12::i;:::-;4591:11:::1;2591:26:13;2611:5;2591:19;:26::i;:::-;1370:22:0::2;:20;:22::i;:::-;4719:8:::3;:16;4749:11;4774:6;4794:12;:10;:12::i;:::-;4719:97;;;;;;;;;;;;;;;;;:::i;:::-;;::::0;::::3;;;;;;;;;;;;;;;::::0;::::3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4712:104;;;;2638:25:13::1;2657:5;2638:18;:25::i;:::-;1250:1:0;4488:335:::0;;;:::o;6055:366::-;6158:7;6175;6206:17;:25;6245:6;:17;;;:43;;6278:10;6245:43;;;6265:10;6245:43;6302:5;6321:12;6347:11;6372:12;:10;:12::i;:::-;6398:6;6206:208;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;159:88:10:-;197:7;223:17;238:1;223:14;:17::i;445:93::-;487:7;513:18;528:2;513:14;:18::i;841:100::-;885:5;915:18;929:3;915:13;:18::i;349:90::-;388:7;414:18;429:2;414:14;:18::i;4219:263:0:-;4319:11;2591:26:13;2611:5;2591:19;:26::i;:::-;1370:22:0::1;:20;:22::i;:::-;4369:106:::2;4397:7;4418:11;4448:17:::0;4369:14:::2;:106::i;:::-;2638:25:13::0;2657:5;2638:18;:25::i;3759:454:0:-;3901:6;3917;3844:11;2591:26:13;2611:5;2591:19;:26::i;:::-;1370:22:0::1;:20;:22::i;:::-;3945::::2;;:::i;:::-;3995:12;:10;:12::i;:::-;3977:15;::::0;::::2;:30:::0;4024:182:::2;::::0;-1:-1:-1;;;4024:182:0;;:8:::2;::::0;:16:::2;::::0;:182:::2;::::0;4054:5:::2;::::0;4073:7:::2;::::0;4094:12:::2;::::0;4120::::2;::::0;4146:11:::2;::::0;4171:6;;3977:5;;4024:182:::2;;;:::i;:::-;;::::0;::::2;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4017:189;;;;;2638:25:13::0;2657:5;2638:18;:25::i;642:93:10:-;683:7;709:19;724:3;709:14;:19::i;544:92::-;585:7;611:18;626:2;611:14;:18::i;1997:412:0:-;2092:11;2591:26:13;2611:5;2591:19;:26::i;:::-;1370:22:0::1;:20;:22::i;:::-;2141:27:::2;;:::i;:::-;2196:12;:10;:12::i;:::-;2178:15;::::0;;::::2;:30:::0;;;;2218:184;-1:-1:-1;;;2218:184:0;;:13:::2;::::0;:21:::2;::::0;:184:::2;::::0;2253:9:::2;::::0;2276:5:::2;::::0;2295:12:::2;::::0;2321:7:::2;::::0;2342:11:::2;::::0;2178:5;;2386:6;;2218:184:::2;;;:::i;5234:416::-:0;5336:33;5379:41;5430:20;5460:24;5494:17;5540:10;:18;5572:11;5597:12;:10;:12::i;:::-;5623:10;5540:103;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5540:103:0;;;;;;;;;;;;:::i;:::-;5533:110;;;;-1:-1:-1;5533:110:0;;-1:-1:-1;5533:110:0;-1:-1:-1;5533:110:0;;-1:-1:-1;5234:416:0;-1:-1:-1;;5234:416:0:o;1613:378::-;1699:11;2591:26:13;2611:5;2591:19;:26::i;:::-;1297:14:0::1;:12;:14::i;:::-;1370:22:::2;:20;:22::i;:::-;1810:5:::3;:16;1840:12;1866;1892:7;1913:11;1938:12;:10;:12::i;:::-;1964:10;1810:174;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;::::0;::::3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1796:11;:188:::0;::::3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;-1:-1:-1::0;;;;;1796:188:0::3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2638:25:13::0;2657:5;2638:18;:25::i;2415:412:0:-;2510:11;2591:26:13;2611:5;2591:19;:26::i;:::-;1370:22:0::1;:20;:22::i;:::-;2559:27:::2;;:::i;:::-;2614:12;:10;:12::i;:::-;2596:15;::::0;;::::2;:30:::0;;;;2636:184;-1:-1:-1;;;2636:184:0;;:13:::2;::::0;:21:::2;::::0;:184:::2;::::0;2671:9:::2;::::0;2694:5:::2;::::0;2713:12:::2;::::0;2739:7:::2;::::0;2760:11:::2;::::0;2596:5;;2804:6;;2636:184:::2;;;:::i;6427:389::-:0;6479:22;;:::i;:::-;6525:284;;;;;;;;6554:7;:5;:7::i;:::-;-1:-1:-1;;;;;6525:284:0;;;;;6575:8;-1:-1:-1;;;;;6525:284:0;;;;;6597:7;-1:-1:-1;;;;;6525:284:0;;;;;6618:35;;;;;;;;6630:10;:8;:10::i;:::-;-1:-1:-1;;;;;6618:35:0;;;;;6642:10;:8;:10::i;:::-;-1:-1:-1;;;;;6618:35:0;;;6525:284;;;;6667:8;:6;:8::i;:::-;-1:-1:-1;;;;;6525:284:0;;;;;6689:8;:6;:8::i;:::-;-1:-1:-1;;;;;6525:284:0;;;;;6711:11;:9;:11::i;:::-;-1:-1:-1;;;;;6525:284:0;;;;;6736:13;:11;:13::i;:::-;6525:284;;;;;;6763:13;:11;:13::i;:::-;6525:284;;;;;;6790:9;:7;:9::i;:::-;6525:284;;;;6518:291;6427:389;-1:-1:-1;6427:389:0:o;1110:275:15:-;1176:11;1199:14;1216:25;13904:14;-1:-1:-1;;13900:22:15;;13887:36;13882:3;13878:46;13858:67;;;13698:243;1216:25;1345:22;;;;1332:36;1328:2;1324:45;;1110:275;-1:-1:-1;;1110:275:15:o;6051:187:48:-;6137:7;6154;6186:21;6195:11;6186:8;:21::i;:::-;6209;6218:11;6209:8;:21::i;2676:349:13:-;2829:14;;;;-1:-1:-1;;;;;2829:14:13;;;:26;:14;:26;2825:94;;2878:30;;-1:-1:-1;;;2878:30:13;;;;;;;;;;;2825:94;2993:14;;:25;;-1:-1:-1;;;;2993:25:13;-1:-1:-1;;;2993:25:13;;;2676:349::o;7101:795:0:-;7184:11;7219:8;7229;:6;:8::i;:::-;7239;:6;:8::i;:::-;7249:9;:7;:9::i;:::-;7208:51;;;-1:-1:-1;;;;;62315:15:78;;;7208:51:0;;;62297:34:78;62367:15;;;62347:18;;;62340:43;62419:15;;62399:18;;;62392:43;62483:6;62471:19;;;62451:18;;;62444:47;62231:19;;7208:51:0;;;;;;;;;;;;7198:62;;;;;;7184:76;;7321:24;7348:401;7398:8;7454:7;:5;:7::i;:::-;7479:8;:6;:8::i;:::-;7505;:6;:8::i;:::-;7531:11;:9;:11::i;:::-;7560:10;:8;:10::i;:::-;7588;:8;:10::i;:::-;7616:13;:11;:13::i;:::-;7647;:11;:13::i;:::-;7678:9;:7;:9::i;:::-;7420:281;;63021:2:78;63048:15;;;-1:-1:-1;;63044:24:78;;;7420:281:0;;;63032:37:78;63103:15;;;63099:24;;63085:12;;;63078:46;63158:15;;;63154:24;;63140:12;;;63133:46;63213:15;;;63209:24;;63195:12;;;63188:46;63268:15;;;63264:24;;63250:12;;;63243:46;63324:15;;63320:24;;;63305:13;;;63298:47;63402:3;63380:16;;;;-1:-1:-1;;;;;;63376:43:78;63361:13;;;63354:66;63455:3;63451:16;;;63436:13;;;63429:39;62571:15;;;-1:-1:-1;;;;;;62567:37:78;63503:13;;;62555:50;63533:13;;7420:281:0;;;;;;;;;;;;7715:3;7732:7;7348:36;:401::i;:::-;7321:428;-1:-1:-1;7828:4:0;-1:-1:-1;;;;;7820:33:0;;;7816:73;;7855:34;;-1:-1:-1;;;7855:34:0;;63759:2:78;7855:34:0;;;63741:21:78;63798:2;63778:18;;;63771:30;-1:-1:-1;;;63817:18:78;;;63810:46;63873:18;;7855:34:0;;;;;;;;3031:319:13;3108:14;;;;-1:-1:-1;;;3108:14:13;;:26;:14;1840:1;3108:26;3104:68;;3143:29;;-1:-1:-1;;;3143:29:13;;;;;;;;;;;3104:68;3314:14;;:29;;-1:-1:-1;;;;3314:29:13;-1:-1:-1;;;3314:29:13;;;3031:319::o;12343:274:15:-;12408:10;12430:14;12447:25;13904:14;-1:-1:-1;;13900:22:15;;13887:36;13882:3;13878:46;13858:67;;;13698:243;12447:25;12577:22;;;;12564:36;12559:3;12555:46;;12343:274;-1:-1:-1;;12343:274:15:o;13011:::-;13076:10;13098:14;13115:25;13904:14;-1:-1:-1;;13900:22:15;;13887:36;13882:3;13878:46;13858:67;;;13698:243;13115:25;13245:22;;;;13232:36;13227:3;13223:46;;13011:274;-1:-1:-1;;13011:274:15:o;6987:108:0:-;7050:7;:5;:7::i;:::-;-1:-1:-1;;;;;7036:21:0;:10;-1:-1:-1;;;;;7036:21:0;;7032:56;;7059:29;;-1:-1:-1;;;7059:29:0;;64104:2:78;7059:29:0;;;64086:21:78;64143:2;64123:18;;;64116:30;-1:-1:-1;;;64162:18:78;;;64155:41;64213:18;;7059:29:0;63902:335:78;7059:29:0;6987:108::o;2345:482:38:-;2555:21;;;;;;;;;2534:42;;;;2530:56;;2345:482;;;:::o;2530:56::-;2611:21;;;;;;;2595:121;2638:17;2634:21;;:1;:21;;;2595:121;;;2704:1;2676:7;2684:1;2676:10;;;;;;;;;:::i;:::-;;:29;;-1:-1:-1;;2676:29:38;;;;;;;;;;;;2657:3;;;;:::i;:::-;;;;2595:121;;;-1:-1:-1;2725:41:38;;-1:-1:-1;;2725:41:38;;;;;;;;;;;;;;2781:39;;8881:38:78;;;2781:39:38;;8869:2:78;8854:18;2781:39:38;;;;;;;2345:482;;;:::o;7902:112:0:-;7953:10;-1:-1:-1;;;;;7967:7:0;7953:21;;7949:58;;7976:31;;-1:-1:-1;;;7976:31:0;;64910:2:78;7976:31:0;;;64892:21:78;64949:2;64929:18;;;64922:30;-1:-1:-1;;;64968:18:78;;;64961:43;65021:18;;7976:31:0;64708:337:78;6244:279:48;6327:13;6357:50;;:::i;:::-;6417:35;;;;:21;;;:35;6469:47;6484:20;6441:11;6484:7;:20::i;:::-;6506:9;6469:14;:47::i;:::-;6462:54;6244:279;-1:-1:-1;;;6244:279:48:o;6529:::-;6612:13;6642:50;;:::i;:::-;6702:35;;;;:21;;;:35;6754:47;6769:20;6726:11;6769:7;:20::i;6482:329:16:-;-1:-1:-1;;4679:15:16;;4673:22;;-1:-1:-1;;4730:15:16;;4724:22;;-1:-1:-1;;4781:15:16;;4775:22;;4828:11;;4867:32;;;4791:4;4867:32;4927:14;;5151:28;5138:42;;-1:-1:-1;;5256:15:16;;5249:39;;;5071:1;5055:18;;5412:4;5408:22;;;5432:52;5405:80;-1:-1:-1;;5372:15:16;;5348:151;5603:66;-1:-1:-1;;5586:15:16;;5562:121;5766:22;;;5760:4;5756:33;5791:38;5753:77;-1:-1:-1;;5720:15:16;;5696:148;5877:4;5873:22;5857:39;;5997:22;;;-1:-1:-1;;5980:15:16;;5970:50;6100:24;;6137;;;6174:33;;6220;;6266;;-1:-1:-1;;6755:49:16;6783:4;6789;6795:8;6755:27;:49::i;:::-;6743:61;6482:329;-1:-1:-1;;;;;;6482:329:16:o;5721:159:48:-;5803:10;5837:36;;;;:22;:36;-1:-1:-1;;5837:22:48;:::i;:::-;:36;;;;:::i;:::-;5830:43;5721:159;-1:-1:-1;;5721:159:48:o;7865:2864::-;8006:13;8036:15;8061:1;8054:4;:8;;;:57;;8105:4;8098:12;;8054:57;;;8081:4;8074:12;;8073:13;;;:::i;:::-;8036:75;;8150:30;8158:9;:21;;;8150:7;:30::i;:::-;8135:47;;8125:7;:57;8121:99;;;8184:36;;-1:-1:-1;;;8184:36:48;;66549:2:78;8184:36:48;;;66531:21:78;66588:2;66568:18;;;66561:30;-1:-1:-1;;;66607:18:78;;;66600:47;66664:18;;8184:36:48;66347:341:78;8184:36:48;8254:13;8270:7;8280:3;8270:13;8287:1;8270:18;:125;;-1:-1:-1;;;8270:125:48;;;8307:34;8270:125;8254:141;;;-1:-1:-1;8423:3:48;8413:13;;:18;8409:83;;8450:34;8442:42;8489:3;8441:51;8409:83;8520:3;8510:13;;:18;8506:83;;8547:34;8539:42;8586:3;8538:51;8506:83;8617:3;8607:13;;:18;8603:83;;8644:34;8636:42;8683:3;8635:51;8603:83;8714:4;8704:14;;:19;8700:84;;8742:34;8734:42;8781:3;8733:51;8700:84;8812:4;8802:14;;:19;8798:84;;8840:34;8832:42;8879:3;8831:51;8798:84;8910:4;8900:14;;:19;8896:84;;8938:34;8930:42;8977:3;8929:51;8896:84;9008:4;8998:14;;:19;8994:84;;9036:34;9028:42;9075:3;9027:51;8994:84;9106:5;9096:15;;:20;9092:85;;9135:34;9127:42;9174:3;9126:51;9092:85;9205:5;9195:15;;:20;9191:85;;9234:34;9226:42;9273:3;9225:51;9191:85;9304:5;9294:15;;:20;9290:85;;9333:34;9325:42;9372:3;9324:51;9290:85;9403:5;9393:15;;:20;9389:85;;9432:34;9424:42;9471:3;9423:51;9389:85;9502:6;9492:16;;:21;9488:86;;9532:34;9524:42;9571:3;9523:51;9488:86;9602:6;9592:16;;:21;9588:86;;9632:34;9624:42;9671:3;9623:51;9588:86;9702:6;9692:16;;:21;9688:86;;9732:34;9724:42;9771:3;9723:51;9688:86;9802:6;9792:16;;:21;9788:86;;9832:34;9824:42;9871:3;9823:51;9788:86;9902:7;9892:17;;:22;9888:86;;9933:33;9925:41;9971:3;9924:50;9888:86;10002:7;9992:17;;:22;9988:85;;10033:32;10025:40;10070:3;10024:49;9988:85;10101:7;10091:17;;:22;10087:83;;10132:30;10124:38;10167:3;10123:47;10087:83;10198:7;10188:17;;:22;10184:78;;10229:25;10221:33;10259:3;10220:42;10184:78;10288:1;10281:4;:8;;;10277:47;;;10319:5;-1:-1:-1;;10299:25:48;;;;;:::i;:::-;;10291:33;;10277:47;10689:7;10680:5;:17;:22;:30;;10709:1;10680:30;;;10705:1;10680:30;10663:48;;10673:2;10664:5;:11;;10663:48;10647:65;;8230:2493;8026:2703;7865:2864;;;;:::o;5886:159::-;5968:10;6002:36;;;;;5670:9;-1:-1:-1;;5670:9:48;:::i;:::-;6002:22;;;;:::i;7236:604:16:-;7366:17;7531:4;7525;7517:19;-1:-1:-1;7577:4:16;7570:18;;;7618:2;7614:17;7608:4;7601:31;7652:4;7645:18;7705:4;7699;7689:21;;;7809:15;;7689:21;7236:604::o;-1:-1:-1:-;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;14:180:78:-;73:6;126:2;114:9;105:7;101:23;97:32;94:52;;;142:1;139;132:12;94:52;-1:-1:-1;165:23:78;;14:180;-1:-1:-1;14:180:78:o;1087:273::-;1144:6;1197:2;1185:9;1176:7;1172:23;1168:32;1165:52;;;1213:1;1210;1203:12;1165:52;1252:9;1239:23;1305:5;1302:1;1291:20;1284:5;1281:31;1271:59;;1326:1;1323;1316:12;2546:127;2607:10;2602:3;2598:20;2595:1;2588:31;2638:4;2635:1;2628:15;2662:4;2659:1;2652:15;2678:255;2750:2;2744:9;2792:6;2780:19;;-1:-1:-1;;;;;2814:34:78;;2850:22;;;2811:62;2808:88;;;2876:18;;:::i;:::-;2912:2;2905:22;2678:255;:::o;2938:253::-;3010:2;3004:9;3052:4;3040:17;;-1:-1:-1;;;;;3072:34:78;;3108:22;;;3069:62;3066:88;;;3134:18;;:::i;3196:253::-;3268:2;3262:9;3310:4;3298:17;;-1:-1:-1;;;;;3330:34:78;;3366:22;;;3327:62;3324:88;;;3392:18;;:::i;3454:253::-;3526:2;3520:9;3568:4;3556:17;;-1:-1:-1;;;;;3588:34:78;;3624:22;;;3585:62;3582:88;;;3650:18;;:::i;3712:255::-;3784:2;3778:9;3826:6;3814:19;;-1:-1:-1;;;;;3848:34:78;;3884:22;;;3845:62;3842:88;;;3910:18;;:::i;3972:275::-;4043:2;4037:9;4108:2;4089:13;;-1:-1:-1;;4085:27:78;4073:40;;-1:-1:-1;;;;;4128:34:78;;4164:22;;;4125:62;4122:88;;;4190:18;;:::i;:::-;4226:2;4219:22;3972:275;;-1:-1:-1;3972:275:78:o;4252:131::-;-1:-1:-1;;;;;4327:31:78;;4317:42;;4307:70;;4373:1;4370;4363:12;4307:70;4252:131;:::o;4388:134::-;4456:20;;4485:31;4456:20;4485:31;:::i;:::-;4388:134;;;:::o;4527:146::-;-1:-1:-1;;;;;4606:5:78;4602:46;4595:5;4592:57;4582:85;;4663:1;4660;4653:12;4678:134;4746:20;;4775:31;4746:20;4775:31;:::i;4817:179::-;4884:20;;4944:26;4933:38;;4923:49;;4913:77;;4986:1;4983;4976:12;5001:121;5086:10;5079:5;5075:22;5068:5;5065:33;5055:61;;5112:1;5109;5102:12;5127:132;5194:20;;5223:30;5194:20;5223:30;:::i;5264:118::-;5351:5;5348:1;5337:20;5330:5;5327:31;5317:59;;5372:1;5369;5362:12;5387:130;5453:20;;5482:29;5453:20;5482:29;:::i;5522:160::-;5587:20;;5643:13;;5636:21;5626:32;;5616:60;;5672:1;5669;5662:12;5687:530;5729:5;5782:3;5775:4;5767:6;5763:17;5759:27;5749:55;;5800:1;5797;5790:12;5749:55;5836:6;5823:20;-1:-1:-1;;;;;5858:2:78;5855:26;5852:52;;;5884:18;;:::i;:::-;5928:55;5971:2;5952:13;;-1:-1:-1;;5948:27:78;5977:4;5944:38;5928:55;:::i;:::-;6008:2;5999:7;5992:19;6054:3;6047:4;6042:2;6034:6;6030:15;6026:26;6023:35;6020:55;;;6071:1;6068;6061:12;6020:55;6136:2;6129:4;6121:6;6117:17;6110:4;6101:7;6097:18;6084:55;6184:1;6159:16;;;6177:4;6155:27;6148:38;;;;6163:7;5687:530;-1:-1:-1;;;5687:530:78:o;6222:1065::-;6314:6;6367:2;6355:9;6346:7;6342:23;6338:32;6335:52;;;6383:1;6380;6373:12;6335:52;6423:9;6410:23;-1:-1:-1;;;;;6493:2:78;6485:6;6482:14;6479:34;;;6509:1;6506;6499:12;6479:34;6532:22;;;;6588:6;6570:16;;;6566:29;6563:49;;;6608:1;6605;6598:12;6563:49;6634:22;;:::i;:::-;6679;6698:2;6679:22;:::i;:::-;6672:5;6665:37;6734:31;6761:2;6757;6753:11;6734:31;:::i;:::-;6729:2;6722:5;6718:14;6711:55;6798:30;6824:2;6820;6816:11;6798:30;:::i;:::-;6793:2;6786:5;6782:14;6775:54;6861:30;6887:2;6883;6879:11;6861:30;:::i;:::-;6856:2;6849:5;6845:14;6838:54;6925:30;6950:3;6946:2;6942:12;6925:30;:::i;:::-;6919:3;6912:5;6908:15;6901:55;6989:30;7014:3;7010:2;7006:12;6989:30;:::i;:::-;6983:3;6976:5;6972:15;6965:55;7053:29;7077:3;7073:2;7069:12;7053:29;:::i;:::-;7047:3;7040:5;7036:15;7029:54;7129:3;7125:2;7121:12;7108:26;7159:2;7149:8;7146:16;7143:36;;;7175:1;7172;7165:12;7143:36;7212:44;7248:7;7237:8;7233:2;7229:17;7212:44;:::i;:::-;7206:3;7195:15;;7188:69;-1:-1:-1;7199:5:78;6222:1065;-1:-1:-1;;;;;6222:1065:78:o;7489:788::-;7577:6;7630:3;7618:9;7609:7;7605:23;7601:33;7598:53;;;7647:1;7644;7637:12;7598:53;7680:2;7674:9;7722:3;7714:6;7710:16;7792:6;7780:10;7777:22;-1:-1:-1;;;;;7744:10:78;7741:34;7738:62;7735:88;;;7803:18;;:::i;:::-;7839:2;7832:22;7876:23;;7908:31;7876:23;7908:31;:::i;:::-;7948:21;;8021:2;8006:18;;7993:32;8034:33;7993:32;8034:33;:::i;:::-;8095:2;8083:15;;8076:32;8141:35;8172:2;8157:18;;8141:35;:::i;:::-;8136:2;8128:6;8124:15;8117:60;8210:35;8241:2;8230:9;8226:18;8210:35;:::i;:::-;8205:2;8193:15;;8186:60;8197:6;7489:788;-1:-1:-1;;;7489:788:78:o;8930:245::-;8988:6;9041:2;9029:9;9020:7;9016:23;9012:32;9009:52;;;9057:1;9054;9047:12;9009:52;9096:9;9083:23;9115:30;9139:5;9115:30;:::i;9866:779::-;9928:5;9976:4;9964:9;9959:3;9955:19;9951:30;9948:50;;;9994:1;9991;9984:12;9948:50;10016:22;;:::i;:::-;10007:31;;10075:9;10062:23;10094:33;10119:7;10094:33;:::i;:::-;10136:22;;10210:2;10195:18;;10182:32;10223:33;10182:32;10223:33;:::i;:::-;10283:2;10272:14;;10265:31;10348:2;10333:18;;10320:32;10361;10320;10361;:::i;:::-;10420:2;10409:14;;10402:31;10485:2;10470:18;;10457:32;10498:31;10457:32;10498:31;:::i;:::-;10556:2;10545:14;;10538:31;10602:36;10633:3;10618:19;;10602:36;:::i;:::-;10596:3;10589:5;10585:15;10578:61;9866:779;;;;:::o;10650:244::-;10742:6;10795:3;10783:9;10774:7;10770:23;10766:33;10763:53;;;10812:1;10809;10802:12;10763:53;10835;10880:7;10869:9;10835:53;:::i;10899:117::-;10984:6;10977:5;10973:18;10966:5;10963:29;10953:57;;11006:1;11003;10996:12;11021:114;11105:4;11098:5;11094:16;11087:5;11084:27;11074:55;;11125:1;11122;11115:12;11140:1062;11227:6;11280:3;11268:9;11259:7;11255:23;11251:33;11248:53;;;11297:1;11294;11287:12;11248:53;11330:2;11324:9;11372:3;11364:6;11360:16;11442:6;11430:10;11427:22;-1:-1:-1;;;;;11394:10:78;11391:34;11388:62;11385:88;;;11453:18;;:::i;:::-;11489:2;11482:22;11526:23;;11558:30;11526:23;11558:30;:::i;:::-;11597:21;;11670:2;11655:18;;11642:32;11683;11642;11683;:::i;:::-;11743:2;11731:15;;11724:32;11808:2;11793:18;;11780:32;11821;11780;11821;:::i;:::-;11881:2;11869:15;;11862:32;11946:2;11931:18;;11918:32;11959;11918;11959;:::i;:::-;12019:2;12007:15;;12000:32;12084:3;12069:19;;12056:33;12098:31;12056:33;12098:31;:::i;:::-;12157:3;12145:16;;12138:33;12149:6;11140:1062;-1:-1:-1;;;11140:1062:78:o;13604:245::-;13662:6;13715:2;13703:9;13694:7;13690:23;13686:32;13683:52;;;13731:1;13728;13721:12;13683:52;13770:9;13757:23;13789:30;13813:5;13789:30;:::i;13854:1130::-;13941:6;13994:2;13982:9;13973:7;13969:23;13965:32;13962:52;;;14010:1;14007;14000:12;13962:52;14050:9;14037:23;-1:-1:-1;;;;;14120:2:78;14112:6;14109:14;14106:34;;;14136:1;14133;14126:12;14106:34;14159:22;;;;14215:4;14197:16;;;14193:27;14190:47;;;14233:1;14230;14223:12;14190:47;14259:22;;:::i;:::-;14318:2;14305:16;14330:33;14355:7;14330:33;:::i;:::-;14372:22;;14439:2;14431:11;;14418:25;14452:33;14418:25;14452:33;:::i;:::-;14512:2;14501:14;;14494:31;14570:2;14562:11;;14549:25;14583:33;14549:25;14583:33;:::i;:::-;14643:2;14632:14;;14625:31;14688:28;14712:2;14704:11;;14688:28;:::i;:::-;14683:2;14676:5;14672:14;14665:52;14750:29;14774:3;14770:2;14766:12;14750:29;:::i;:::-;14744:3;14737:5;14733:15;14726:54;14826:3;14822:2;14818:12;14805:26;14856:2;14846:8;14843:16;14840:36;;;14872:1;14869;14862:12;14840:36;14909:44;14945:7;14934:8;14930:2;14926:17;14909:44;:::i;:::-;14903:3;14892:15;;14885:69;-1:-1:-1;14896:5:78;13854:1130;-1:-1:-1;;;;;13854:1130:78:o;15820:489::-;15904:12;;-1:-1:-1;;;;;15900:38:78;15888:51;;15985:4;15974:16;;;15968:23;-1:-1:-1;;;;;16076:21:78;;;16060:14;;;16053:45;;;;16151:4;16140:16;;;16134:23;16130:32;;;16114:14;;;16107:56;16216:4;16205:16;;;16199:23;16224:6;16195:36;16179:14;;;16172:60;16295:4;16284:16;;;16278:23;16275:1;16264:38;16248:14;;16241:62;15820:489::o;16394:2056::-;16923:13;;15552:12;;15521:6;15548:21;;;15536:34;;15623:4;15612:16;;;15606:23;15602:32;;15586:14;;;15579:56;15688:4;15677:16;;;15671:23;15667:32;15651:14;;;15644:56;16880:3;16865:19;;16995:4;16983:17;;16977:24;-1:-1:-1;;;;;15777:31:78;;17058:4;17043:20;;15765:44;-1:-1:-1;17113:4:78;17101:17;;17095:24;-1:-1:-1;;;;;15777:31:78;;17178:4;17163:20;;15765:44;-1:-1:-1;17233:4:78;17221:17;;17215:24;-1:-1:-1;;;;;460:31:78;;17298:4;17283:20;;448:44;-1:-1:-1;17353:4:78;17341:17;;17335:24;-1:-1:-1;;;;;460:31:78;;17418:4;17403:20;;448:44;-1:-1:-1;17473:4:78;17461:17;;17455:24;-1:-1:-1;;;;;1740:46:78;;17538:4;17523:20;;1728:59;17488:56;17593:4;17585:6;17581:17;17575:24;17618:6;17633:52;17681:2;17670:9;17666:18;17650:14;373:1;362:20;350:33;;298:91;17633:52;17734:4;17726:6;17722:17;17716:24;17694:46;;17759:6;17774:52;17822:2;17811:9;17807:18;17791:14;1873:1;1862:20;1850:33;;1798:91;17774:52;17863:15;;;17857:22;8718:6;8707:18;;;17947:6;17932:22;;8695:31;17992:15;;;17986:22;8707:18;18076:6;18061:22;;8695:31;-1:-1:-1;18093:61:78;18149:3;18134:19;;18126:6;18093:61;:::i;:::-;18163;18219:3;18208:9;18204:19;18196:6;18163:61;:::i;:::-;-1:-1:-1;;;;;1740:46:78;;18275:3;18260:19;;1728:59;275:10;264:22;;18330:3;18315:19;;252:35;275:10;264:22;;18385:3;18370:19;;252:35;16381:4;16370:16;;18439:3;18424:19;;16358:29;18399:45;16394:2056;;;;;;;;;;:::o;18455:1001::-;18547:6;18600:2;18588:9;18579:7;18575:23;18571:32;18568:52;;;18616:1;18613;18606:12;18568:52;18656:9;18643:23;-1:-1:-1;;;;;18726:2:78;18718:6;18715:14;18712:34;;;18742:1;18739;18732:12;18712:34;18765:22;;;;18821:4;18803:16;;;18799:27;18796:47;;;18839:1;18836;18829:12;18796:47;18865:22;;:::i;:::-;18910;18929:2;18910:22;:::i;:::-;18903:5;18896:37;18965:29;18990:2;18986;18982:11;18965:29;:::i;:::-;18960:2;18953:5;18949:14;18942:53;19027:29;19052:2;19048;19044:11;19027:29;:::i;:::-;19022:2;19015:5;19011:14;19004:53;19089:30;19115:2;19111;19107:11;19089:30;:::i;:::-;19084:2;19077:5;19073:14;19066:54;19153:32;19180:3;19176:2;19172:12;19153:32;:::i;:::-;19147:3;19140:5;19136:15;19129:57;19219:32;19246:3;19242:2;19238:12;19219:32;:::i;:::-;19213:3;19206:5;19202:15;19195:57;19298:3;19294:2;19290:12;19277:26;19328:2;19318:8;19315:16;19312:36;;;19344:1;19341;19334:12;19312:36;19381:44;19417:7;19406:8;19402:2;19398:17;19381:44;:::i;:::-;19375:3;19364:15;;19357:69;-1:-1:-1;19368:5:78;18455:1001;-1:-1:-1;;;;;18455:1001:78:o;19461:243::-;19518:6;19571:2;19559:9;19550:7;19546:23;19542:32;19539:52;;;19587:1;19584;19577:12;19539:52;19626:9;19613:23;19645:29;19668:5;19645:29;:::i;19807:289::-;19886:12;;-1:-1:-1;;;;;19882:38:78;19870:51;;19985:4;19974:16;;;19968:23;19964:2;19953:39;19937:14;;;19930:63;20046:4;20035:16;;;20029:23;-1:-1:-1;;;;;20025:64:78;20009:14;;20002:88;19807:289::o;20101:855::-;20444:13;;-1:-1:-1;;;;;20440:22:78;;;20422:41;;20523:4;20511:17;;;20505:24;20501:33;;;20479:20;;;20472:63;20595:4;20583:17;;;20577:24;-1:-1:-1;;;;;20573:50:78;20551:20;;;20544:80;20694:4;20682:17;;;20676:24;20673:1;20662:39;20640:20;;;20633:69;20773:4;20761:17;;;20755:24;20751:2;20740:40;20718:20;;;20711:70;20611:3;20829:17;;;20823:24;-1:-1:-1;;;;;20819:65:78;20797:20;;;20790:95;20371:3;20356:19;;20894:56;20945:3;20930:19;;20922:6;20894:56;:::i;20961:182::-;21020:4;-1:-1:-1;;;;;21045:6:78;21042:30;21039:56;;;21075:18;;:::i;:::-;-1:-1:-1;21120:1:78;21116:14;21132:4;21112:25;;20961:182::o;21148:963::-;21231:6;21262:2;21305;21293:9;21284:7;21280:23;21276:32;21273:52;;;21321:1;21318;21311:12;21273:52;21361:9;21348:23;-1:-1:-1;;;;;21386:6:78;21383:30;21380:50;;;21426:1;21423;21416:12;21380:50;21449:22;;21502:4;21494:13;;21490:27;-1:-1:-1;21480:55:78;;21531:1;21528;21521:12;21480:55;21567:2;21554:16;21590:59;21606:42;21645:2;21606:42;:::i;:::-;21590:59;:::i;:::-;21683:15;;;21765:1;21761:10;;;;21753:19;;21749:28;;;21714:12;;;;21789:19;;;21786:39;;;21821:1;21818;21811:12;21786:39;21845:11;;;;21865:216;21881:6;21876:3;21873:15;21865:216;;;21961:3;21948:17;21978:30;22002:5;21978:30;:::i;:::-;22021:18;;21898:12;;;;22059;;;;21865:216;;;22100:5;21148:963;-1:-1:-1;;;;;;;21148:963:78:o;22116:1436::-;22460:3;22473:22;;;22544:13;;22445:19;;;22566:22;;;22412:4;;22642;;22619:3;22604:19;;;22669:15;;;22412:4;22712:184;22726:6;22723:1;22720:13;22712:184;;;22801:13;;22798:1;22787:28;22775:41;;22836:12;;;;22871:15;;;;22748:1;22741:9;22712:184;;;-1:-1:-1;;;22932:19:78;;;22912:18;;;22905:47;23002:13;;23024:21;;;23100:15;;;;23063:12;;;23135:1;23145:215;23161:8;23156:3;23153:17;23145:215;;;23234:15;;-1:-1:-1;;;;;23230:41:78;23216:56;;23333:17;;;;23294:14;;;;23268:1;23180:11;23145:215;;;-1:-1:-1;;;;;;;460:31:78;;23433:2;23418:18;;448:44;23377:5;-1:-1:-1;23391:46:78;;-1:-1:-1;;394:104:78;23391:46;-1:-1:-1;;;;;1740:46:78;;23488:2;23473:18;;1728:59;23501:45;23541:3;23530:9;23526:19;23518:6;1873:1;1862:20;1850:33;;1798:91;23557:247;23616:6;23669:2;23657:9;23648:7;23644:23;23640:32;23637:52;;;23685:1;23682;23675:12;23637:52;23724:9;23711:23;23743:31;23768:5;23743:31;:::i;23809:790::-;23901:6;23954:2;23942:9;23933:7;23929:23;23925:32;23922:52;;;23970:1;23967;23960:12;23922:52;24003:2;23997:9;24045:2;24037:6;24033:15;24114:6;24102:10;24099:22;-1:-1:-1;;;;;24066:10:78;24063:34;24060:62;24057:88;;;24125:18;;:::i;:::-;24161:2;24154:22;24198:23;;24230:31;24198:23;24230:31;:::i;:::-;24270:21;;24343:2;24328:18;;24315:32;24356;24315;24356;:::i;:::-;24416:2;24404:15;;24397:32;24481:2;24466:18;;24453:32;24494:33;24453:32;24494:33;:::i;:::-;24555:2;24543:15;;24536:32;24547:6;23809:790;-1:-1:-1;;;23809:790:78:o;24817:1400::-;25049:13;;-1:-1:-1;;;;;460:31:78;448:44;;25017:3;25002:19;;25121:4;25113:6;25109:17;25103:24;25136:54;25184:4;25173:9;25169:20;25155:12;-1:-1:-1;;;;;460:31:78;448:44;;394:104;25136:54;;25239:4;25231:6;25227:17;25221:24;25254:56;25304:4;25293:9;25289:20;25273:14;-1:-1:-1;;;;;460:31:78;448:44;;394:104;25254:56;;25359:4;25351:6;25347:17;25341:24;25374:67;25435:4;25424:9;25420:20;25404:14;24723:12;;-1:-1:-1;;;;;24719:21:78;;;24707:34;;24794:4;24783:16;;;24777:23;24773:32;24757:14;;24750:56;24604:208;25374:67;-1:-1:-1;25490:4:78;25478:17;;25472:24;-1:-1:-1;;;;;460:31:78;;25555:4;25540:20;;448:44;-1:-1:-1;25610:4:78;25598:17;;25592:24;-1:-1:-1;;;;;460:31:78;;25675:4;25660:20;;448:44;-1:-1:-1;25730:4:78;25718:17;;25712:24;-1:-1:-1;;;;;460:31:78;;25795:4;25780:20;;448:44;25745:56;25850:4;25842:6;25838:17;25832:24;25875:6;25890:53;25939:2;25928:9;25924:18;25908:14;275:10;264:22;252:35;;199:94;25890:53;25980:15;;25974:22;;-1:-1:-1;26015:6:78;26030:52;26063:18;;;25974:22;13390:1;13379:20;13367:33;;13315:91;26030:52;26119:15;;26113:22;8718:6;8707:18;;26203:6;26188:22;;8695:31;26113:22;-1:-1:-1;26144:67:78;24817:1400;;;;:::o;26337:471::-;26378:3;26416:5;26410:12;26443:6;26438:3;26431:19;26468:1;26478:162;26492:6;26489:1;26486:13;26478:162;;;26554:4;26610:13;;;26606:22;;26600:29;26582:11;;;26578:20;;26571:59;26507:12;26478:162;;;26658:6;26655:1;26652:13;26649:87;;;26724:1;26717:4;26708:6;26703:3;26699:16;26695:27;26688:38;26649:87;-1:-1:-1;26790:2:78;26769:15;-1:-1:-1;;26765:29:78;26756:39;;;;26797:4;26752:50;;26337:471;-1:-1:-1;;26337:471:78:o;26813:1917::-;26894:5;26888:12;26909:45;26950:3;26945:2;26939:9;15552:12;;15521:6;15548:21;;;15536:34;;15623:4;15612:16;;;15606:23;15602:32;;15586:14;;;15579:56;15688:4;15677:16;;;15671:23;15667:32;15651:14;;15644:56;15446:260;26909:45;26997:4;26989:13;;26983:20;-1:-1:-1;;;;;15777:31:78;;27054:4;27045:14;;15765:44;-1:-1:-1;27105:4:78;27097:13;;27091:20;-1:-1:-1;;;;;15777:31:78;;27164:4;27155:14;;15765:44;-1:-1:-1;27215:4:78;27207:13;;27201:20;-1:-1:-1;;;;;460:31:78;;27274:4;27265:14;;448:44;-1:-1:-1;27325:4:78;27317:13;;27311:20;-1:-1:-1;;;;;460:31:78;;27384:4;27375:14;;448:44;-1:-1:-1;27435:4:78;27427:13;;27421:20;-1:-1:-1;;;;;1740:46:78;;27494:4;27485:14;;1728:59;27450:50;27545:4;27541:2;27537:13;27531:20;27570:6;27585:46;27627:2;27622:3;27618:12;27602:14;373:1;362:20;350:33;;298:91;27585:46;27676:4;27672:2;27668:13;27662:20;27640:42;;27701:6;27716:46;27758:2;27753:3;27749:12;27733:14;1873:1;1862:20;1850:33;;1798:91;27716:46;27799:11;;;27793:18;8718:6;8707:18;;;27873:6;27864:16;;8695:31;27918:11;;27912:18;8707;;;27992:6;27983:16;;8695:31;-1:-1:-1;28048:4:78;28037:16;;28031:23;28063:66;28121:6;28112:16;;28031:23;28063:66;:::i;:::-;;28178:4;28171:5;28167:16;28161:23;28193:67;28252:6;28247:3;28243:16;28226:15;28193:67;:::i;:::-;-1:-1:-1;28309:4:78;28298:16;;28292:23;-1:-1:-1;;;;;1740:46:78;28369:6;28360:16;;1728:59;28426:4;28415:16;;28409:23;275:10;264:22;;;28485:6;28476:16;;252:35;28542:4;28531:16;;28525:23;264:22;28601:6;28592:16;;252:35;28658:4;28647:16;28641:23;16381:4;16370:16;28716:6;28707:16;;;16358:29;26813:1917::o;28673:51::-;;26813:1917;;:::o;28735:426::-;-1:-1:-1;;;;;28824:5:78;28818:12;28814:53;28809:3;28802:66;28929:10;28921:4;28914:5;28910:16;28904:23;28900:40;28893:4;28888:3;28884:14;28877:64;29004:4;28997:5;28993:16;28987:23;28984:1;28973:38;28966:4;28961:3;28957:14;28950:62;29075:4;29068:5;29064:16;29058:23;29055:1;29044:38;29037:4;29032:3;29028:14;29021:62;29146:4;29139:5;29135:16;29129:23;29122:31;29115:39;29108:4;29103:3;29099:14;29092:63;;;28735:426::o;29166:1186::-;29254:12;;-1:-1:-1;;;;;460:31:78;448:44;;29318:4;29311:5;29307:16;29301:23;29333:48;29375:4;29370:3;29366:14;29352:12;-1:-1:-1;;;;;460:31:78;448:44;;394:104;29333:48;;29429:4;29422:5;29418:16;29412:23;29444:50;29488:4;29483:3;29479:14;29463;-1:-1:-1;;;;;460:31:78;448:44;;394:104;29444:50;;29542:4;29535:5;29531:16;29525:23;29557:61;29612:4;29607:3;29603:14;29587;24723:12;;-1:-1:-1;;;;;24719:21:78;;;24707:34;;24794:4;24783:16;;;24777:23;24773:32;24757:14;;24750:56;24604:208;29557:61;-1:-1:-1;29666:4:78;29655:16;;29649:23;-1:-1:-1;;;;;460:31:78;;29725:4;29716:14;;448:44;-1:-1:-1;29779:4:78;29768:16;;29762:23;-1:-1:-1;;;;;460:31:78;;29838:4;29829:14;;448:44;-1:-1:-1;29892:4:78;29881:16;;29875:23;-1:-1:-1;;;;;460:31:78;;29951:4;29942:14;;448:44;29907:50;30005:4;29998:5;29994:16;29988:23;30030:6;30045:47;30088:2;30083:3;30079:12;30063:14;275:10;264:22;252:35;;199:94;30045:47;30129:14;;30123:21;;-1:-1:-1;30163:6:78;30178:46;30211:12;;;30123:21;13390:1;13379:20;13367:33;;13315:91;30178:46;30261:14;;30255:21;8718:6;8707:18;;30338:6;30329:16;;8695:31;30255:21;-1:-1:-1;28673:51:78;26813:1917;;:::o;30357:1805::-;30420:48;30464:3;30456:5;30450:12;30420:48;:::i;:::-;30514:4;30507:5;30503:16;30497:23;30529:65;30586:6;30581:3;30577:16;30563:12;30529:65;:::i;:::-;-1:-1:-1;30645:4:78;30634:16;;30628:23;30619:6;30610:16;;30603:49;30703:4;30692:16;;30686:23;30677:6;30668:16;;30661:49;30761:4;30750:16;;30744:23;30735:6;30726:16;;30719:49;30819:4;30808:16;;30802:23;30793:6;30784:16;;30777:49;30877:4;30866:16;;30860:23;30851:6;30842:16;;30835:49;30932:4;30921:16;;30915:23;-1:-1:-1;;;;;460:31:78;;;30991:6;30982:16;;448:44;31047:6;31036:18;;31030:25;460:31;;31108:6;31099:16;;448:44;31164:6;31153:18;;31147:25;460:31;31225:6;31216:16;;448:44;31281:6;31270:18;;31264:25;-1:-1:-1;;;;;1740:46:78;31342:6;31333:16;;1728:59;31398:6;31387:18;;31381:25;373:1;362:20;;;31457:6;31448:16;;350:33;31513:6;31502:18;;31496:25;362:20;;31572:6;31563:16;;350:33;31628:6;31617:18;;31611:25;1873:1;1862:20;31687:6;31678:16;;1850:33;31743:6;31732:18;;31726:25;16381:4;16370:16;31802:6;31793:16;;16358:29;31858:6;31847:18;;31841:25;1964:13;1957:21;31916:6;31907:16;;1945:34;31973:6;31962:18;;31956:25;1964:13;1957:21;32032:6;32023:16;;1945:34;32089:6;32078:18;32072:25;1964:13;1957:21;32148:6;32139:16;;;1945:34;26813:1917::o;32167:1158::-;32235:48;32279:3;32271:5;32265:12;32235:48;:::i;:::-;32329:4;32322:5;32318:16;32312:23;32344:63;32399:6;32394:3;32390:16;32376:12;32344:63;:::i;:::-;;32455:4;32448:5;32444:16;32438:23;32470:67;32529:6;32524:3;32520:16;32504:14;32470:67;:::i;:::-;;32585:4;32578:5;32574:16;32568:23;32600:66;32658:6;32653:3;32649:16;32633:14;32600:66;:::i;:::-;;32714:4;32707:5;32703:16;32697:23;32729:61;32782:6;32777:3;32773:16;32757:14;32729:61;:::i;:::-;;32841:4;32834:5;32830:16;32824:23;32815:6;32810:3;32806:16;32799:49;32899:4;32892:5;32888:16;32882:23;32873:6;32868:3;32864:16;32857:49;32957:4;32950:5;32946:16;32940:23;32931:6;32926:3;32922:16;32915:49;33015:6;33008:5;33004:18;32998:25;32989:6;32984:3;32980:16;32973:51;33075:6;33068:5;33064:18;33058:25;33049:6;33044:3;33040:16;33033:51;33135:6;33128:5;33124:18;33118:25;33109:6;33104:3;33100:16;33093:51;33195:6;33188:5;33184:18;33178:25;33169:6;33164:3;33160:16;33153:51;33252:6;33245:5;33241:18;33235:25;33269:50;33311:6;33306:3;33302:16;33286:14;1873:1;1862:20;1850:33;;1798:91;33330:1966;33966:4;33995;34026:6;34015:9;34008:25;34069:6;34064:2;34053:9;34049:18;34042:34;34112:6;34107:2;34096:9;34092:18;34085:34;34155:6;34150:2;34139:9;34135:18;34128:34;34199:6;34193:3;34182:9;34178:19;34171:35;34243:6;34237:3;34226:9;34222:19;34215:35;34287:2;34281:3;34270:9;34266:19;34259:31;34362:1;34358;34353:3;34349:11;34345:19;34336:6;34330:13;34326:39;34321:2;34310:9;34306:18;34299:67;;-1:-1:-1;;;;;34426:2:78;34418:6;34414:15;34408:22;34404:63;34397:4;34386:9;34382:20;34375:93;34515:2;34507:6;34503:15;34497:22;34528:53;34575:4;34564:9;34560:20;34546:12;26298:26;26287:38;26275:51;;26222:110;34528:53;-1:-1:-1;34630:2:78;34618:15;;34612:22;275:10;264:22;34692:4;34677:20;;252:35;34747:3;34735:16;;34729:23;1873:1;1862:20;;;34809:4;34794:20;;1850:33;34864:3;34852:16;;34846:23;1862:20;;34926:4;34911:20;;1850:33;34981:3;34969:16;;34963:23;1964:13;1957:21;35042:4;35027:20;;1945:34;35097:4;35085:17;;35079:24;35141:6;35134:4;35119:20;;35112:36;35165:54;35213:4;35198:20;;35079:24;35165:54;:::i;:::-;35157:62;;;35228;35284:4;35273:9;35269:20;35261:6;35228:62;:::i;:::-;33330:1966;;;;;;;;;;;:::o;35301:1075::-;35821:25;;;35877:2;35862:18;;;35855:34;;;35920:2;35905:18;;;35898:34;;;35963:2;35948:18;;;35941:34;;;36016:13;;-1:-1:-1;;;;;36012:39:78;36006:3;35991:19;;35984:68;36099:15;;;36093:22;-1:-1:-1;;;;;36089:63:78;36039:3;36068:19;;36061:92;36210:15;;36204:22;36197:30;36190:38;36184:3;36169:19;;36162:67;36286:15;;36280:22;36273:30;36266:38;36260:3;36245:19;;36238:67;35807:4;35792:20;;36314:56;36365:3;36350:19;;36342:6;36314:56;:::i;36381:138::-;36460:13;;36482:31;36460:13;36482:31;:::i;36524:373::-;36612:6;36620;36628;36681:2;36669:9;36660:7;36656:23;36652:32;36649:52;;;36697:1;36694;36687:12;36649:52;36726:9;36720:16;36710:26;;36776:2;36765:9;36761:18;36755:25;36745:35;;36823:2;36812:9;36808:18;36802:25;36836:31;36861:5;36836:31;:::i;:::-;36886:5;36876:15;;;36524:373;;;;;:::o;37488:495::-;37580:12;;-1:-1:-1;;;;;37576:38:78;37564:51;;37611:1;37647:16;;37641:23;-1:-1:-1;;;;;37696:50:78;;37689:4;37680:14;;37673:74;37783:3;37779:19;;;37772:4;37763:14;;37756:43;37844:4;37833:16;;;37827:23;37899:6;37882:24;;37875:4;37866:14;;37859:48;37959:2;37955:20;37938:38;;;37923:13;;37916:61;37488:495::o;37988:1819::-;36992:12;;37023:6;37050:18;;;37038:31;;37109:2;37105:18;;;37101:27;;37094:4;37085:14;;;37078:51;;;;37165:20;;;;37161:29;;37154:4;37145:14;;37138:53;38199:4;38188:16;;38182:23;-1:-1:-1;;;;;37298:36:78;;;38257:4;38248:14;;15765:44;38354:4;38343:16;;;38337:23;37298:36;;;38414:4;38405:14;;;15765:44;;;;38511:4;38500:16;;38494:23;-1:-1:-1;;;;;37441:36:78;;;38571:4;38562:14;;448:44;38668:4;38657:16;;38651:23;37441:36;38728:4;38719:14;;448:44;38777:4;38766:16;;38760:23;-1:-1:-1;;;;;38864:18:78;;;38893:4;38884:14;;;1728:59;;;;38939:20;;;;38936:1;38925:35;38971:6;38962:16;;350:33;39026:3;39022:19;;;39005:37;;;39053:6;39044:16;;1850:33;39131:3;39127:19;;;39123:28;;39162:6;39153:16;;8695:31;39211:20;;;;39207:29;;39247:6;39238:16;;8695:31;38760:23;39179:76;39264:73;39329:6;39324:3;39320:16;39316:1;39309:5;39305:13;39264:73;:::i;:::-;39346:76;39414:6;39409:3;39405:16;39398:4;39391:5;39387:16;39346:76;:::i;:::-;39467:4;39456:16;;;;39450:23;39501:20;;;-1:-1:-1;;;;;1740:46:78;39532:6;39523:16;;1728:59;-1:-1:-1;39604:4:78;39600:22;;;39559:10;39596:31;;;39638:6;39629:16;;252:35;39681:4;39677:22;;;39673:31;39715:6;39706:16;;252:35;39757:4;39753:22;39777:4;39749:33;39793:6;39784:16;;;16358:29;26813:1917::o;39812:763::-;40294:25;;;40350:2;40335:18;;40328:34;;;40280:4;40265:20;;40371:65;40432:2;40417:18;;40409:6;40371:65;:::i;:::-;40445:62;40502:3;40491:9;40487:19;40479:6;40445:62;:::i;:::-;40557:10;40549:6;40545:23;40538:4;40527:9;40523:20;40516:53;39812:763;;;;;;;;:::o;40580:164::-;40657:13;;40710:1;40699:20;;;40689:31;;40679:59;;40734:1;40731;40724:12;40749:138;40828:13;;40850:31;40828:13;40850:31;:::i;40892:601::-;40987:6;40995;41003;41011;41064:3;41052:9;41043:7;41039:23;41035:33;41032:53;;;41081:1;41078;41071:12;41032:53;41104:38;41132:9;41104:38;:::i;:::-;41094:48;;41185:2;41174:9;41170:18;41164:25;41198:31;41223:5;41198:31;:::i;:::-;41298:2;41283:18;;41277:25;41248:5;;-1:-1:-1;41311:33:78;41277:25;41311:33;:::i;:::-;41415:2;41400:18;;41394:25;41363:7;;-1:-1:-1;41428:33:78;41394:25;41428:33;:::i;:::-;40892:601;;;;-1:-1:-1;40892:601:78;;-1:-1:-1;;40892:601:78:o;41498:439::-;41583:12;;-1:-1:-1;;;;;41579:38:78;41567:51;;41671:4;41660:16;;;41654:23;-1:-1:-1;;;;;41650:64:78;41634:14;;;41627:88;41768:4;41757:16;;;41751:23;41776:10;41747:40;41731:14;;;41724:64;41851:4;41840:16;;;41834:23;41831:1;41820:38;41804:14;;;41797:62;41922:4;41911:16;;;41905:23;41898:31;41891:39;41875:14;;41868:63;41498:439::o;41942:2622::-;42455:4;42497;42486:9;42482:20;42474:28;;42529:6;42518:9;42511:25;42572:6;42567:2;42556:9;42552:18;42545:34;42615:6;42610:2;42599:9;42595:18;42588:34;42658:6;42653:2;42642:9;42638:18;42631:34;42674:62;42731:3;42720:9;42716:19;42708:6;42674:62;:::i;:::-;42755:3;42767:64;42827:2;42816:9;42812:18;42803:6;42797:13;42767:64;:::i;:::-;42878:2;42870:6;42866:15;42860:22;42891:68;42953:4;42942:9;42938:20;42924:12;42891:68;:::i;:::-;;43008:2;43000:6;42996:15;42990:22;43021:65;43080:4;43069:9;43065:20;43049:14;43021:65;:::i;:::-;;43135:2;43127:6;43123:15;43117:22;43148:69;43211:4;43200:9;43196:20;43180:14;43148:69;:::i;:::-;;43266:3;43258:6;43254:16;43248:23;43280:71;43345:4;43334:9;43330:20;43314:14;43280:71;:::i;:::-;-1:-1:-1;43400:4:78;43388:17;;43382:24;-1:-1:-1;;;;;460:31:78;;;43465:4;43450:20;;448:44;43520:4;43508:17;;43502:24;460:31;;43585:4;43570:20;;448:44;43640:4;43628:17;;43622:24;460:31;43705:4;43690:20;;448:44;43760:6;43748:19;;43742:26;-1:-1:-1;;;;;1740:46:78;;;43827:4;43812:20;;1728:59;43870:15;;;43864:22;1740:46;;43945:4;43930:20;;1728:59;44000:6;43988:19;;43982:26;1740:46;44067:4;44052:20;;1728:59;44123:6;44111:19;;44105:26;1873:1;1862:20;44189:4;44174:20;;1850:33;44245:6;44233:19;;44227:26;1964:13;1957:21;44310:4;44295:20;;1945:34;44366:6;44354:19;;44348:26;1964:13;1957:21;44431:4;44416:20;;1945:34;44487:6;44475:19;;;44469:26;1964:13;1957:21;44552:4;44537:20;;;1945:34;44475:19;41942:2622;-1:-1:-1;;;;;41942:2622:78:o;44569:822::-;44873:4;44915:3;44904:9;44900:19;44892:27;;44946:6;44935:9;44928:25;44972:6;45033:2;45024:6;45018:13;45014:22;45009:2;44998:9;44994:18;44987:50;45101:2;45095;45087:6;45083:15;45077:22;45073:31;45068:2;45057:9;45053:18;45046:59;45169:2;45163;45155:6;45151:15;45145:22;45141:31;45136:2;45125:9;45121:18;45114:59;45238:2;45232;45224:6;45220:15;45214:22;45210:31;45204:3;45193:9;45189:19;45182:60;;45308:4;45301:3;45293:6;45289:16;45283:23;45279:34;45273:3;45262:9;45258:19;45251:63;45323:62;45380:3;45369:9;45365:19;45357:6;45323:62;:::i;:::-;44569:822;;;;;;:::o;45396:385::-;45475:6;45483;45536:2;45524:9;45515:7;45511:23;45507:32;45504:52;;;45552:1;45549;45542:12;45504:52;45584:9;45578:16;45603:31;45628:5;45603:31;:::i;:::-;45703:2;45688:18;;45682:25;45653:5;;-1:-1:-1;45716:33:78;45682:25;45716:33;:::i;:::-;45768:7;45758:17;;;45396:385;;;;;:::o;45786:942::-;46394:25;;;46450:2;46435:18;;46428:34;;;46493:2;46478:18;;46471:34;;;46380:4;46365:20;;46514:65;46575:2;46560:18;;46552:6;46514:65;:::i;:::-;46588:62;46645:3;46634:9;46630:19;46622:6;46588:62;:::i;:::-;46659:63;46716:4;46705:9;46701:20;46693:6;46659:63;:::i;46733:1475::-;47268:4;47297;47328:6;47317:9;47310:25;47371:6;47366:2;47355:9;47351:18;47344:34;47414:6;47409:2;47398:9;47394:18;47387:34;47457:6;47452:2;47441:9;47437:18;47430:34;47501:6;47495:3;47484:9;47480:19;47473:35;47545:2;47539:3;47528:9;47524:19;47517:31;47584:1;47580;47575:3;47571:11;47567:19;47641:2;47632:6;47626:13;47622:22;47617:2;47606:9;47602:18;47595:50;47711:2;47705;47697:6;47693:15;47687:22;47683:31;47676:4;47665:9;47661:20;47654:61;;;-1:-1:-1;;;;;47775:2:78;47767:6;47763:15;47757:22;47753:63;47746:4;47735:9;47731:20;47724:93;47887:2;47879:6;47875:15;47869:22;47862:30;47855:38;47848:4;47837:9;47833:20;47826:68;47964:3;47956:6;47952:16;47946:23;47939:31;47932:39;47925:4;47914:9;47910:20;47903:69;48019:3;48011:6;48007:16;48001:23;48062:4;48055;48044:9;48040:20;48033:34;48084:52;48130:4;48119:9;48115:20;48101:12;48084:52;:::i;:::-;48076:60;;;48145:57;48196:4;48185:9;48181:20;48173:6;48145:57;:::i;48213:243::-;48290:6;48298;48351:2;48339:9;48330:7;48326:23;48322:32;48319:52;;;48367:1;48364;48357:12;48319:52;-1:-1:-1;;48390:16:78;;48446:2;48431:18;;;48425:25;48390:16;;48425:25;;-1:-1:-1;48213:243:78:o;48461:393::-;48546:5;48540:12;48535:3;48528:25;48602:4;48595:5;48591:16;48585:23;48578:4;48573:3;48569:14;48562:47;-1:-1:-1;;;;;48662:4:78;48655:5;48651:16;48645:23;48641:64;48634:4;48629:3;48625:14;48618:88;48769:4;48762:5;48758:16;48752:23;48749:1;48738:38;48731:4;48726:3;48722:14;48715:62;48840:4;48833:5;48829:16;48823:23;48820:1;48809:38;48802:4;48797:3;48793:14;48786:62;;;48461:393::o;48859:727::-;48982:1;48978;48973:3;48969:11;48965:19;48957:5;48951:12;48947:38;48942:3;48935:51;49049:4;49042:5;49038:16;49032:23;49029:1;49018:38;49011:4;49006:3;49002:14;48995:62;49120:4;49113:5;49109:16;49103:23;49100:1;49089:38;49082:4;49077:3;49073:14;49066:62;49189:10;49181:4;49174:5;49170:16;49164:23;49160:40;49153:4;49148:3;49144:14;49137:64;48917:3;49247:4;49240:5;49236:16;49230:23;-1:-1:-1;;;;;49356:2:78;49342:12;49338:21;49331:4;49326:3;49322:14;49315:45;49421:2;49413:4;49406:5;49402:16;49396:23;49392:32;49385:4;49380:3;49376:14;49369:56;;;49473:4;49466:5;49462:16;49456:23;49511:4;49504;49499:3;49495:14;49488:28;49532:48;49574:4;49569:3;49565:14;49549;49532:48;:::i;49591:2185::-;50177:4;50206;50237:6;50226:9;50219:25;50280:6;50275:2;50264:9;50260:18;50253:34;50323:6;50318:2;50307:9;50303:18;50296:34;50366:6;50361:2;50350:9;50346:18;50339:34;50410:6;50404:3;50393:9;50389:19;50382:35;50426:65;50486:3;50475:9;50471:19;50462:6;50456:13;50426:65;:::i;:::-;50538:2;50530:6;50526:15;50520:22;50551:66;50612:3;50601:9;50597:19;50583:12;50551:66;:::i;:::-;;50666:2;50658:6;50654:15;50648:22;50679:71;50744:4;50733:9;50729:20;50713:14;50679:71;:::i;:::-;-1:-1:-1;50799:2:78;50787:15;;50781:22;-1:-1:-1;;;;;460:31:78;;;50862:4;50847:20;;448:44;50924:3;50912:16;;50906:23;50899:4;50884:20;;50877:53;50979:3;50967:16;;50961:23;460:31;;51043:4;51028:20;;448:44;51098:4;51086:17;;51080:24;460:31;51163:4;51148:20;;448:44;51218:4;51206:17;;51200:24;19785:2;19774:21;;;51282:4;51267:20;;19762:34;51337:6;51325:19;;51319:26;19774:21;;51403:4;51388:20;;19762:34;51458:6;51446:19;;51440:26;19774:21;;51524:4;51509:20;;19762:34;51579:6;51567:19;;51561:26;19774:21;;51645:4;51630:20;;19762:34;51682:4;51667:20;;51660:32;;;51709:61;51751:18;;;51743:6;51709:61;:::i;:::-;51701:69;49591:2185;-1:-1:-1;;;;;;;;;;49591:2185:78:o;51781:1006::-;52106:4;52135;52177:2;52166:9;52162:18;52189:56;52235:9;52227:6;52189:56;:::i;:::-;52254:62;52311:3;52300:9;52296:19;52288:6;52254:62;:::i;:::-;52347:4;52332:20;;52325:32;;;;52406:13;;52428:22;;;;52481:4;52466:20;;;52505:4;;52532:15;;;52565:1;52575:186;52589:6;52586:1;52583:13;52575:186;;;52654:13;;52669:10;52650:30;52638:43;;52701:12;;;;52736:15;;;;52611:1;52604:9;52575:186;;;-1:-1:-1;52778:3:78;;51781:1006;-1:-1:-1;;;;;;;;51781:1006:78:o;52792:733::-;52857:5;52910:3;52903:4;52895:6;52891:17;52887:27;52877:55;;52928:1;52925;52918:12;52877:55;52957:6;52951:13;52983:4;53007:59;53023:42;53062:2;53023:42;:::i;53007:59::-;53100:15;;;53186:1;53182:10;;;;53170:23;;53166:32;;;53131:12;;;;53210:15;;;53207:35;;;53238:1;53235;53228:12;53207:35;53274:2;53266:6;53262:15;53286:210;53302:6;53297:3;53294:15;53286:210;;;53375:3;53369:10;53392:31;53417:5;53392:31;:::i;:::-;53436:18;;53474:12;;;;53319;;53286:210;;;-1:-1:-1;53514:5:78;52792:733;-1:-1:-1;;;;;;52792:733:78:o;53530:134::-;53607:13;;53629:29;53607:13;53629:29;:::i;53669:1406::-;53821:6;53829;53837;53845;53853;53906:3;53894:9;53885:7;53881:23;53877:33;53874:53;;;53923:1;53920;53913:12;53874:53;53956:9;53950:16;-1:-1:-1;;;;;54026:2:78;54018:6;54015:14;54012:34;;;54042:1;54039;54032:12;54012:34;54080:6;54069:9;54065:22;54055:32;;54125:7;54118:4;54114:2;54110:13;54106:27;54096:55;;54147:1;54144;54137:12;54096:55;54176:2;54170:9;54198:4;54222:59;54238:42;54277:2;54238:42;:::i;54222:59::-;54315:15;;;54397:1;54393:10;;;;54385:19;;54381:28;;;54346:12;;;;54421:19;;;54418:39;;;54453:1;54450;54443:12;54418:39;54477:11;;;;54497:157;54513:6;54508:3;54505:15;54497:157;;;54579:32;54607:3;54579:32;:::i;:::-;54567:45;;54530:12;;;;54632;;;;54497:157;;;54709:18;;;54703:25;54673:5;;-1:-1:-1;54703:25:78;;-1:-1:-1;;;54740:16:78;;;54737:36;;;54769:1;54766;54759:12;54737:36;;54792:74;54858:7;54847:8;54836:9;54832:24;54792:74;:::i;:::-;54782:84;;;54885:49;54930:2;54919:9;54915:18;54885:49;:::i;:::-;54875:59;;54953:49;54998:2;54987:9;54983:18;54953:49;:::i;:::-;54943:59;;55021:48;55064:3;55053:9;55049:19;55021:48;:::i;:::-;55011:58;;53669:1406;;;;;;;;:::o;55080:839::-;55586:25;;;55642:2;55627:18;;55620:34;;;55685:2;55670:18;;55663:34;;;55572:4;55557:20;;55706:65;55767:2;55752:18;;55744:6;55706:65;:::i;:::-;55780:62;55837:3;55826:9;55822:19;55814:6;55780:62;:::i;:::-;-1:-1:-1;;;;;55880:32:78;;;;55873:4;55858:20;;;;55851:62;55080:839;;-1:-1:-1;;;;;55080:839:78:o;55924:136::-;56002:13;;56024:30;56002:13;56024:30;:::i;56065:748::-;56134:5;56182:4;56170:9;56165:3;56161:19;56157:30;56154:50;;;56200:1;56197;56190:12;56154:50;56233:2;56227:9;56275:4;56267:6;56263:17;56346:6;56334:10;56331:22;-1:-1:-1;;;;;56298:10:78;56295:34;56292:62;56289:88;;;56357:18;;:::i;:::-;56397:10;56393:2;56386:22;;56426:6;56417:15;;56462:9;56456:16;56481:32;56505:7;56481:32;:::i;:::-;56522:23;;56590:2;56575:18;;56569:25;56603:32;56569:25;56603:32;:::i;:::-;56663:2;56651:15;;56644:32;56721:2;56706:18;;56700:25;56734:32;56700:25;56734:32;:::i;:::-;56794:2;56782:15;;;;56775:32;56065:748;;-1:-1:-1;;56065:748:78:o;56818:177::-;56897:13;;-1:-1:-1;;;;;56939:31:78;;56929:42;;56919:70;;56985:1;56982;56975:12;57000:824;57072:5;57120:4;57108:9;57103:3;57099:19;57095:30;57092:50;;;57138:1;57135;57128:12;57092:50;57160:22;;:::i;:::-;57151:31;;57212:9;57206:16;57231:33;57256:7;57231:33;:::i;:::-;57273:22;;57340:2;57325:18;;57319:25;57353:33;57319:25;57353:33;:::i;:::-;57413:2;57402:14;;57395:31;57471:2;57456:18;;57450:25;57484:33;57450:25;57484:33;:::i;:::-;57544:2;57533:14;;57526:31;57602:2;57587:18;;57581:25;57615:32;57581:25;57615:32;:::i;:::-;57674:2;57663:14;;57656:31;57732:3;57717:19;;57711:26;57746:31;57711:26;57746:31;:::i;57829:136::-;57907:13;;57929:30;57907:13;57929:30;:::i;57970:134::-;58047:13;;58069:29;58047:13;58069:29;:::i;58109:1853::-;58208:6;58252:9;58243:7;58239:23;58282:3;58278:2;58274:12;58271:32;;;58299:1;58296;58289:12;58271:32;58325:22;;:::i;:::-;58366:6;58392:2;58388;58384:11;58381:31;;;58408:1;58405;58398:12;58381:31;58436:22;;:::i;:::-;58421:37;;58483:60;58535:7;58524:9;58483:60;:::i;:::-;58474:7;58467:77;58580:49;58625:2;58614:9;58610:18;58580:49;:::i;:::-;58573:4;58564:7;58560:18;58553:77;58666:50;58711:3;58700:9;58696:19;58666:50;:::i;:::-;58659:4;58650:7;58646:18;58639:78;58751:50;58796:3;58785:9;58781:19;58751:50;:::i;:::-;58746:2;58737:7;58733:16;58726:76;58837:50;58882:3;58871:9;58867:19;58837:50;:::i;:::-;58831:3;58822:7;58818:17;58811:77;58923:50;58968:3;58957:9;58953:19;58923:50;:::i;:::-;58917:3;58908:7;58904:17;58897:77;58993:3;59031:47;59074:2;59063:9;59059:18;59031:47;:::i;:::-;59025:3;59016:7;59012:17;59005:74;59098:3;59136:47;59179:2;59168:9;59164:18;59136:47;:::i;:::-;59130:3;59121:7;59117:17;59110:74;59218:52;59262:6;59251:9;59247:22;59218:52;:::i;:::-;59213:2;59204:7;59200:16;59193:78;59305:49;59349:3;59338:9;59334:19;59305:49;:::i;:::-;59287:16;;;59280:75;-1:-1:-1;59364:22:78;;;59420:72;59484:7;59464:18;;;59420:72;:::i;:::-;59413:4;59406:5;59402:16;59395:98;;59527:73;59592:7;59586:3;59575:9;59571:19;59527:73;:::i;:::-;59520:4;59513:5;59509:16;59502:99;59633:50;59678:3;59667:9;59663:19;59633:50;:::i;:::-;59628:2;59621:5;59617:14;59610:74;59717:49;59761:3;59750:9;59746:19;59717:49;:::i;:::-;59711:3;59704:5;59700:15;59693:74;59800:49;59844:3;59833:9;59829:19;59800:49;:::i;:::-;59794:3;59787:5;59783:15;59776:74;59883:48;59926:3;59915:9;59911:19;59883:48;:::i;:::-;59877:3;59866:15;;59859:73;59870:5;58109:1853;-1:-1:-1;;;;58109:1853:78:o;60268:1757::-;60854:4;60896;60885:9;60881:20;60873:28;;60928:6;60917:9;60910:25;60971:6;60966:2;60955:9;60951:18;60944:34;61014:6;61009:2;60998:9;60994:18;60987:34;61057:6;61052:2;61041:9;61037:18;61030:34;61101:6;61095:3;61084:9;61080:19;61073:35;61117:65;61177:3;61166:9;61162:19;61153:6;61147:13;61117:65;:::i;:::-;61229:2;61221:6;61217:15;61211:22;61242:66;61303:3;61292:9;61288:19;61274:12;61242:66;:::i;:::-;;61357:2;61349:6;61345:15;61339:22;61370:71;61435:4;61424:9;61420:20;61404:14;61370:71;:::i;:::-;-1:-1:-1;61497:2:78;61485:15;;61479:22;61472:4;61457:20;;61450:52;61562:3;61550:16;;61544:23;-1:-1:-1;;;;;61540:49:78;61533:4;61518:20;;61511:79;61577:3;61627:16;;61621:23;61653:56;61703:4;61688:20;;61621:23;-1:-1:-1;;;;;460:31:78;448:44;;394:104;61653:56;;61758:4;61750:6;61746:17;61740:24;61773:55;61822:4;61811:9;61807:20;61791:14;19785:2;19774:21;19762:34;;19709:93;61773:55;;61877:4;61869:6;61865:17;61859:24;61892:55;61941:4;61930:9;61926:20;61910:14;19785:2;19774:21;19762:34;;19709:93;61892:55;-1:-1:-1;60052:12:78;;-1:-1:-1;;;;;60048:38:78;62013:4;61998:20;;60036:51;60140:4;60129:16;;60123:23;60148:10;60119:40;60103:14;;;60096:64;60213:4;60202:16;;60196:23;-1:-1:-1;;;;;60192:64:78;60176:14;;;60169:88;61956:63;59967:296;64242:127;64303:10;64298:3;64294:20;64291:1;64284:31;64334:4;64331:1;64324:15;64358:4;64355:1;64348:15;64374:127;64435:10;64430:3;64426:20;64423:1;64416:31;64466:4;64463:1;64456:15;64490:4;64487:1;64480:15;64506:197;64544:3;64572:6;64613:2;64606:5;64602:14;64640:2;64631:7;64628:15;64625:41;;64646:18;;:::i;:::-;64695:1;64682:15;;64506:197;-1:-1:-1;;;64506:197:78:o;65050:127::-;65111:10;65106:3;65102:20;65099:1;65092:31;65142:4;65139:1;65132:15;65166:4;65163:1;65156:15;65182:372;65220:1;65261;65258;65247:16;65297:1;65294;65283:16;65318:3;65308:134;;65364:10;65359:3;65355:20;65352:1;65345:31;65399:4;65396:1;65389:15;65427:4;65424:1;65417:15;65308:134;-1:-1:-1;;65458:21:78;;-1:-1:-1;;65481:15:78;;65454:43;65451:69;;;65500:18;;:::i;:::-;65534:14;;;65182:372;-1:-1:-1;;;65182:372:78:o;65559:642::-;65597:7;65644:1;65641;65630:16;65680:1;65677;65666:16;65701:8;65737:1;65732:3;65728:11;65767:1;65762:3;65758:11;65814:3;65810:2;65806:12;65801:3;65798:21;65793:2;65789;65785:11;65781:39;65778:65;;;65823:18;;:::i;:::-;-1:-1:-1;;65902:1:78;65893:11;;65920;;;65942:13;;;65933:23;;65916:41;65913:67;;;65960:18;;:::i;:::-;66008:1;66003:3;65999:11;65989:21;;66057:3;66053:2;66048:13;66043:3;66039:23;66034:2;66030;66026:11;66022:41;66019:67;;;66066:18;;:::i;:::-;66133:3;66129:2;66124:13;66119:3;66115:23;66110:2;66106;66102:11;66098:41;66095:67;;;66142:18;;:::i;:::-;-1:-1:-1;;;66182:13:78;;;;;65559:642;-1:-1:-1;;;;;65559:642:78:o;66206:136::-;66241:3;-1:-1:-1;;;66262:22:78;;66259:48;;66287:18;;:::i;:::-;-1:-1:-1;66327:1:78;66323:13;;66206:136::o;66693:184::-;66727:3;66774:5;66771:1;66760:20;66808:7;66804:12;66795:7;66792:25;66789:51;;66820:18;;:::i;:::-;66860:1;66856:15;;66693:184;-1:-1:-1;;66693:184:78:o", "linkReferences": {"contracts/libraries/Ticks.sol": {"Ticks": [{"start": 4081, "length": 20}]}, "contracts/libraries/limit/pool/BurnLimitCall.sol": {"BurnLimitCall": [{"start": 3028, "length": 20}]}, "contracts/libraries/limit/pool/MintLimitCall.sol": {"MintLimitCall": [{"start": 2465, "length": 20}]}, "contracts/libraries/limit/pool/SnapshotLimitCall.sol": {"SnapshotLimitCall": [{"start": 3303, "length": 20}]}, "contracts/libraries/pool/FeesCall.sol": {"FeesCall": [{"start": 3150, "length": 20}]}, "contracts/libraries/pool/QuoteCall.sol": {"QuoteCall": [{"start": 2673, "length": 20}]}, "contracts/libraries/pool/SampleCall.sol": {"SampleCall": [{"start": 3900, "length": 20}]}, "contracts/libraries/pool/SwapCall.sol": {"SwapCall": [{"start": 3611, "length": 20}]}, "contracts/libraries/range/pool/BurnRangeCall.sol": {"BurnRangeCall": [{"start": 5401, "length": 20}]}, "contracts/libraries/range/pool/MintRangeCall.sol": {"MintRangeCall": [{"start": 3829, "length": 20}]}, "contracts/libraries/range/pool/SnapshotRangeCall.sol": {"SnapshotRangeCall": [{"start": 2828, "length": 20}]}}, "immutableReferences": {"55": [{"start": 932, "length": 32}, {"start": 5506, "length": 32}, {"start": 5906, "length": 32}, {"start": 6040, "length": 32}], "57": [{"start": 1439, "length": 32}, {"start": 5553, "length": 32}, {"start": 6293, "length": 32}, {"start": 6865, "length": 32}]}}, "methodIdentifiers": {"burnLimit((address,uint128,uint32,int24,bool))": "63aca53c", "burnRange((address,uint32,uint128))": "fadd58a2", "factory()": "c45a0155", "fees((uint16,uint16,uint16,uint16,uint8))": "810b1d09", "genesisTime()": "42c6498a", "globalState()": "e76c01e4", "immutables()": "fdf53665", "increaseSampleCount(uint16)": "d468e71f", "initialize(uint160)": "f637731d", "limitTickMap()": "60f9b0d3", "maxPrice()": "e38d6b5c", "minPrice()": "e45be8eb", "mintLimit((address,uint128,uint96,uint32,int24,int24,bool,bytes))": "270ffd7d", "mintRange((address,int24,int24,uint32,uint128,uint128,bytes))": "f0a20aec", "original()": "46c715fa", "owner()": "8da5cb5b", "poolToken()": "cbdf382c", "positions(uint256)": "99fbab88", "positions0(uint256)": "aedb4197", "positions1(uint256)": "256b9f39", "priceBounds(int16)": "240dda1f", "quote((uint160,uint128,bool,bool))": "43865d4e", "rangeTickMap()": "a99c9dd5", "sample(uint32[])": "f62167fc", "samples(uint256)": "07e72129", "snapshotLimit((address,uint128,uint32,int24,bool))": "8a5ee2c3", "snapshotRange(uint32)": "5cbb18d1", "swap((address,uint160,uint128,bool,bool,bytes))": "e323eb0e", "swapFee()": "54cf2aeb", "tickSpacing()": "d0c93a7c", "ticks(int24)": "f30dba93", "token0()": "0dfe1681", "token1()": "d21220a7"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"factory_\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"ReentrancyGuardInvalidState\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReadOnlyReentrantCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"burnPercent\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"claim\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.BurnLimitParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"burnLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"uint128\",\"name\":\"burnPercent\",\"type\":\"uint128\"}],\"internalType\":\"struct PoolsharkStructs.BurnRangeParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"burnRange\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"factory\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee0\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee1\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee0\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee1\",\"type\":\"uint16\"},{\"internalType\":\"uint8\",\"name\":\"setFeesFlags\",\"type\":\"uint8\"}],\"internalType\":\"struct PoolsharkStructs.FeesParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"fees\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"token0Fees\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"token1Fees\",\"type\":\"uint128\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"genesisTime\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"globalState\",\"outputs\":[{\"components\":[{\"components\":[{\"internalType\":\"uint16\",\"name\":\"index\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"count\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"countMax\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.SampleState\",\"name\":\"samples\",\"type\":\"tuple\"},{\"internalType\":\"uint200\",\"name\":\"feeGrowthGlobal0\",\"type\":\"uint200\"},{\"internalType\":\"uint200\",\"name\":\"feeGrowthGlobal1\",\"type\":\"uint200\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"int56\",\"name\":\"tickSecondsAccum\",\"type\":\"int56\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"},{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee0\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee1\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.RangePoolState\",\"name\":\"pool\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"protocolFees\",\"type\":\"uint128\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee\",\"type\":\"uint16\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"}],\"internalType\":\"struct PoolsharkStructs.LimitPoolState\",\"name\":\"pool0\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"protocolFees\",\"type\":\"uint128\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee\",\"type\":\"uint16\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"}],\"internalType\":\"struct PoolsharkStructs.LimitPoolState\",\"name\":\"pool1\",\"type\":\"tuple\"},{\"internalType\":\"uint128\",\"name\":\"liquidityGlobal\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"positionIdNext\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"},{\"internalType\":\"uint8\",\"name\":\"unlocked\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"immutables\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"factory\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"min\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"max\",\"type\":\"uint160\"}],\"internalType\":\"struct PoolsharkStructs.PriceBounds\",\"name\":\"bounds\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"genesisTime\",\"type\":\"uint32\"},{\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.LimitImmutables\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"newSampleCountMax\",\"type\":\"uint16\"}],\"name\":\"increaseSampleCount\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint160\",\"name\":\"startPrice\",\"type\":\"uint160\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"limitTickMap\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blocks\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"maxPrice\",\"outputs\":[{\"internalType\":\"uint160\",\"name\":\"\",\"type\":\"uint160\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"minPrice\",\"outputs\":[{\"internalType\":\"uint160\",\"name\":\"\",\"type\":\"uint160\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"uint96\",\"name\":\"mintPercent\",\"type\":\"uint96\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"callbackData\",\"type\":\"bytes\"}],\"internalType\":\"struct PoolsharkStructs.MintLimitParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"mintLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"uint128\",\"name\":\"amount0\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"amount1\",\"type\":\"uint128\"},{\"internalType\":\"bytes\",\"name\":\"callbackData\",\"type\":\"bytes\"}],\"internalType\":\"struct PoolsharkStructs.MintRangeParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"mintRange\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"original\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"poolToken\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"positions\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"feeGrowthInside0Last\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"feeGrowthInside1Last\",\"type\":\"uint256\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"positions0\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"epochLast\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"crossedInto\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"positions1\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"epochLast\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"crossedInto\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"}],\"name\":\"priceBounds\",\"outputs\":[{\"internalType\":\"uint160\",\"name\":\"\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"\",\"type\":\"uint160\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint160\",\"name\":\"priceLimit\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"bool\",\"name\":\"exactIn\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.QuoteParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"quote\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint160\",\"name\":\"\",\"type\":\"uint160\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rangeTickMap\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blocks\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32[]\",\"name\":\"secondsAgo\",\"type\":\"uint32[]\"}],\"name\":\"sample\",\"outputs\":[{\"internalType\":\"int56[]\",\"name\":\"tickSecondsAccum\",\"type\":\"int56[]\"},{\"internalType\":\"uint160[]\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160[]\"},{\"internalType\":\"uint160\",\"name\":\"averagePrice\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"averageLiquidity\",\"type\":\"uint128\"},{\"internalType\":\"int24\",\"name\":\"averageTick\",\"type\":\"int24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"samples\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"blockTimestamp\",\"type\":\"uint32\"},{\"internalType\":\"int56\",\"name\":\"tickSecondsAccum\",\"type\":\"int56\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"burnPercent\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"claim\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.SnapshotLimitParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"snapshotLimit\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"\",\"type\":\"uint128\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"}],\"name\":\"snapshotRange\",\"outputs\":[{\"internalType\":\"int56\",\"name\":\"tickSecondsAccum\",\"type\":\"int56\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"feesOwed0\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"feesOwed1\",\"type\":\"uint128\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint160\",\"name\":\"priceLimit\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"bool\",\"name\":\"exactIn\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"callbackData\",\"type\":\"bytes\"}],\"internalType\":\"struct PoolsharkStructs.SwapParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"swap\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"swapFee\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"tickSpacing\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int24\",\"name\":\"\",\"type\":\"int24\"}],\"name\":\"ticks\",\"outputs\":[{\"components\":[{\"internalType\":\"uint200\",\"name\":\"feeGrowthOutside0\",\"type\":\"uint200\"},{\"internalType\":\"uint200\",\"name\":\"feeGrowthOutside1\",\"type\":\"uint200\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccumOutside\",\"type\":\"uint160\"},{\"internalType\":\"int56\",\"name\":\"tickSecondsAccumOutside\",\"type\":\"int56\"},{\"internalType\":\"int128\",\"name\":\"liquidityDelta\",\"type\":\"int128\"},{\"internalType\":\"uint128\",\"name\":\"liquidityAbsolute\",\"type\":\"uint128\"}],\"internalType\":\"struct PoolsharkStructs.RangeTick\",\"name\":\"range\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"priceAt\",\"type\":\"uint160\"},{\"internalType\":\"int128\",\"name\":\"liquidityDelta\",\"type\":\"int128\"},{\"internalType\":\"uint128\",\"name\":\"liquidityAbsolute\",\"type\":\"uint128\"}],\"internalType\":\"struct PoolsharkStructs.LimitTick\",\"name\":\"limit\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token0\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token1\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ReentrancyGuardInvalidState()\":[{\"details\":\"Reentrant state invalid.\"}],\"ReentrancyGuardReadOnlyReentrantCall()\":[{\"details\":\"Unauthorized read-only reentrant call.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Poolshark Limit Pool Implementation\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/LimitPool.sol\":\"LimitPool\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/LimitPool.sol\":{\"keccak256\":\"0x63e6fb0b3aa1758a080623dd6831836d259b34e34644ca031e9da48e72feea37\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://eca6e515c3df87abe97115e09409107ddc3161e2c9f640e0d008d36a40ddda39\",\"dweb:/ipfs/QmdTdME8PVUqakTNSHeFFH6KyMqZGGTEjVnZCxZUMV8eu2\"]},\"contracts/base/storage/LimitPoolFactoryStorage.sol\":{\"keccak256\":\"0xec8efbb85bf6d831dc0c3910eccbe1b6def550ac0b88eef62b4a7c0c314a31ae\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://*****************************9ea2ec7d4f81a0512f5b83901d3a9a4631f\",\"dweb:/ipfs/QmeKyWJwsX9LwB5A8ovGm7J2nToJvR1dnE3sMww46ERb55\"]},\"contracts/base/storage/LimitPoolImmutables.sol\":{\"keccak256\":\"0x12df280adc3ad5199cdb4ef07df724427f9694a2eaae240f31ba887b0d5946de\",\"license\":\"BSD\",\"urls\":[\"bzz-raw://829502a9103d01f0ce94459377da9b972629e6b227ceff9a7d6d01f866fc4715\",\"dweb:/ipfs/QmTj5jRT6Pff6efUgU11ASNaK1PoLykwKNWFAoPndyfZgQ\"]},\"contracts/base/storage/LimitPoolStorage.sol\":{\"keccak256\":\"0x063aa09f6535188ff3bbe5ca25af30433460e10786c9fc4ea061a572d04b7ca3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://878402ff2c89a596e9d8a18278bc484df2301296a0725af83747809227bbe315\",\"dweb:/ipfs/QmSKz1tQRxPiP5cgJo8kg8RieStHHzbMk3zSCjVCUYHKKL\"]},\"contracts/external/openzeppelin/security/LimitReentrancyGuard.sol\":{\"keccak256\":\"0x624b4055d71a8b5d6af63eb2ae3c237d13183e00c9114171faa7ec720d65e223\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://56de4a517b56017d985101ff56e22c9ebb03b1d9ad2b565767475095115b74c2\",\"dweb:/ipfs/QmY7kcsj2uKCCXVT4iamQLtgyeNshhb5FbShGt5QaQZ8dX\"]},\"contracts/external/solady/Clone.sol\":{\"keccak256\":\"0xe7dc35cc81529e1e43b249f99de3a26e595ffd209450b85f3e8bc74a0d9aea01\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6d0471fad81c71706936f7b014b0cfcd5f03337450838409c8c02fedc38a8560\",\"dweb:/ipfs/Qmede4qMoU29saLxaBxaA72kuWt4794HLcB7ZdXHRrBFXC\"]},\"contracts/external/solady/LibClone.sol\":{\"keccak256\":\"0x93750a76e235631c1438283750f8b096026a11d82399fdc002816c55acc1f55a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e99db3fbe71ac90a31b411481b148d17ef2cb6c748d38216ae233778d0228d4d\",\"dweb:/ipfs/QmWkt4Q5fQkMkq8a3Vf2KjKBqkAvqzpGFLy1rFvWzyYN5k\"]},\"contracts/interfaces/IERC20Minimal.sol\":{\"keccak256\":\"0xaff2e27e82f63ae2a2cdab428a0a29e0c035168813b639ff0c813f9cc13487a0\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://15f34a8337c2fdd90c9329b0104bbbe4a949b8e4431f5f52465e0e75a620db58\",\"dweb:/ipfs/QmWaJaHG4j8xe4ubgRQyYr8BqvyqmsKQUuQLFTXpQ8D42P\"]},\"contracts/interfaces/IPool.sol\":{\"keccak256\":\"0x67f42bc51b5a8fe379805a5c07826872c4503163d53894c59173e8f6d72a2b53\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://1662beddbe5fec900079fcb76eb1371b9af88f3a90db0bd828c4b57c460a659e\",\"dweb:/ipfs/QmQXx2t6iSgyjaXEsdaXMpaStjmVuRMiCjJA8x1CWtnEYu\"]},\"contracts/interfaces/IPositionERC1155.sol\":{\"keccak256\":\"0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11\",\"dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe\"]},\"contracts/interfaces/callbacks/ILimitPoolCallback.sol\":{\"keccak256\":\"0xbdd400055110618e0e90afe1064193676db3fcdcc621573b45c3512e8f062821\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://86c3b364c617fec20bfd96a843feca91d950bd2289b7650bd2a96ac9e7c380a7\",\"dweb:/ipfs/QmWR5PJQu6TuQb7ipouKkiwN5Lq11khdTTDAtaXxJyGnrX\"]},\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/limit/ILimitPool.sol\":{\"keccak256\":\"0x8754512ae636a8871b11412ad16735be460142a48ee9f0fc50310f4d4aa45227\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://9621227f4ab660043534cb124559d96d42cefad3441b61a7a82be81ef8674dcf\",\"dweb:/ipfs/QmYZsFqXwFW6dcYSrjc4R1Lu1r9MbMbo6EQH6Rk2yqztKA\"]},\"contracts/interfaces/limit/ILimitPoolFactory.sol\":{\"keccak256\":\"0x89fc412319c30451896a1db8d77bb528c6df383523cf7f278534a645a4a49733\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://495509c3a546e0b85b34a8ef7fc114ca5bb98d831c8bbd176b42d36703f00fd0\",\"dweb:/ipfs/QmcFR6a32Fp7s4hRDmJfp7GaC65uQUtRo6VhE6ygCpZefm\"]},\"contracts/interfaces/limit/ILimitPoolManager.sol\":{\"keccak256\":\"0x3569f41d49e23238d34b3dc91e7fe7587c8fa141cacc97bf382caa1b580837de\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://aa5545c53994d586c1e7e6aaeb7d0522a678ae012867268e93e3f65060e7360f\",\"dweb:/ipfs/QmU34ZDnYxCDV6i8fxq13AU5LptW3rXgY5ZYpLy2t8sLqk\"]},\"contracts/interfaces/limit/ILimitPoolStorageView.sol\":{\"keccak256\":\"0x81173f20ed82249830e6af5b51505e4373c86f2cba9ea7785d4f41cc0b54ee52\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://7905082bb0bf3bc4191e6fccdedd30bf2d662edf446a87a14df5c4cc5422759c\",\"dweb:/ipfs/QmdTppuhdTJjgLL6THPpown2Km6ya5bh6Q1qxAiw9oGo9b\"]},\"contracts/interfaces/limit/ILimitPoolView.sol\":{\"keccak256\":\"0x298606a582d43209c74b9abf469e3576876444b964ce02be0b5d52d8662fd88a\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://adad5cd4a5cbf1c874e8351def6f31877d305e28b6aaf6d9dc539c9d7417c420\",\"dweb:/ipfs/QmZTbR2jJzQe2T6GpDoGLPD5gAgLks6XFqzPewSUR1kBpw\"]},\"contracts/interfaces/range/IRangePool.sol\":{\"keccak256\":\"0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab\",\"dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt\"]},\"contracts/interfaces/range/IRangePoolFactory.sol\":{\"keccak256\":\"0xa38c314350de1c59bd90e521fe0920d1e5008009aca592697b07194346da8cc1\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://53551b95615f6a6259753c7e9fc189a46dfe971b83cb417af4d4c593278f9d60\",\"dweb:/ipfs/QmXFnKtuRVJgs9HbNaNZna7mkmBRhPxtiVfkJKWuqytscS\"]},\"contracts/interfaces/range/IRangePoolManager.sol\":{\"keccak256\":\"0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065\",\"dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP\"]},\"contracts/interfaces/structs/LimitPoolStructs.sol\":{\"keccak256\":\"0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef\",\"dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/interfaces/structs/RangePoolStructs.sol\":{\"keccak256\":\"0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9\",\"dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx\"]},\"contracts/libraries/Samples.sol\":{\"keccak256\":\"0xae162c0e142bbe444c3748135d74bf272849768abca62c1908794bb72551c0eb\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://cf38a14a2a9653dc9b44ad37f091b7c6117be6ca8a76b5f9e279998bd9060763\",\"dweb:/ipfs/QmYxJm1w9YL4bYHJqNcLTRxiwYKH68dTzNGGQiQxFmnADE\"]},\"contracts/libraries/TickMap.sol\":{\"keccak256\":\"0xa77a5fb9acdce8ed769375832ba046cd4173eb47112b3083021b1637df903555\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://71ed48dae8ec94546989456c1d2dff0ff412db9d364d2996dc2f5877a1c82c64\",\"dweb:/ipfs/QmStzY8Q86bF8A7SLasHtuKF3B6MNzruBidc8YhU1yLwsK\"]},\"contracts/libraries/Ticks.sol\":{\"keccak256\":\"0xae060497e2eed43dae98cffe0889ca1d0ee2810efda6d6772c9aa25db384fd94\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://b2bee1eea24c1865372ae5a624f7ccfb85c42b615ac74363e3389d382043eed8\",\"dweb:/ipfs/QmfQ2w8JHgMPk2CokEv1QYJtrSEz84fh6VheyWfMsqQjw3\"]},\"contracts/libraries/limit/Claims.sol\":{\"keccak256\":\"0x2e7796e5042a82fac8e50869ad07b4a97a0d008094b1d8c2ea3add580729b58b\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://78817a63f595cd3f210298a270b99fed9d72b28d87f22f0442b5f03044339b19\",\"dweb:/ipfs/QmZSwiyZcQjTHaEx9KPtD9CUF8ewMnqvxHpTrGvWU3w7Kg\"]},\"contracts/libraries/limit/EpochMap.sol\":{\"keccak256\":\"0x6469820c0b831b3837bb187ab5eecc0e441732745ddd6315ef3fcacf73f2c80f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://06a2c94be49eb09e6d219b7b4cfe1a8f60e9978b36c5cc5301dc4dc8f314c1d2\",\"dweb:/ipfs/QmYnREJYpkay3ey3S1uhwxawYAxgZdyLzRCSPNtrWZzmid\"]},\"contracts/libraries/limit/LimitPositions.sol\":{\"keccak256\":\"0xbef58cca843b52cedbd4fb7206e61f72fa953a19227978bc52c1799268171947\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://966deff90f8d11bb9f6131ba6eeebc7f5b5fb5d1b4091d95bb33ced09a791d9e\",\"dweb:/ipfs/QmP3Ronwzp79vq4E4Abs5T9Gu8jdk2rg8iiQsZAf32ypGM\"]},\"contracts/libraries/limit/LimitTicks.sol\":{\"keccak256\":\"0x2b2f355f979ebbbcb20932480937b09e45d9b73bf0dd9a8a4aaf48e511d7fe8d\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://ca969198d60151e3e58180f6581a4c29e9a69eeba14c248893c26f9d8cca11e2\",\"dweb:/ipfs/QmR3cbLsddrVtvGvGNx7o8fcYj1w5sqTUeVg1yeTnZZJoo\"]},\"contracts/libraries/limit/pool/BurnLimitCall.sol\":{\"keccak256\":\"0x16bf77ff3d76442c3f7a3d7af6ff558a12434782fc7b76b8b777d3148fa9d015\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d90dc6945539f0ff453d5d54abdb0a141d66ea3f8c494fd2aa79bdbbf7c487f4\",\"dweb:/ipfs/QmR9KRTFvcavaXtDnBKcj469j674AuK4o1YKqLj4z66J3v\"]},\"contracts/libraries/limit/pool/MintLimitCall.sol\":{\"keccak256\":\"0x100aeb1d2b958c682eb4a65bd8f9765ff82376265402806439e456d6fa6f9946\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://56716c72f2d4cf7ce2e0922ef3397713f298ca21d350ec63ddcc144c5ccd40cb\",\"dweb:/ipfs/QmZmxE6TNZi7N8WNo7XnPgNmuc3mdqQPV16iqB88i8ZGDU\"]},\"contracts/libraries/limit/pool/SnapshotLimitCall.sol\":{\"keccak256\":\"0x0c5737e1d6ff0f4ae16857f5223e3a6e2305cc63e6236d6711877033b11ed6ab\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4f3e44bde71c70b81c8fdd6ea0d7e85704a4a419bcb8d4c30ce4c0ddbb46a999\",\"dweb:/ipfs/QmQ2R6V2VUb84cnieQdnrbWttULdGM1RJQRk8mRfYq77zp\"]},\"contracts/libraries/math/ConstantProduct.sol\":{\"keccak256\":\"0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8\",\"dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED\"]},\"contracts/libraries/math/OverflowMath.sol\":{\"keccak256\":\"0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29\",\"dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T\"]},\"contracts/libraries/pool/FeesCall.sol\":{\"keccak256\":\"0xb47d915fa1158de36d4d0345d9ce7355078eeec7141521eb2835ce0747b08dd0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ecc13f3b564cf10b71b5c6329338a233227db0ee02caad88131b21ac3791778f\",\"dweb:/ipfs/QmZXeyFULJKtBYnJBnGL628tgdkc5oWwzBcxb27bSbAZge\"]},\"contracts/libraries/pool/QuoteCall.sol\":{\"keccak256\":\"0xcaf4ba9df82e5314cbb90a2d295ec96fb3b905e1cbb10886be421157679cd58d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://315af3bb533e38230b587783ac74249b1554a206519e83b774abed6362af605e\",\"dweb:/ipfs/QmaXoaayBBLKseZTprMYe6AkGb2AFkPBYA7hCDPjqbbGqC\"]},\"contracts/libraries/pool/SampleCall.sol\":{\"keccak256\":\"0x164ef9d8eabaeeee2219e8a9ff2ee0d8c9f9b580b09cf766205e2ecef5e899cb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d16c401f176615cfe62b4f242426c3d674367b14feda3f5e5c0c6ad99403d4be\",\"dweb:/ipfs/QmbhohgcHi5bMvc6ogiyv1YaqrLwq1gfWFS3JAXH4x718R\"]},\"contracts/libraries/pool/SwapCall.sol\":{\"keccak256\":\"0xf374ae7fae027d2f1bbf176c6090ba476f2b4b2278ee345f1a9fcbb5301fa3c4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bfce05020484bc6ff4f5ffae6671d3c9e9eb42357cde50af2a3283ce82d1765a\",\"dweb:/ipfs/QmWzDQQ2hFRxLh4ZzWizjaF4kLNuhccWenuVYjNCDqhRYc\"]},\"contracts/libraries/range/RangePositions.sol\":{\"keccak256\":\"0xfd5d33da316411e5fcd85aba2de23957fdd2260f3574d7d722967454459f93f3\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://1441e97805c07b39e4311e279ff51733bdbb7ec490d06e7ab69f4a26ada768c5\",\"dweb:/ipfs/QmZZQ4eCruFxGCJvtwc1oHaThKkqgVM5wyqPpm2PTr43KG\"]},\"contracts/libraries/range/RangeTicks.sol\":{\"keccak256\":\"0x38a551df38f0cddd334bb500a06912fadfab8481a0df59b2ea5c07673961f74c\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://225702e06e66012489a2b45ab0ea9740ede94e7a34aae73b6d16e33a8d74dcd0\",\"dweb:/ipfs/QmRKNER6fXqBoWrTKQ1ruPCcYzz8fbM3PepNB4n47Nrtjy\"]},\"contracts/libraries/range/math/FeeMath.sol\":{\"keccak256\":\"0x81c976abdc41c8af4e3276741b1824157d82eb60eca512dee17a28539832df4f\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://7435150736fdc891d7c503adc4d027334045d82e67d3112edc30f6dc41892c49\",\"dweb:/ipfs/QmTNAoETKtyBSmq2PaPcFQdxG8VhA56EHfPc3WpeRLPKHH\"]},\"contracts/libraries/range/pool/BurnRangeCall.sol\":{\"keccak256\":\"0x871883ba0b407a2c0552d6da9d74beaac5b2b12ec5b21ac1ed14049137255633\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8e6a24a276e9025ffb6e9c2dad3608fa253911025a71ebb7f1cf7fe11633b1df\",\"dweb:/ipfs/QmVsA9zQSc4MYoYMvXCYvLqubnt1fpFb2md4aaSE7Aozsw\"]},\"contracts/libraries/range/pool/MintRangeCall.sol\":{\"keccak256\":\"0x4ffd78f7b88ac07188f5e1cc3e6c8407d3c9ffb010b5cf3e4439d20583740996\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9906280a2807a369ee9ded2f1ed33b757fbe2fd08092b67fbf89678d1795e75c\",\"dweb:/ipfs/QmZaDKhXBuHUn3TRcjKkfc9paksBpRHdFC5veeW2xjrXvr\"]},\"contracts/libraries/range/pool/SnapshotRangeCall.sol\":{\"keccak256\":\"0x9fe5c667eaeaae638e607e61f9e1f76499702c9f4c750768587b5979f5c4e473\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd2a678d053b15e67ba8ddf2ef861dc6f87f6383280d5804bf92130216ad1c24\",\"dweb:/ipfs/QmPp4eUVGbLTrgWE8hVnAzENUMSfqXiPo1xV4uXkmp9dVR\"]},\"contracts/libraries/utils/Bytes.sol\":{\"keccak256\":\"0x461969264908704100e3195ed51ca299ddcfa8bafcd8c2bf55e159e44d4b6b73\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ded39fcbe8dd11f7ba5aa5bb319a733529076c03a2d332812913fbab4dbecc00\",\"dweb:/ipfs/QmeZmu4exQa8hSbfEabw3hxcS9n1qNf8Uiun5hxao9GZT9\"]},\"contracts/libraries/utils/Collect.sol\":{\"keccak256\":\"0xc4a6b4dcb543dba1b9cf5288dc8968457771fe4d8d1a980459ea21f5de38e94b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c3bfed6dfcc73048bfcdeea301cbac3c3c712f2a6dce5ecf5c93cba25a63338a\",\"dweb:/ipfs/QmbwoSprsiTna7ZLQMoi7dnnQNsTvcxN7UbcYsNVmE7iyG\"]},\"contracts/libraries/utils/PositionTokens.sol\":{\"keccak256\":\"0xf08d7ba95f79d677e2b4e32abc41c0f86ca11fbf6d056d1de06fe5c3ebdc9960\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://fabb91eb2b0f6823b48bdb245f9ac650958b526179e86b44683ba7b691843d79\",\"dweb:/ipfs/QmWsGVaBvQ3RFwu94BvAebRHP1NCzJ5dLfX7VTeWXjG4if\"]},\"contracts/libraries/utils/SafeCast.sol\":{\"keccak256\":\"0xc4ec846404c1111c6353c8b2251469e07f32570b1ffafc58701c385c458c21e2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://51b7397ff450c2aedafdcb40a6ca181c8e1947fb409dbbcd8b000b8eee2e9fcf\",\"dweb:/ipfs/QmQ3wPdCtuLreoEkvFoifsx4drC5kT3p2CwGKBBn2qQso4\"]},\"contracts/libraries/utils/SafeTransfers.sol\":{\"keccak256\":\"0x1dcd632dc5123e4f15ad19c8c88ba0a3bf42f0a5b91211169aecfcc8523ed0f8\",\"license\":\"Unlicense\",\"urls\":[\"bzz-raw://67467c0c05b10847e84f3f7229027de3f80f448cb8589a0b4314b45c76220dd1\",\"dweb:/ipfs/QmS17vjUJ16LmtZ7cKdQAGZKcdEYck5NsoAhjGgZdc3b9S\"]},\"contracts/libraries/utils/String.sol\":{\"keccak256\":\"0x0d03855bfeabee266fa8252cbcac9bab81b5a951d3c0a99ae51f2e4a1942dbf2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2fba9aae08e1406ee8c7c21da98bd72c4f34e55323ffbb0146e9ec8f0a4a06e3\",\"dweb:/ipfs/QmbT189JtHc8esSwJ9TCkLhYVBVguJr2noepxKh5XrjEfj\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0\",\"dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34\",\"dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd\",\"dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92\",\"dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f\",\"dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "factory_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardInvalidState"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReadOnlyReentrantCall"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [{"internalType": "struct PoolsharkStructs.BurnLimitParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint128", "name": "burnPercent", "type": "uint128"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "int24", "name": "claim", "type": "int24"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}]}], "stateMutability": "nonpayable", "type": "function", "name": "burnLimit"}, {"inputs": [{"internalType": "struct PoolsharkStructs.BurnRangeParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "uint128", "name": "burnPercent", "type": "uint128"}]}], "stateMutability": "nonpayable", "type": "function", "name": "burn<PERSON>ang<PERSON>"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "factory", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.FeesParams", "name": "params", "type": "tuple", "components": [{"internalType": "uint16", "name": "protocolSwapFee0", "type": "uint16"}, {"internalType": "uint16", "name": "protocolSwapFee1", "type": "uint16"}, {"internalType": "uint16", "name": "protocolFillFee0", "type": "uint16"}, {"internalType": "uint16", "name": "protocolFillFee1", "type": "uint16"}, {"internalType": "uint8", "name": "set<PERSON><PERSON><PERSON><PERSON>s", "type": "uint8"}]}], "stateMutability": "nonpayable", "type": "function", "name": "fees", "outputs": [{"internalType": "uint128", "name": "token0Fees", "type": "uint128"}, {"internalType": "uint128", "name": "token1Fees", "type": "uint128"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "genesisTime", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "globalState", "outputs": [{"internalType": "struct PoolsharkStructs.RangePoolState", "name": "pool", "type": "tuple", "components": [{"internalType": "struct PoolsharkStructs.SampleState", "name": "samples", "type": "tuple", "components": [{"internalType": "uint16", "name": "index", "type": "uint16"}, {"internalType": "uint16", "name": "count", "type": "uint16"}, {"internalType": "uint16", "name": "countMax", "type": "uint16"}]}, {"internalType": "uint200", "name": "feeGrowthGlobal0", "type": "uint200"}, {"internalType": "uint200", "name": "feeGrowthGlobal1", "type": "uint200"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160"}, {"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "int56", "name": "tickSecondsAccum", "type": "int56"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}, {"internalType": "uint16", "name": "protocolSwapFee0", "type": "uint16"}, {"internalType": "uint16", "name": "protocolSwapFee1", "type": "uint16"}]}, {"internalType": "struct PoolsharkStructs.LimitPoolState", "name": "pool0", "type": "tuple", "components": [{"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint128", "name": "protocolFees", "type": "uint128"}, {"internalType": "uint16", "name": "protocolFillFee", "type": "uint16"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}]}, {"internalType": "struct PoolsharkStructs.LimitPoolState", "name": "pool1", "type": "tuple", "components": [{"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint128", "name": "protocolFees", "type": "uint128"}, {"internalType": "uint16", "name": "protocolFillFee", "type": "uint16"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}]}, {"internalType": "uint128", "name": "liquidityGlobal", "type": "uint128"}, {"internalType": "uint32", "name": "positionIdNext", "type": "uint32"}, {"internalType": "uint32", "name": "epoch", "type": "uint32"}, {"internalType": "uint8", "name": "unlocked", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "immutables", "outputs": [{"internalType": "struct PoolsharkStructs.LimitImmutables", "name": "", "type": "tuple", "components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "poolImpl", "type": "address"}, {"internalType": "address", "name": "factory", "type": "address"}, {"internalType": "struct PoolsharkStructs.PriceBounds", "name": "bounds", "type": "tuple", "components": [{"internalType": "uint160", "name": "min", "type": "uint160"}, {"internalType": "uint160", "name": "max", "type": "uint160"}]}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "uint32", "name": "genesisTime", "type": "uint32"}, {"internalType": "int16", "name": "tickSpacing", "type": "int16"}, {"internalType": "uint16", "name": "swapFee", "type": "uint16"}]}]}, {"inputs": [{"internalType": "uint16", "name": "newSampleCountMax", "type": "uint16"}], "stateMutability": "nonpayable", "type": "function", "name": "increaseSampleCount"}, {"inputs": [{"internalType": "uint160", "name": "startPrice", "type": "uint160"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "limitTickMap", "outputs": [{"internalType": "uint256", "name": "blocks", "type": "uint256"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "maxPrice", "outputs": [{"internalType": "uint160", "name": "", "type": "uint160"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "minPrice", "outputs": [{"internalType": "uint160", "name": "", "type": "uint160"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.MintLimitParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint96", "name": "mintPercent", "type": "uint96"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"internalType": "bytes", "name": "callbackData", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "mintLimit"}, {"inputs": [{"internalType": "struct PoolsharkStructs.MintRangeParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "uint128", "name": "amount0", "type": "uint128"}, {"internalType": "uint128", "name": "amount1", "type": "uint128"}, {"internalType": "bytes", "name": "callbackData", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "mintRange"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "original", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "poolToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "positions", "outputs": [{"internalType": "uint256", "name": "feeGrowthInside0Last", "type": "uint256"}, {"internalType": "uint256", "name": "feeGrowthInside1Last", "type": "uint256"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "positions0", "outputs": [{"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint32", "name": "epochLast", "type": "uint32"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}, {"internalType": "bool", "name": "crossedInto", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "positions1", "outputs": [{"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint32", "name": "epochLast", "type": "uint32"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}, {"internalType": "bool", "name": "crossedInto", "type": "bool"}]}, {"inputs": [{"internalType": "int16", "name": "tickSpacing", "type": "int16"}], "stateMutability": "pure", "type": "function", "name": "priceBounds", "outputs": [{"internalType": "uint160", "name": "", "type": "uint160"}, {"internalType": "uint160", "name": "", "type": "uint160"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.QuoteParams", "name": "params", "type": "tuple", "components": [{"internalType": "uint160", "name": "priceLimit", "type": "uint160"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "bool", "name": "exactIn", "type": "bool"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}]}], "stateMutability": "view", "type": "function", "name": "quote", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint160", "name": "", "type": "uint160"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rangeTickMap", "outputs": [{"internalType": "uint256", "name": "blocks", "type": "uint256"}]}, {"inputs": [{"internalType": "uint32[]", "name": "secondsAgo", "type": "uint32[]"}], "stateMutability": "view", "type": "function", "name": "sample", "outputs": [{"internalType": "int56[]", "name": "tickSecondsAccum", "type": "int56[]"}, {"internalType": "uint160[]", "name": "secondsPerLiquidityAccum", "type": "uint160[]"}, {"internalType": "uint160", "name": "averagePrice", "type": "uint160"}, {"internalType": "uint128", "name": "averageLiquidity", "type": "uint128"}, {"internalType": "int24", "name": "averageTick", "type": "int24"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "samples", "outputs": [{"internalType": "uint32", "name": "blockTimestamp", "type": "uint32"}, {"internalType": "int56", "name": "tickSecondsAccum", "type": "int56"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.SnapshotLimitParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint128", "name": "burnPercent", "type": "uint128"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "int24", "name": "claim", "type": "int24"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}]}], "stateMutability": "view", "type": "function", "name": "snapshotLimit", "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}, {"internalType": "uint128", "name": "", "type": "uint128"}]}, {"inputs": [{"internalType": "uint32", "name": "positionId", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "snapshotRange", "outputs": [{"internalType": "int56", "name": "tickSecondsAccum", "type": "int56"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160"}, {"internalType": "uint128", "name": "feesOwed0", "type": "uint128"}, {"internalType": "uint128", "name": "feesOwed1", "type": "uint128"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.SwapParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint160", "name": "priceLimit", "type": "uint160"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "bool", "name": "exactIn", "type": "bool"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"internalType": "bytes", "name": "callbackData", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "swap", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}, {"internalType": "int256", "name": "", "type": "int256"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "swapFee", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "tickSpacing", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [{"internalType": "int24", "name": "", "type": "int24"}], "stateMutability": "view", "type": "function", "name": "ticks", "outputs": [{"internalType": "struct PoolsharkStructs.RangeTick", "name": "range", "type": "tuple", "components": [{"internalType": "uint200", "name": "feeGrowthOutside0", "type": "uint200"}, {"internalType": "uint200", "name": "feeGrowthOutside1", "type": "uint200"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccumOutside", "type": "uint160"}, {"internalType": "int56", "name": "tickSecondsAccumOutside", "type": "int56"}, {"internalType": "int128", "name": "liquidityDelta", "type": "int128"}, {"internalType": "uint128", "name": "liquidityAbsolute", "type": "uint128"}]}, {"internalType": "struct PoolsharkStructs.LimitTick", "name": "limit", "type": "tuple", "components": [{"internalType": "uint160", "name": "priceAt", "type": "uint160"}, {"internalType": "int128", "name": "liquidityDelta", "type": "int128"}, {"internalType": "uint128", "name": "liquidityAbsolute", "type": "uint128"}]}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "token0", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "token1", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/LimitPool.sol": "LimitPool"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/LimitPool.sol": {"keccak256": "0x63e6fb0b3aa1758a080623dd6831836d259b34e34644ca031e9da48e72feea37", "urls": ["bzz-raw://eca6e515c3df87abe97115e09409107ddc3161e2c9f640e0d008d36a40ddda39", "dweb:/ipfs/QmdTdME8PVUqakTNSHeFFH6KyMqZGGTEjVnZCxZUMV8eu2"], "license": "BUSL-1.1"}, "contracts/base/storage/LimitPoolFactoryStorage.sol": {"keccak256": "0xec8efbb85bf6d831dc0c3910eccbe1b6def550ac0b88eef62b4a7c0c314a31ae", "urls": ["bzz-raw://*****************************9ea2ec7d4f81a0512f5b83901d3a9a4631f", "dweb:/ipfs/QmeKyWJwsX9LwB5A8ovGm7J2nToJvR1dnE3sMww46ERb55"], "license": "BUSL-1.1"}, "contracts/base/storage/LimitPoolImmutables.sol": {"keccak256": "0x12df280adc3ad5199cdb4ef07df724427f9694a2eaae240f31ba887b0d5946de", "urls": ["bzz-raw://829502a9103d01f0ce94459377da9b972629e6b227ceff9a7d6d01f866fc4715", "dweb:/ipfs/QmTj5jRT6Pff6efUgU11ASNaK1PoLykwKNWFAoPndyfZgQ"], "license": "BSD"}, "contracts/base/storage/LimitPoolStorage.sol": {"keccak256": "0x063aa09f6535188ff3bbe5ca25af30433460e10786c9fc4ea061a572d04b7ca3", "urls": ["bzz-raw://878402ff2c89a596e9d8a18278bc484df2301296a0725af83747809227bbe315", "dweb:/ipfs/QmSKz1tQRxPiP5cgJo8kg8RieStHHzbMk3zSCjVCUYHKKL"], "license": "BUSL-1.1"}, "contracts/external/openzeppelin/security/LimitReentrancyGuard.sol": {"keccak256": "0x624b4055d71a8b5d6af63eb2ae3c237d13183e00c9114171faa7ec720d65e223", "urls": ["bzz-raw://56de4a517b56017d985101ff56e22c9ebb03b1d9ad2b565767475095115b74c2", "dweb:/ipfs/QmY7kcsj2uKCCXVT4iamQLtgyeNshhb5FbShGt5QaQZ8dX"], "license": "MIT"}, "contracts/external/solady/Clone.sol": {"keccak256": "0xe7dc35cc81529e1e43b249f99de3a26e595ffd209450b85f3e8bc74a0d9aea01", "urls": ["bzz-raw://6d0471fad81c71706936f7b014b0cfcd5f03337450838409c8c02fedc38a8560", "dweb:/ipfs/Qmede4qMoU29saLxaBxaA72kuWt4794HLcB7ZdXHRrBFXC"], "license": "MIT"}, "contracts/external/solady/LibClone.sol": {"keccak256": "0x93750a76e235631c1438283750f8b096026a11d82399fdc002816c55acc1f55a", "urls": ["bzz-raw://e99db3fbe71ac90a31b411481b148d17ef2cb6c748d38216ae233778d0228d4d", "dweb:/ipfs/QmWkt4Q5fQkMkq8a3Vf2KjKBqkAvqzpGFLy1rFvWzyYN5k"], "license": "MIT"}, "contracts/interfaces/IERC20Minimal.sol": {"keccak256": "0xaff2e27e82f63ae2a2cdab428a0a29e0c035168813b639ff0c813f9cc13487a0", "urls": ["bzz-raw://15f34a8337c2fdd90c9329b0104bbbe4a949b8e4431f5f52465e0e75a620db58", "dweb:/ipfs/QmWaJaHG4j8xe4ubgRQyYr8BqvyqmsKQUuQLFTXpQ8D42P"], "license": "GPL-2.0-or-later"}, "contracts/interfaces/IPool.sol": {"keccak256": "0x67f42bc51b5a8fe379805a5c07826872c4503163d53894c59173e8f6d72a2b53", "urls": ["bzz-raw://1662beddbe5fec900079fcb76eb1371b9af88f3a90db0bd828c4b57c460a659e", "dweb:/ipfs/QmQXx2t6iSgyjaXEsdaXMpaStjmVuRMiCjJA8x1CWtnEYu"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/IPositionERC1155.sol": {"keccak256": "0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c", "urls": ["bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11", "dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe"], "license": "MIT"}, "contracts/interfaces/callbacks/ILimitPoolCallback.sol": {"keccak256": "0xbdd400055110618e0e90afe1064193676db3fcdcc621573b45c3512e8f062821", "urls": ["bzz-raw://86c3b364c617fec20bfd96a843feca91d950bd2289b7650bd2a96ac9e7c380a7", "dweb:/ipfs/QmWR5PJQu6TuQb7ipouKkiwN5Lq11khdTTDAtaXxJyGnrX"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPool.sol": {"keccak256": "0x8754512ae636a8871b11412ad16735be460142a48ee9f0fc50310f4d4aa45227", "urls": ["bzz-raw://9621227f4ab660043534cb124559d96d42cefad3441b61a7a82be81ef8674dcf", "dweb:/ipfs/QmYZsFqXwFW6dcYSrjc4R1Lu1r9MbMbo6EQH6Rk2yqztKA"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPoolFactory.sol": {"keccak256": "0x89fc412319c30451896a1db8d77bb528c6df383523cf7f278534a645a4a49733", "urls": ["bzz-raw://495509c3a546e0b85b34a8ef7fc114ca5bb98d831c8bbd176b42d36703f00fd0", "dweb:/ipfs/QmcFR6a32Fp7s4hRDmJfp7GaC65uQUtRo6VhE6ygCpZefm"], "license": "BUSL-1.1"}, "contracts/interfaces/limit/ILimitPoolManager.sol": {"keccak256": "0x3569f41d49e23238d34b3dc91e7fe7587c8fa141cacc97bf382caa1b580837de", "urls": ["bzz-raw://aa5545c53994d586c1e7e6aaeb7d0522a678ae012867268e93e3f65060e7360f", "dweb:/ipfs/QmU34ZDnYxCDV6i8fxq13AU5LptW3rXgY5ZYpLy2t8sLqk"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPoolStorageView.sol": {"keccak256": "0x81173f20ed82249830e6af5b51505e4373c86f2cba9ea7785d4f41cc0b54ee52", "urls": ["bzz-raw://7905082bb0bf3bc4191e6fccdedd30bf2d662edf446a87a14df5c4cc5422759c", "dweb:/ipfs/QmdTppuhdTJjgLL6THPpown2Km6ya5bh6Q1qxAiw9oGo9b"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPoolView.sol": {"keccak256": "0x298606a582d43209c74b9abf469e3576876444b964ce02be0b5d52d8662fd88a", "urls": ["bzz-raw://adad5cd4a5cbf1c874e8351def6f31877d305e28b6aaf6d9dc539c9d7417c420", "dweb:/ipfs/QmZTbR2jJzQe2T6GpDoGLPD5gAgLks6XFqzPewSUR1kBpw"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePool.sol": {"keccak256": "0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf", "urls": ["bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab", "dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePoolFactory.sol": {"keccak256": "0xa38c314350de1c59bd90e521fe0920d1e5008009aca592697b07194346da8cc1", "urls": ["bzz-raw://53551b95615f6a6259753c7e9fc189a46dfe971b83cb417af4d4c593278f9d60", "dweb:/ipfs/QmXFnKtuRVJgs9HbNaNZna7mkmBRhPxtiVfkJKWuqytscS"], "license": "GPLv3"}, "contracts/interfaces/range/IRangePoolManager.sol": {"keccak256": "0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139", "urls": ["bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065", "dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/LimitPoolStructs.sol": {"keccak256": "0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4", "urls": ["bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef", "dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/interfaces/structs/RangePoolStructs.sol": {"keccak256": "0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd", "urls": ["bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9", "dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx"], "license": "GPLv3"}, "contracts/libraries/Samples.sol": {"keccak256": "0xae162c0e142bbe444c3748135d74bf272849768abca62c1908794bb72551c0eb", "urls": ["bzz-raw://cf38a14a2a9653dc9b44ad37f091b7c6117be6ca8a76b5f9e279998bd9060763", "dweb:/ipfs/QmYxJm1w9YL4bYHJqNcLTRxiwYKH68dTzNGGQiQxFmnADE"], "license": "GPLv3"}, "contracts/libraries/TickMap.sol": {"keccak256": "0xa77a5fb9acdce8ed769375832ba046cd4173eb47112b3083021b1637df903555", "urls": ["bzz-raw://71ed48dae8ec94546989456c1d2dff0ff412db9d364d2996dc2f5877a1c82c64", "dweb:/ipfs/QmStzY8Q86bF8A7SLasHtuKF3B6MNzruBidc8YhU1yLwsK"], "license": "GPLv3"}, "contracts/libraries/Ticks.sol": {"keccak256": "0xae060497e2eed43dae98cffe0889ca1d0ee2810efda6d6772c9aa25db384fd94", "urls": ["bzz-raw://b2bee1eea24c1865372ae5a624f7ccfb85c42b615ac74363e3389d382043eed8", "dweb:/ipfs/QmfQ2w8JHgMPk2CokEv1QYJtrSEz84fh6VheyWfMsqQjw3"], "license": "GPLv3"}, "contracts/libraries/limit/Claims.sol": {"keccak256": "0x2e7796e5042a82fac8e50869ad07b4a97a0d008094b1d8c2ea3add580729b58b", "urls": ["bzz-raw://78817a63f595cd3f210298a270b99fed9d72b28d87f22f0442b5f03044339b19", "dweb:/ipfs/QmZSwiyZcQjTHaEx9KPtD9CUF8ewMnqvxHpTrGvWU3w7Kg"], "license": "BUSL-1.1"}, "contracts/libraries/limit/EpochMap.sol": {"keccak256": "0x6469820c0b831b3837bb187ab5eecc0e441732745ddd6315ef3fcacf73f2c80f", "urls": ["bzz-raw://06a2c94be49eb09e6d219b7b4cfe1a8f60e9978b36c5cc5301dc4dc8f314c1d2", "dweb:/ipfs/QmYnREJYpkay3ey3S1uhwxawYAxgZdyLzRCSPNtrWZzmid"], "license": "BUSL-1.1"}, "contracts/libraries/limit/LimitPositions.sol": {"keccak256": "0xbef58cca843b52cedbd4fb7206e61f72fa953a19227978bc52c1799268171947", "urls": ["bzz-raw://966deff90f8d11bb9f6131ba6eeebc7f5b5fb5d1b4091d95bb33ced09a791d9e", "dweb:/ipfs/QmP3Ronwzp79vq4E4Abs5T9Gu8jdk2rg8iiQsZAf32ypGM"], "license": "BUSL-1.1"}, "contracts/libraries/limit/LimitTicks.sol": {"keccak256": "0x2b2f355f979ebbbcb20932480937b09e45d9b73bf0dd9a8a4aaf48e511d7fe8d", "urls": ["bzz-raw://ca969198d60151e3e58180f6581a4c29e9a69eeba14c248893c26f9d8cca11e2", "dweb:/ipfs/QmR3cbLsddrVtvGvGNx7o8fcYj1w5sqTUeVg1yeTnZZJoo"], "license": "GPLv3"}, "contracts/libraries/limit/pool/BurnLimitCall.sol": {"keccak256": "0x16bf77ff3d76442c3f7a3d7af6ff558a12434782fc7b76b8b777d3148fa9d015", "urls": ["bzz-raw://d90dc6945539f0ff453d5d54abdb0a141d66ea3f8c494fd2aa79bdbbf7c487f4", "dweb:/ipfs/QmR9KRTFvcavaXtDnBKcj469j674AuK4o1YKqLj4z66J3v"], "license": "MIT"}, "contracts/libraries/limit/pool/MintLimitCall.sol": {"keccak256": "0x100aeb1d2b958c682eb4a65bd8f9765ff82376265402806439e456d6fa6f9946", "urls": ["bzz-raw://56716c72f2d4cf7ce2e0922ef3397713f298ca21d350ec63ddcc144c5ccd40cb", "dweb:/ipfs/QmZmxE6TNZi7N8WNo7XnPgNmuc3mdqQPV16iqB88i8ZGDU"], "license": "MIT"}, "contracts/libraries/limit/pool/SnapshotLimitCall.sol": {"keccak256": "0x0c5737e1d6ff0f4ae16857f5223e3a6e2305cc63e6236d6711877033b11ed6ab", "urls": ["bzz-raw://4f3e44bde71c70b81c8fdd6ea0d7e85704a4a419bcb8d4c30ce4c0ddbb46a999", "dweb:/ipfs/QmQ2R6V2VUb84cnieQdnrbWttULdGM1RJQRk8mRfYq77zp"], "license": "MIT"}, "contracts/libraries/math/ConstantProduct.sol": {"keccak256": "0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3", "urls": ["bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8", "dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED"], "license": "GPL-3.0-or-later"}, "contracts/libraries/math/OverflowMath.sol": {"keccak256": "0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b", "urls": ["bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29", "dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T"], "license": "MIT"}, "contracts/libraries/pool/FeesCall.sol": {"keccak256": "0xb47d915fa1158de36d4d0345d9ce7355078eeec7141521eb2835ce0747b08dd0", "urls": ["bzz-raw://ecc13f3b564cf10b71b5c6329338a233227db0ee02caad88131b21ac3791778f", "dweb:/ipfs/QmZXeyFULJKtBYnJBnGL628tgdkc5oWwzBcxb27bSbAZge"], "license": "MIT"}, "contracts/libraries/pool/QuoteCall.sol": {"keccak256": "0xcaf4ba9df82e5314cbb90a2d295ec96fb3b905e1cbb10886be421157679cd58d", "urls": ["bzz-raw://315af3bb533e38230b587783ac74249b1554a206519e83b774abed6362af605e", "dweb:/ipfs/QmaXoaayBBLKseZTprMYe6AkGb2AFkPBYA7hCDPjqbbGqC"], "license": "MIT"}, "contracts/libraries/pool/SampleCall.sol": {"keccak256": "0x164ef9d8eabaeeee2219e8a9ff2ee0d8c9f9b580b09cf766205e2ecef5e899cb", "urls": ["bzz-raw://d16c401f176615cfe62b4f242426c3d674367b14feda3f5e5c0c6ad99403d4be", "dweb:/ipfs/QmbhohgcHi5bMvc6ogiyv1YaqrLwq1gfWFS3JAXH4x718R"], "license": "MIT"}, "contracts/libraries/pool/SwapCall.sol": {"keccak256": "0xf374ae7fae027d2f1bbf176c6090ba476f2b4b2278ee345f1a9fcbb5301fa3c4", "urls": ["bzz-raw://bfce05020484bc6ff4f5ffae6671d3c9e9eb42357cde50af2a3283ce82d1765a", "dweb:/ipfs/QmWzDQQ2hFRxLh4ZzWizjaF4kLNuhccWenuVYjNCDqhRYc"], "license": "MIT"}, "contracts/libraries/range/RangePositions.sol": {"keccak256": "0xfd5d33da316411e5fcd85aba2de23957fdd2260f3574d7d722967454459f93f3", "urls": ["bzz-raw://1441e97805c07b39e4311e279ff51733bdbb7ec490d06e7ab69f4a26ada768c5", "dweb:/ipfs/QmZZQ4eCruFxGCJvtwc1oHaThKkqgVM5wyqPpm2PTr43KG"], "license": "GPLv3"}, "contracts/libraries/range/RangeTicks.sol": {"keccak256": "0x38a551df38f0cddd334bb500a06912fadfab8481a0df59b2ea5c07673961f74c", "urls": ["bzz-raw://225702e06e66012489a2b45ab0ea9740ede94e7a34aae73b6d16e33a8d74dcd0", "dweb:/ipfs/QmRKNER6fXqBoWrTKQ1ruPCcYzz8fbM3PepNB4n47Nrtjy"], "license": "GPLv3"}, "contracts/libraries/range/math/FeeMath.sol": {"keccak256": "0x81c976abdc41c8af4e3276741b1824157d82eb60eca512dee17a28539832df4f", "urls": ["bzz-raw://7435150736fdc891d7c503adc4d027334045d82e67d3112edc30f6dc41892c49", "dweb:/ipfs/QmTNAoETKtyBSmq2PaPcFQdxG8VhA56EHfPc3WpeRLPKHH"], "license": "GPLv3"}, "contracts/libraries/range/pool/BurnRangeCall.sol": {"keccak256": "0x871883ba0b407a2c0552d6da9d74beaac5b2b12ec5b21ac1ed14049137255633", "urls": ["bzz-raw://8e6a24a276e9025ffb6e9c2dad3608fa253911025a71ebb7f1cf7fe11633b1df", "dweb:/ipfs/QmVsA9zQSc4MYoYMvXCYvLqubnt1fpFb2md4aaSE7Aozsw"], "license": "MIT"}, "contracts/libraries/range/pool/MintRangeCall.sol": {"keccak256": "0x4ffd78f7b88ac07188f5e1cc3e6c8407d3c9ffb010b5cf3e4439d20583740996", "urls": ["bzz-raw://9906280a2807a369ee9ded2f1ed33b757fbe2fd08092b67fbf89678d1795e75c", "dweb:/ipfs/QmZaDKhXBuHUn3TRcjKkfc9paksBpRHdFC5veeW2xjrXvr"], "license": "MIT"}, "contracts/libraries/range/pool/SnapshotRangeCall.sol": {"keccak256": "0x9fe5c667eaeaae638e607e61f9e1f76499702c9f4c750768587b5979f5c4e473", "urls": ["bzz-raw://cd2a678d053b15e67ba8ddf2ef861dc6f87f6383280d5804bf92130216ad1c24", "dweb:/ipfs/QmPp4eUVGbLTrgWE8hVnAzENUMSfqXiPo1xV4uXkmp9dVR"], "license": "MIT"}, "contracts/libraries/utils/Bytes.sol": {"keccak256": "0x461969264908704100e3195ed51ca299ddcfa8bafcd8c2bf55e159e44d4b6b73", "urls": ["bzz-raw://ded39fcbe8dd11f7ba5aa5bb319a733529076c03a2d332812913fbab4dbecc00", "dweb:/ipfs/QmeZmu4exQa8hSbfEabw3hxcS9n1qNf8Uiun5hxao9GZT9"], "license": "MIT"}, "contracts/libraries/utils/Collect.sol": {"keccak256": "0xc4a6b4dcb543dba1b9cf5288dc8968457771fe4d8d1a980459ea21f5de38e94b", "urls": ["bzz-raw://c3bfed6dfcc73048bfcdeea301cbac3c3c712f2a6dce5ecf5c93cba25a63338a", "dweb:/ipfs/QmbwoSprsiTna7ZLQMoi7dnnQNsTvcxN7UbcYsNVmE7iyG"], "license": "MIT"}, "contracts/libraries/utils/PositionTokens.sol": {"keccak256": "0xf08d7ba95f79d677e2b4e32abc41c0f86ca11fbf6d056d1de06fe5c3ebdc9960", "urls": ["bzz-raw://fabb91eb2b0f6823b48bdb245f9ac650958b526179e86b44683ba7b691843d79", "dweb:/ipfs/QmWsGVaBvQ3RFwu94BvAebRHP1NCzJ5dLfX7VTeWXjG4if"], "license": "GPLv3"}, "contracts/libraries/utils/SafeCast.sol": {"keccak256": "0xc4ec846404c1111c6353c8b2251469e07f32570b1ffafc58701c385c458c21e2", "urls": ["bzz-raw://51b7397ff450c2aedafdcb40a6ca181c8e1947fb409dbbcd8b000b8eee2e9fcf", "dweb:/ipfs/QmQ3wPdCtuLreoEkvFoifsx4drC5kT3p2CwGKBBn2qQso4"], "license": "GPL-2.0-or-later"}, "contracts/libraries/utils/SafeTransfers.sol": {"keccak256": "0x1dcd632dc5123e4f15ad19c8c88ba0a3bf42f0a5b91211169aecfcc8523ed0f8", "urls": ["bzz-raw://67467c0c05b10847e84f3f7229027de3f80f448cb8589a0b4314b45c76220dd1", "dweb:/ipfs/QmS17vjUJ16LmtZ7cKdQAGZKcdEYck5NsoAhjGgZdc3b9S"], "license": "Unlicense"}, "contracts/libraries/utils/String.sol": {"keccak256": "0x0d03855bfeabee266fa8252cbcac9bab81b5a951d3c0a99ae51f2e4a1942dbf2", "urls": ["bzz-raw://2fba9aae08e1406ee8c7c21da98bd72c4f34e55323ffbb0146e9ec8f0a4a06e3", "dweb:/ipfs/QmbT189JtHc8esSwJ9TCkLhYVBVguJr2noepxKh5XrjEfj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238", "urls": ["bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0", "dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b", "urls": ["bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34", "dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca", "urls": ["bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd", "dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7", "urls": ["bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92", "dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1", "urls": ["bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f", "dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy"], "license": "MIT"}}, "version": 1}, "id": 0}