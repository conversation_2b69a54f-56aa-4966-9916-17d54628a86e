{"abi": [{"type": "event", "name": "FeeToTransfer", "inputs": [{"name": "previousFeeTo", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newFeeTo", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnerTransfer", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "StakeRange", "inputs": [{"name": "pool", "type": "address", "indexed": false, "internalType": "address"}, {"name": "positionId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "recipient", "type": "address", "indexed": false, "internalType": "address"}, {"name": "feeGrowthInside0Last", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "feeGrowthInside1Last", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "liquidity", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "StakeRangeAccrued", "inputs": [{"name": "pool", "type": "address", "indexed": false, "internalType": "address"}, {"name": "positionId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "feeGrowth0Accrued", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "feeGrowth1Accrued", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "UnstakeRange", "inputs": [{"name": "pool", "type": "address", "indexed": false, "internalType": "address"}, {"name": "positionId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "recipient", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousFeeTo\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newFeeTo\",\"type\":\"address\"}],\"name\":\"FeeToTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnerTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"feeGrowthInside0Last\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"feeGrowthInside1Last\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"}],\"name\":\"StakeRange\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"feeGrowth0Accrued\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"feeGrowth1Accrued\",\"type\":\"uint256\"}],\"name\":\"StakeRangeAccrued\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"}],\"name\":\"UnstakeRange\",\"type\":\"event\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/base/events/RangeStakerEvents.sol\":\"RangeStakerEvents\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/base/events/RangeStakerEvents.sol\":{\"keccak256\":\"0x17f81479cfcc20afe3ba8c14f4aae43c31dc599529fc5836737446f392064f02\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://1390badbab0c7f323a6eb2a45fed119bca421f3d3d15a8f51dc27e833e77d9f3\",\"dweb:/ipfs/QmbpUBjzM5NuXGKhav7chipxNykwA3utz2TMNChEfnYvQU\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "previousFeeTo", "type": "address", "indexed": true}, {"internalType": "address", "name": "newFeeTo", "type": "address", "indexed": true}], "type": "event", "name": "FeeToTransfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnerTransfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pool", "type": "address", "indexed": false}, {"internalType": "uint32", "name": "positionId", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "recipient", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "feeGrowthInside0Last", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "feeGrowthInside1Last", "type": "uint256", "indexed": false}, {"internalType": "uint128", "name": "liquidity", "type": "uint128", "indexed": false}], "type": "event", "name": "StakeRange", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pool", "type": "address", "indexed": false}, {"internalType": "uint32", "name": "positionId", "type": "uint32", "indexed": false}, {"internalType": "uint256", "name": "feeGrowth0Accrued", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "feeGrowth1Accrued", "type": "uint256", "indexed": false}], "type": "event", "name": "StakeRangeAccrued", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pool", "type": "address", "indexed": false}, {"internalType": "uint32", "name": "positionId", "type": "uint32", "indexed": false}, {"internalType": "address", "name": "recipient", "type": "address", "indexed": false}], "type": "event", "name": "UnstakeRange", "anonymous": false}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/base/events/RangeStakerEvents.sol": "RangeStakerEvents"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/base/events/RangeStakerEvents.sol": {"keccak256": "0x17f81479cfcc20afe3ba8c14f4aae43c31dc599529fc5836737446f392064f02", "urls": ["bzz-raw://1390badbab0c7f323a6eb2a45fed119bca421f3d3d15a8f51dc27e833e77d9f3", "dweb:/ipfs/QmbpUBjzM5NuXGKhav7chipxNykwA3utz2TMNChEfnYvQU"], "license": "GPL-3.0-or-later"}}, "version": 1}, "id": 8}