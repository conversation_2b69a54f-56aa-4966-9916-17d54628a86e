{"abi": [{"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"balanceOf(address)": "70a08231"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"balanceOf(address)\":{\"params\":{\"account\":\"The address for which to look up the balance for\"},\"returns\":{\"_0\":\"amount of tokens held by the account\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"balanceOf(address)\":{\"notice\":\"Returns the balance of a token\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/IERC20Minimal.sol\":\"IERC20Minimal\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/IERC20Minimal.sol\":{\"keccak256\":\"0xaff2e27e82f63ae2a2cdab428a0a29e0c035168813b639ff0c813f9cc13487a0\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://15f34a8337c2fdd90c9329b0104bbbe4a949b8e4431f5f52465e0e75a620db58\",\"dweb:/ipfs/QmWaJaHG4j8xe4ubgRQyYr8BqvyqmsKQUuQLFTXpQ8D42P\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"balanceOf(address)": {"params": {"account": "The address for which to look up the balance for"}, "returns": {"_0": "amount of tokens held by the account"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"balanceOf(address)": {"notice": "Returns the balance of a token"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/IERC20Minimal.sol": "IERC20Minimal"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/IERC20Minimal.sol": {"keccak256": "0xaff2e27e82f63ae2a2cdab428a0a29e0c035168813b639ff0c813f9cc13487a0", "urls": ["bzz-raw://15f34a8337c2fdd90c9329b0104bbbe4a949b8e4431f5f52465e0e75a620db58", "dweb:/ipfs/QmWaJaHG4j8xe4ubgRQyYr8BqvyqmsKQUuQLFTXpQ8D42P"], "license": "GPL-2.0-or-later"}}, "version": 1}, "id": 17}