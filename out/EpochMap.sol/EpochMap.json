{"abi": [{"type": "event", "name": "SyncLimitTick", "inputs": [{"name": "epoch", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "tick", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}], "bytecode": {"object": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212203887aff2700b4e572a5cc5e4014f5c7ca73a30a0a1ecd89517dfa9179f7286d664736f6c634300080d0033", "sourceMap": "157:3419:42:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;157:3419:42;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212203887aff2700b4e572a5cc5e4014f5c7ca73a30a0a1ecd89517dfa9179f7286d664736f6c634300080d0033", "sourceMap": "157:3419:42:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"tick\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"name\":\"SyncLimitTick\",\"type\":\"event\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/limit/EpochMap.sol\":\"EpochMap\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/structs/LimitPoolStructs.sol\":{\"keccak256\":\"0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef\",\"dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/libraries/limit/EpochMap.sol\":{\"keccak256\":\"0x6469820c0b831b3837bb187ab5eecc0e441732745ddd6315ef3fcacf73f2c80f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://06a2c94be49eb09e6d219b7b4cfe1a8f60e9978b36c5cc5301dc4dc8f314c1d2\",\"dweb:/ipfs/QmYnREJYpkay3ey3S1uhwxawYAxgZdyLzRCSPNtrWZzmid\"]},\"contracts/libraries/math/ConstantProduct.sol\":{\"keccak256\":\"0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8\",\"dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED\"]},\"contracts/libraries/math/OverflowMath.sol\":{\"keccak256\":\"0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29\",\"dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint32", "name": "epoch", "type": "uint32", "indexed": false}, {"internalType": "int24", "name": "tick", "type": "int24", "indexed": false}, {"internalType": "bool", "name": "zeroForOne", "type": "bool", "indexed": false}], "type": "event", "name": "SyncLimitTick", "anonymous": false}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/limit/EpochMap.sol": "EpochMap"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/LimitPoolStructs.sol": {"keccak256": "0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4", "urls": ["bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef", "dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/libraries/limit/EpochMap.sol": {"keccak256": "0x6469820c0b831b3837bb187ab5eecc0e441732745ddd6315ef3fcacf73f2c80f", "urls": ["bzz-raw://06a2c94be49eb09e6d219b7b4cfe1a8f60e9978b36c5cc5301dc4dc8f314c1d2", "dweb:/ipfs/QmYnREJYpkay3ey3S1uhwxawYAxgZdyLzRCSPNtrWZzmid"], "license": "BUSL-1.1"}, "contracts/libraries/math/ConstantProduct.sol": {"keccak256": "0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3", "urls": ["bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8", "dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED"], "license": "GPL-3.0-or-later"}, "contracts/libraries/math/OverflowMath.sol": {"keccak256": "0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b", "urls": ["bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29", "dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T"], "license": "MIT"}}, "version": 1}, "id": 42}