{"abi": [{"type": "function", "name": "globalState", "inputs": [], "outputs": [{"name": "pool", "type": "tuple", "internalType": "struct PoolsharkStructs.RangePoolState", "components": [{"name": "samples", "type": "tuple", "internalType": "struct PoolsharkStructs.SampleState", "components": [{"name": "index", "type": "uint16", "internalType": "uint16"}, {"name": "count", "type": "uint16", "internalType": "uint16"}, {"name": "countMax", "type": "uint16", "internalType": "uint16"}]}, {"name": "feeGrowthGlobal0", "type": "uint200", "internalType": "uint200"}, {"name": "feeGrowthGlobal1", "type": "uint200", "internalType": "uint200"}, {"name": "secondsPerLiquidityAccum", "type": "uint160", "internalType": "uint160"}, {"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "tickSecondsAccum", "type": "int56", "internalType": "int56"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}, {"name": "protocolSwapFee0", "type": "uint16", "internalType": "uint16"}, {"name": "protocolSwapFee1", "type": "uint16", "internalType": "uint16"}]}, {"name": "pool0", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitPoolState", "components": [{"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFees", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFillFee", "type": "uint16", "internalType": "uint16"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}]}, {"name": "pool1", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitPoolState", "components": [{"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFees", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFillFee", "type": "uint16", "internalType": "uint16"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}]}, {"name": "liquidityGlobal", "type": "uint128", "internalType": "uint128"}, {"name": "positionIdNext", "type": "uint32", "internalType": "uint32"}, {"name": "epoch", "type": "uint32", "internalType": "uint32"}, {"name": "unlocked", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"globalState()": "e76c01e4"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"globalState\",\"outputs\":[{\"components\":[{\"components\":[{\"internalType\":\"uint16\",\"name\":\"index\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"count\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"countMax\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.SampleState\",\"name\":\"samples\",\"type\":\"tuple\"},{\"internalType\":\"uint200\",\"name\":\"feeGrowthGlobal0\",\"type\":\"uint200\"},{\"internalType\":\"uint200\",\"name\":\"feeGrowthGlobal1\",\"type\":\"uint200\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"int56\",\"name\":\"tickSecondsAccum\",\"type\":\"int56\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"},{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee0\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee1\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.RangePoolState\",\"name\":\"pool\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"protocolFees\",\"type\":\"uint128\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee\",\"type\":\"uint16\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"}],\"internalType\":\"struct PoolsharkStructs.LimitPoolState\",\"name\":\"pool0\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"protocolFees\",\"type\":\"uint128\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee\",\"type\":\"uint16\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"}],\"internalType\":\"struct PoolsharkStructs.LimitPoolState\",\"name\":\"pool1\",\"type\":\"tuple\"},{\"internalType\":\"uint128\",\"name\":\"liquidityGlobal\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"positionIdNext\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"},{\"internalType\":\"uint8\",\"name\":\"unlocked\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/limit/ILimitPoolStorageView.sol\":\"ILimitPoolStorageView\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/limit/ILimitPoolStorageView.sol\":{\"keccak256\":\"0x81173f20ed82249830e6af5b51505e4373c86f2cba9ea7785d4f41cc0b54ee52\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://7905082bb0bf3bc4191e6fccdedd30bf2d662edf446a87a14df5c4cc5422759c\",\"dweb:/ipfs/QmdTppuhdTJjgLL6THPpown2Km6ya5bh6Q1qxAiw9oGo9b\"]},\"contracts/interfaces/structs/LimitPoolStructs.sol\":{\"keccak256\":\"0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef\",\"dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "globalState", "outputs": [{"internalType": "struct PoolsharkStructs.RangePoolState", "name": "pool", "type": "tuple", "components": [{"internalType": "struct PoolsharkStructs.SampleState", "name": "samples", "type": "tuple", "components": [{"internalType": "uint16", "name": "index", "type": "uint16"}, {"internalType": "uint16", "name": "count", "type": "uint16"}, {"internalType": "uint16", "name": "countMax", "type": "uint16"}]}, {"internalType": "uint200", "name": "feeGrowthGlobal0", "type": "uint200"}, {"internalType": "uint200", "name": "feeGrowthGlobal1", "type": "uint200"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160"}, {"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "int56", "name": "tickSecondsAccum", "type": "int56"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}, {"internalType": "uint16", "name": "protocolSwapFee0", "type": "uint16"}, {"internalType": "uint16", "name": "protocolSwapFee1", "type": "uint16"}]}, {"internalType": "struct PoolsharkStructs.LimitPoolState", "name": "pool0", "type": "tuple", "components": [{"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint128", "name": "protocolFees", "type": "uint128"}, {"internalType": "uint16", "name": "protocolFillFee", "type": "uint16"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}]}, {"internalType": "struct PoolsharkStructs.LimitPoolState", "name": "pool1", "type": "tuple", "components": [{"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint128", "name": "protocolFees", "type": "uint128"}, {"internalType": "uint16", "name": "protocolFillFee", "type": "uint16"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}]}, {"internalType": "uint128", "name": "liquidityGlobal", "type": "uint128"}, {"internalType": "uint32", "name": "positionIdNext", "type": "uint32"}, {"internalType": "uint32", "name": "epoch", "type": "uint32"}, {"internalType": "uint8", "name": "unlocked", "type": "uint8"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/limit/ILimitPoolStorageView.sol": "ILimitPoolStorageView"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPoolStorageView.sol": {"keccak256": "0x81173f20ed82249830e6af5b51505e4373c86f2cba9ea7785d4f41cc0b54ee52", "urls": ["bzz-raw://7905082bb0bf3bc4191e6fccdedd30bf2d662edf446a87a14df5c4cc5422759c", "dweb:/ipfs/QmdTppuhdTJjgLL6THPpown2Km6ya5bh6Q1qxAiw9oGo9b"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/LimitPoolStructs.sol": {"keccak256": "0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4", "urls": ["bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef", "dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}}, "version": 1}, "id": 29}