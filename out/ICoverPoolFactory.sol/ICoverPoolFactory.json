{"abi": [{"type": "function", "name": "createCoverPool", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct ICoverPoolFactory.CoverPoolParams", "components": [{"name": "poolType", "type": "bytes32", "internalType": "bytes32"}, {"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "feeTier", "type": "uint16", "internalType": "uint16"}, {"name": "tickSpread", "type": "int16", "internalType": "int16"}, {"name": "twap<PERSON><PERSON>th", "type": "uint16", "internalType": "uint16"}]}], "outputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getCoverPool", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct ICoverPoolFactory.CoverPoolParams", "components": [{"name": "poolType", "type": "bytes32", "internalType": "bytes32"}, {"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "feeTier", "type": "uint16", "internalType": "uint16"}, {"name": "tickSpread", "type": "int16", "internalType": "int16"}, {"name": "twap<PERSON><PERSON>th", "type": "uint16", "internalType": "uint16"}]}], "outputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"createCoverPool((bytes32,address,address,uint16,int16,uint16))": "211ed5b7", "getCoverPool((bytes32,address,address,uint16,int16,uint16))": "41d80eb3"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"poolType\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"tokenIn\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenOut\",\"type\":\"address\"},{\"internalType\":\"uint16\",\"name\":\"feeTier\",\"type\":\"uint16\"},{\"internalType\":\"int16\",\"name\":\"tickSpread\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"twapLength\",\"type\":\"uint16\"}],\"internalType\":\"struct ICoverPoolFactory.CoverPoolParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"createCoverPool\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"poolType\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"tokenIn\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenOut\",\"type\":\"address\"},{\"internalType\":\"uint16\",\"name\":\"feeTier\",\"type\":\"uint16\"},{\"internalType\":\"int16\",\"name\":\"tickSpread\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"twapLength\",\"type\":\"uint16\"}],\"internalType\":\"struct ICoverPoolFactory.CoverPoolParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"getCoverPool\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"createCoverPool((bytes32,address,address,uint16,int16,uint16))\":{\"params\":{\"params\":\"The CoverPoolParams struct referenced above.\"}},\"getCoverPool((bytes32,address,address,uint16,int16,uint16))\":{\"params\":{\"params\":\"The CoverPoolParams struct referenced above.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"createCoverPool((bytes32,address,address,uint16,int16,uint16))\":{\"notice\":\"Creates a new CoverPool.\"},\"getCoverPool((bytes32,address,address,uint16,int16,uint16))\":{\"notice\":\"Fetches an existing CoverPool.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/cover/ICoverPoolFactory.sol\":\"ICoverPoolFactory\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ICoverPoolFactory.sol\":{\"keccak256\":\"0x97f9c15f6fb7ea741deba6a2426f939377ab9eb3884bbf911537ac5f948781fc\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://6e28ea670c6572512f5e552b685f3e9491ccdb3cf56200353f04e11f6df21bd7\",\"dweb:/ipfs/QmPxzvofNNeB6jLJRhcCnXPQiWz6N3xgFakie6yhHaRzVc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct ICoverPoolFactory.CoverPoolParams", "name": "params", "type": "tuple", "components": [{"internalType": "bytes32", "name": "poolType", "type": "bytes32"}, {"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "address", "name": "tokenOut", "type": "address"}, {"internalType": "uint16", "name": "feeTier", "type": "uint16"}, {"internalType": "int16", "name": "tickSpread", "type": "int16"}, {"internalType": "uint16", "name": "twap<PERSON><PERSON>th", "type": "uint16"}]}], "stateMutability": "nonpayable", "type": "function", "name": "createCoverPool", "outputs": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}]}, {"inputs": [{"internalType": "struct ICoverPoolFactory.CoverPoolParams", "name": "params", "type": "tuple", "components": [{"internalType": "bytes32", "name": "poolType", "type": "bytes32"}, {"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "address", "name": "tokenOut", "type": "address"}, {"internalType": "uint16", "name": "feeTier", "type": "uint16"}, {"internalType": "int16", "name": "tickSpread", "type": "int16"}, {"internalType": "uint16", "name": "twap<PERSON><PERSON>th", "type": "uint16"}]}], "stateMutability": "view", "type": "function", "name": "getCoverPool", "outputs": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"createCoverPool((bytes32,address,address,uint16,int16,uint16))": {"params": {"params": "The CoverPoolParams struct referenced above."}}, "getCoverPool((bytes32,address,address,uint16,int16,uint16))": {"params": {"params": "The CoverPoolParams struct referenced above."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"createCoverPool((bytes32,address,address,uint16,int16,uint16))": {"notice": "Creates a new CoverPool."}, "getCoverPool((bytes32,address,address,uint16,int16,uint16))": {"notice": "Fetches an existing CoverPool."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/cover/ICoverPoolFactory.sol": "ICoverPoolFactory"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ICoverPoolFactory.sol": {"keccak256": "0x97f9c15f6fb7ea741deba6a2426f939377ab9eb3884bbf911537ac5f948781fc", "urls": ["bzz-raw://6e28ea670c6572512f5e552b685f3e9491ccdb3cf56200353f04e11f6df21bd7", "dweb:/ipfs/QmPxzvofNNeB6jLJRhcCnXPQiWz6N3xgFakie6yhHaRzVc"], "license": "BUSL-1.1"}}, "version": 1}, "id": 24}