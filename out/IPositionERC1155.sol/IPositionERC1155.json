{"abi": [{"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "balanceOfBatch", "inputs": [{"name": "accounts", "type": "address[]", "internalType": "address[]"}, {"name": "ids", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "batchBalances", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "burn", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "constants", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitImmutables", "components": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "poolImpl", "type": "address", "internalType": "address"}, {"name": "factory", "type": "address", "internalType": "address"}, {"name": "bounds", "type": "tuple", "internalType": "struct PoolsharkStructs.PriceBounds", "components": [{"name": "min", "type": "uint160", "internalType": "uint160"}, {"name": "max", "type": "uint160", "internalType": "uint160"}]}, {"name": "token0", "type": "address", "internalType": "address"}, {"name": "token1", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "genesisTime", "type": "uint32", "internalType": "uint32"}, {"name": "tickSpacing", "type": "int16", "internalType": "int16"}, {"name": "swapFee", "type": "uint16", "internalType": "uint16"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isApprovedForAll", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "constants", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitImmutables", "components": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "poolImpl", "type": "address", "internalType": "address"}, {"name": "factory", "type": "address", "internalType": "address"}, {"name": "bounds", "type": "tuple", "internalType": "struct PoolsharkStructs.PriceBounds", "components": [{"name": "min", "type": "uint160", "internalType": "uint160"}, {"name": "max", "type": "uint160", "internalType": "uint160"}]}, {"name": "token0", "type": "address", "internalType": "address"}, {"name": "token1", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "genesisTime", "type": "uint32", "internalType": "uint32"}, {"name": "tickSpacing", "type": "int16", "internalType": "int16"}, {"name": "swapFee", "type": "uint16", "internalType": "uint16"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "safeBatchTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "amount", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "safeTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256", "internalType": "uint256"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setApprovalForAll", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "approved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [{"name": "id", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "withdrawEth", "inputs": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "constants", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitImmutables", "components": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "poolImpl", "type": "address", "internalType": "address"}, {"name": "factory", "type": "address", "internalType": "address"}, {"name": "bounds", "type": "tuple", "internalType": "struct PoolsharkStructs.PriceBounds", "components": [{"name": "min", "type": "uint160", "internalType": "uint160"}, {"name": "max", "type": "uint160", "internalType": "uint160"}]}, {"name": "token0", "type": "address", "internalType": "address"}, {"name": "token1", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "genesisTime", "type": "uint32", "internalType": "uint32"}, {"name": "tickSpacing", "type": "int16", "internalType": "int16"}, {"name": "swapFee", "type": "uint16", "internalType": "uint16"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "ApprovalForAll", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approve", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "TransferBatch", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "ids", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}, {"name": "amounts", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "TransferSingle", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "id", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"balanceOf(address,uint256)": "00fdd58e", "balanceOfBatch(address[],uint256[])": "4e1273f4", "burn(address,uint256,uint256,(address,address,address,(uint160,uint160),address,address,address,uint32,int16,uint16))": "cad37bfe", "isApprovedForAll(address,address)": "e985e9c5", "mint(address,uint256,uint256,(address,address,address,(uint160,uint160),address,address,address,uint32,int16,uint16))": "05cdce22", "name()": "06fdde03", "safeBatchTransferFrom(address,address,uint256[],uint256[])": "fba0ee64", "safeTransferFrom(address,address,uint256,uint256)": "0febdd49", "setApprovalForAll(address,bool)": "a22cb465", "supportsInterface(bytes4)": "01ffc9a7", "symbol()": "95d89b41", "totalSupply(uint256)": "bd85b039", "withdrawEth(address,(address,address,address,(uint160,uint160),address,address,address,uint32,int16,uint16))": "14a305d9"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"approve\",\"type\":\"bool\"}],\"name\":\"ApprovalForAll\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"ids\",\"type\":\"uint256[]\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"}],\"name\":\"TransferBatch\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"TransferSingle\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"accounts\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"ids\",\"type\":\"uint256[]\"}],\"name\":\"balanceOfBatch\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"batchBalances\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"factory\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"min\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"max\",\"type\":\"uint160\"}],\"internalType\":\"struct PoolsharkStructs.PriceBounds\",\"name\":\"bounds\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"genesisTime\",\"type\":\"uint32\"},{\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.LimitImmutables\",\"name\":\"constants\",\"type\":\"tuple\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"isApprovedForAll\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"factory\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"min\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"max\",\"type\":\"uint160\"}],\"internalType\":\"struct PoolsharkStructs.PriceBounds\",\"name\":\"bounds\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"genesisTime\",\"type\":\"uint32\"},{\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.LimitImmutables\",\"name\":\"constants\",\"type\":\"tuple\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256[]\",\"name\":\"id\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"amount\",\"type\":\"uint256[]\"}],\"name\":\"safeBatchTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setApprovalForAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"factory\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"min\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"max\",\"type\":\"uint160\"}],\"internalType\":\"struct PoolsharkStructs.PriceBounds\",\"name\":\"bounds\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"genesisTime\",\"type\":\"uint32\"},{\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.LimitImmutables\",\"name\":\"constants\",\"type\":\"tuple\"}],\"name\":\"withdrawEth\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"supportsInterface(bytes4)\":{\"details\":\"Returns true if this contract implements the interface defined by `interfaceId`. See the corresponding https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section] to learn more about how these ids are created. This function call must use less than 30 000 gas.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/IPositionERC1155.sol\":\"IPositionERC1155\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/IPositionERC1155.sol\":{\"keccak256\":\"0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11\",\"dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe\"]},\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f\",\"dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "bool", "name": "approve", "type": "bool", "indexed": false}], "type": "event", "name": "ApprovalForAll", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]", "indexed": false}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]", "indexed": false}], "type": "event", "name": "TransferBatch", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "id", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "TransferSingle", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}], "stateMutability": "view", "type": "function", "name": "balanceOfBatch", "outputs": [{"internalType": "uint256[]", "name": "batchBalances", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "struct PoolsharkStructs.LimitImmutables", "name": "constants", "type": "tuple", "components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "poolImpl", "type": "address"}, {"internalType": "address", "name": "factory", "type": "address"}, {"internalType": "struct PoolsharkStructs.PriceBounds", "name": "bounds", "type": "tuple", "components": [{"internalType": "uint160", "name": "min", "type": "uint160"}, {"internalType": "uint160", "name": "max", "type": "uint160"}]}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "uint32", "name": "genesisTime", "type": "uint32"}, {"internalType": "int16", "name": "tickSpacing", "type": "int16"}, {"internalType": "uint16", "name": "swapFee", "type": "uint16"}]}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "struct PoolsharkStructs.LimitImmutables", "name": "constants", "type": "tuple", "components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "poolImpl", "type": "address"}, {"internalType": "address", "name": "factory", "type": "address"}, {"internalType": "struct PoolsharkStructs.PriceBounds", "name": "bounds", "type": "tuple", "components": [{"internalType": "uint160", "name": "min", "type": "uint160"}, {"internalType": "uint160", "name": "max", "type": "uint160"}]}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "uint32", "name": "genesisTime", "type": "uint32"}, {"internalType": "int16", "name": "tickSpacing", "type": "int16"}, {"internalType": "uint16", "name": "swapFee", "type": "uint16"}]}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256[]", "name": "id", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "amount", "type": "uint256[]"}], "stateMutability": "nonpayable", "type": "function", "name": "safeBatchTransferFrom"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "safeTransferFrom"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setApprovalForAll"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "struct PoolsharkStructs.LimitImmutables", "name": "constants", "type": "tuple", "components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "poolImpl", "type": "address"}, {"internalType": "address", "name": "factory", "type": "address"}, {"internalType": "struct PoolsharkStructs.PriceBounds", "name": "bounds", "type": "tuple", "components": [{"internalType": "uint160", "name": "min", "type": "uint160"}, {"internalType": "uint160", "name": "max", "type": "uint160"}]}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "uint32", "name": "genesisTime", "type": "uint32"}, {"internalType": "int16", "name": "tickSpacing", "type": "int16"}, {"internalType": "uint16", "name": "swapFee", "type": "uint16"}]}], "stateMutability": "nonpayable", "type": "function", "name": "withdrawEth"}], "devdoc": {"kind": "dev", "methods": {"supportsInterface(bytes4)": {"details": "Returns true if this contract implements the interface defined by `interfaceId`. See the corresponding https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section] to learn more about how these ids are created. This function call must use less than 30 000 gas."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/IPositionERC1155.sol": "IPositionERC1155"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/IPositionERC1155.sol": {"keccak256": "0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c", "urls": ["bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11", "dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe"], "license": "MIT"}, "contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1", "urls": ["bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f", "dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy"], "license": "MIT"}}, "version": 1}, "id": 19}