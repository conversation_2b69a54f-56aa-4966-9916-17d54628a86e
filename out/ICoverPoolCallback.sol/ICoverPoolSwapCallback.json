{"abi": [{"type": "function", "name": "coverPoolSwapCallback", "inputs": [{"name": "amount0Delta", "type": "int256", "internalType": "int256"}, {"name": "amount1Delta", "type": "int256", "internalType": "int256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"coverPoolSwapCallback(int256,int256,bytes)": "5cf4dc45"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"amount0Delta\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"amount1Delta\",\"type\":\"int256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"coverPoolSwapCallback\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"coverPoolSwapCallback(int256,int256,bytes)\":{\"details\":\"In the implementation you must pay the pool tokens owed for the swap. amount0Delta and amount1Delta can both be 0 if no tokens were swapped.\",\"params\":{\"amount0Delta\":\"The amount of token0 either received by (positive) or sent from (negative) the user.\",\"amount1Delta\":\"The amount of token1 either received by (positive) or sent from (negative) the user.\"}}},\"title\":\"Callback for swaps\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"coverPoolSwapCallback(int256,int256,bytes)\":{\"notice\":\"Called to `msg.sender` after executing a swap.\"}},\"notice\":\"Any contract that calls the `swap` function must implement this interface.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/callbacks/ICoverPoolCallback.sol\":\"ICoverPoolSwapCallback\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/callbacks/ICoverPoolCallback.sol\":{\"keccak256\":\"0xa363a0569c45bf56b810e334d56608739efa074c7ad7816c53fe8dcbd105cf2e\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://31561cccd6992eba142c6283d3b87b2322093c0460200331d0724dd857a24f2f\",\"dweb:/ipfs/QmdmAT8sRi9ZNLGJXRz8xmoMUQ4c8KgFfN5BWQjrpZNBo7\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "int256", "name": "amount0Delta", "type": "int256"}, {"internalType": "int256", "name": "amount1Delta", "type": "int256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "coverPoolSwapCallback"}], "devdoc": {"kind": "dev", "methods": {"coverPoolSwapCallback(int256,int256,bytes)": {"details": "In the implementation you must pay the pool tokens owed for the swap. amount0Delta and amount1Delta can both be 0 if no tokens were swapped.", "params": {"amount0Delta": "The amount of token0 either received by (positive) or sent from (negative) the user.", "amount1Delta": "The amount of token1 either received by (positive) or sent from (negative) the user."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"coverPoolSwapCallback(int256,int256,bytes)": {"notice": "Called to `msg.sender` after executing a swap."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/callbacks/ICoverPoolCallback.sol": "ICoverPoolSwapCallback"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/callbacks/ICoverPoolCallback.sol": {"keccak256": "0xa363a0569c45bf56b810e334d56608739efa074c7ad7816c53fe8dcbd105cf2e", "urls": ["bzz-raw://31561cccd6992eba142c6283d3b87b2322093c0460200331d0724dd857a24f2f", "dweb:/ipfs/QmdmAT8sRi9ZNLGJXRz8xmoMUQ4c8KgFfN5BWQjrpZNBo7"], "license": "GPL-3.0-or-later"}}, "version": 1}, "id": 21}