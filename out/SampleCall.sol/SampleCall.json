{"abi": [{"type": "function", "name": "perform", "inputs": [{"name": "state", "type": "tuple", "internalType": "struct PoolsharkStructs.GlobalState", "components": [{"name": "pool", "type": "tuple", "internalType": "struct PoolsharkStructs.RangePoolState", "components": [{"name": "samples", "type": "tuple", "internalType": "struct PoolsharkStructs.SampleState", "components": [{"name": "index", "type": "uint16", "internalType": "uint16"}, {"name": "count", "type": "uint16", "internalType": "uint16"}, {"name": "countMax", "type": "uint16", "internalType": "uint16"}]}, {"name": "feeGrowthGlobal0", "type": "uint200", "internalType": "uint200"}, {"name": "feeGrowthGlobal1", "type": "uint200", "internalType": "uint200"}, {"name": "secondsPerLiquidityAccum", "type": "uint160", "internalType": "uint160"}, {"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "tickSecondsAccum", "type": "int56", "internalType": "int56"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}, {"name": "protocolSwapFee0", "type": "uint16", "internalType": "uint16"}, {"name": "protocolSwapFee1", "type": "uint16", "internalType": "uint16"}]}, {"name": "pool0", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitPoolState", "components": [{"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFees", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFillFee", "type": "uint16", "internalType": "uint16"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}]}, {"name": "pool1", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitPoolState", "components": [{"name": "price", "type": "uint160", "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFees", "type": "uint128", "internalType": "uint128"}, {"name": "protocolFillFee", "type": "uint16", "internalType": "uint16"}, {"name": "tickAtPrice", "type": "int24", "internalType": "int24"}]}, {"name": "liquidityGlobal", "type": "uint128", "internalType": "uint128"}, {"name": "positionIdNext", "type": "uint32", "internalType": "uint32"}, {"name": "epoch", "type": "uint32", "internalType": "uint32"}, {"name": "unlocked", "type": "uint8", "internalType": "uint8"}]}, {"name": "constants", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitImmutables", "components": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "poolImpl", "type": "address", "internalType": "address"}, {"name": "factory", "type": "address", "internalType": "address"}, {"name": "bounds", "type": "tuple", "internalType": "struct PoolsharkStructs.PriceBounds", "components": [{"name": "min", "type": "uint160", "internalType": "uint160"}, {"name": "max", "type": "uint160", "internalType": "uint160"}]}, {"name": "token0", "type": "address", "internalType": "address"}, {"name": "token1", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "genesisTime", "type": "uint32", "internalType": "uint32"}, {"name": "tickSpacing", "type": "int16", "internalType": "int16"}, {"name": "swapFee", "type": "uint16", "internalType": "uint16"}]}, {"name": "secondsAgo", "type": "uint32[]", "internalType": "uint32[]"}], "outputs": [{"name": "tickSecondsAccum", "type": "int56[]", "internalType": "int56[]"}, {"name": "secondsPerLiquidityAccum", "type": "uint160[]", "internalType": "uint160[]"}, {"name": "averagePrice", "type": "uint160", "internalType": "uint160"}, {"name": "averageLiquidity", "type": "uint128", "internalType": "uint128"}, {"name": "averageTick", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "event", "name": "SampleLengthIncreased", "inputs": [{"name": "sampleLengthNext", "type": "uint16", "indexed": false, "internalType": "uint16"}], "anonymous": false}, {"type": "event", "name": "SampleRecorded", "inputs": [{"name": "tickSecondsAccum", "type": "int56", "indexed": false, "internalType": "int56"}, {"name": "secondsPerLiquidityAccum", "type": "uint160", "indexed": false, "internalType": "uint160"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "139:1133:52:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;139:1133:52;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "139:1133:52:-:0;;;;;;;;;;;;;;;;;;;;;;;;390:880;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;;:::i;:::-;;;;;;;;;591:33;634:41;685:20;715:24;749:17;198:1;787:26;;:5;:14;;;:26;;;783:100;;827:56;;-1:-1:-1;;;827:56:52;;10556:2:78;827:56:52;;;10538:21:78;10595:2;10575:18;;;10568:30;10634:34;10614:18;;;10607:62;-1:-1:-1;;;10685:18:78;;;10678:36;10731:19;;827:56:52;;;;;;;;;900:363;933:4;952:301;;;;;;;;999:5;:10;;;:18;;;:24;;;952:301;;;;;;1041:5;:10;;;:18;;;:24;;;952:301;;;;;;1090:15;952:301;;;;;;1124:10;952:301;;;;1152:5;:10;;;:22;;;952:301;;;;;;1192:5;:10;;;:20;;;-1:-1:-1;;;;;952:301:52;;;;;1230:9;952:301;;;900:11;:363::i;:::-;893:370;;;;-1:-1:-1;893:370:52;;-1:-1:-1;893:370:52;-1:-1:-1;893:370:52;;-1:-1:-1;390:880:52;-1:-1:-1;;;;390:880:52:o;2833:2478:38:-;2959:33;3002:41;3053:20;3083:24;3117:17;3155:6;:19;;;:24;;3178:1;3155:24;3151:69;;3181:39;;-1:-1:-1;;;3181:39:38;;10963:2:78;3181:39:38;;;10945:21:78;11002:2;10982:18;;;10975:30;-1:-1:-1;;;11021:18:78;;;11014:51;11082:18;;3181:39:38;10761:345:78;3181:39:38;3234:6;:17;;;:24;3262:1;3234:29;3230:75;;3265:40;;-1:-1:-1;;;3265:40:38;;11313:2:78;3265:40:38;;;11295:21:78;11352:2;11332:18;;;11325:30;-1:-1:-1;;;11371:18:78;;;11364:52;11433:18;;3265:40:38;11111:346:78;3265:40:38;3315:12;3357:1;3330:6;:17;;;:24;:28;:59;;3388:1;3330:59;;;3361:6;:17;;;:24;3330:59;3315:74;;3399:26;3441:4;3428:18;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;3428:18:38;;3399:47;;3460:6;:17;;;:24;3488:1;3460:29;3456:236;;3518:15;;;3531:1;3518:15;;;;;;;;;;;;;;;;;;;;-1:-1:-1;3518:15:38;3505:28;;3563:6;:17;;;3581:1;3563:20;;;;;;;;:::i;:::-;;;;;;;3547:10;3558:1;3547:13;;;;;;;;:::i;:::-;;;;;;:36;;;;;;;;;;;3613:6;:17;;;3631:1;3613:20;;;;;;;;:::i;:::-;;;;;;;3636:1;3613:24;;;;:::i;:::-;3597:10;3608:1;3597:13;;;;;;;;:::i;:::-;;;;;;:40;;;;;;;;;;;3456:236;;;-1:-1:-1;3675:17:38;;;;3456:236;3724:10;3755:1;3735:10;:17;:21;;;;:::i;:::-;3724:33;;;;;;;;:::i;:::-;;;;;;;3707:50;;:10;3718:1;3707:13;;;;;;;;:::i;:::-;;;;;;;:50;;;3703:102;;3759:46;;-1:-1:-1;;;3759:46:38;;12291:2:78;3759:46:38;;;12273:21:78;12330:2;12310:18;;;12303:30;12369;12349:18;;;12342:58;12417:18;;3759:46:38;12089:352:78;3759:46:38;3847:10;:17;3835:30;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;3835:30:38;;3816:49;;3916:10;:17;3902:32;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;3902:32:38;;3875:59;;3950:9;3945:284;3969:10;:17;3965:1;:21;3945:284;;;4106:112;4144:4;4167:6;4191:10;4202:1;4191:13;;;;;;;;:::i;:::-;;;;;;;4106:9;:112::i;:::-;4025:16;4042:1;4025:19;;;;;;;;:::i;:::-;;;;;;4062:24;4087:1;4062:27;;;;;;;;:::i;:::-;;;;;;4007:211;-1:-1:-1;;;;;4007:211:38;-1:-1:-1;;;;;4007:211:38;;;;;;;;;;;;;;3988:3;;;;;:::i;:::-;;;;3945:284;;;;4278:10;4289:1;4278:13;;;;;;;;:::i;:::-;;;;;;;4242:49;;:10;4273:1;4253:10;:17;:21;;;;:::i;:::-;4242:33;;;;;;;;:::i;:::-;;;;;;;:49;;;4238:1067;;;4468:10;4479:1;4468:13;;;;;;;;:::i;:::-;;;;;;;4432:10;4463:1;4443:10;:17;:21;;;;:::i;:::-;4432:33;;;;;;;;:::i;:::-;;;;;;;:49;;;;:::i;:::-;4327:155;;4350:16;4387:1;4367:10;:17;:21;;;;:::i;:::-;4350:39;;;;;;;;:::i;:::-;;;;;;;4328:16;4345:1;4328:19;;;;;;;;:::i;:::-;;;;;;;:61;;;;:::i;:::-;4327:155;;;;:::i;:::-;4307:176;;4512:61;4543:11;4556:6;:16;;;4512:30;:61::i;:::-;4497:76;;4770:10;4781:1;4770:13;;;;;;;;:::i;:::-;;;;;;;4734:10;4765:1;4745:10;:17;:21;;;;:::i;:::-;4734:33;;;;;;;;:::i;:::-;;;;;;;:49;;;;:::i;:::-;4614:170;;4645:24;4690:1;4670:10;:17;:21;;;;:::i;:::-;4645:47;;;;;;;;:::i;:::-;;;;;;;4615:24;4640:1;4615:27;;;;;;;;:::i;:::-;;;;;;;:77;;;;:::i;:::-;4614:170;;;;:::i;:::-;4587:198;;4238:1067;;;4957:10;4988:1;4968:10;:17;:21;;;;:::i;:::-;4957:33;;;;;;;;:::i;:::-;;;;;;;4941:10;4952:1;4941:13;;;;;;;;:::i;:::-;;;;;;;:49;;;;:::i;:::-;4836:155;;4879:16;4896:1;4879:19;;;;;;;;:::i;:::-;;;;;;;4837:16;4874:1;4854:10;:17;:21;;;;:::i;:::-;4837:39;;;;;;;;:::i;:::-;;;;;;;:61;;;;:::i;:::-;4836:155;;;;:::i;:::-;4816:176;;5021:61;5052:11;5065:6;:16;;;5021:30;:61::i;:::-;5006:76;;5259:10;5290:1;5270:10;:17;:21;;;;:::i;:::-;5259:33;;;;;;;;:::i;:::-;;;;;;;5243:10;5254:1;5243:13;;;;;;;;:::i;:::-;;;;;;;:49;;;;:::i;:::-;5123:170;;5174:24;5199:1;5174:27;;;;;;;;:::i;:::-;;;;;;;5124:24;5169:1;5149:10;:17;:21;;;;:::i;:::-;5124:47;;;;;;;;:::i;:::-;;;;;;;:77;;;;:::i;:::-;5123:170;;;;:::i;:::-;5096:198;;4238:1067;3141:2170;;2833:2478;;;;;;;;:::o;5804:2525::-;5966:24;6000:32;6049:37;6089;6101:4;6107:6;:18;;;6089:37;;:11;:37::i;:::-;6049:77;;6141:10;:15;;6155:1;6141:15;6137:497;;6234:21;;:52;6270:15;6234:52;;:25;;6258:1;6234:25;:::i;:::-;:52;;;6230:268;;6315:168;6343:6;6378:15;6416:6;:11;;;6449:6;:16;;;6315:6;:168::i;:::-;6306:177;;6230:268;6537:6;:23;;;6578:6;:31;;;6512:111;;;;;;;6137:497;6644:17;6664:36;6690:10;6671:15;6664:36;:::i;:::-;6644:56;;6762:42;6818:43;6874:127;6911:4;6933:6;6957;6981:10;6874:19;:127::i;:::-;6748:253;;;;7030:11;:26;;;7016:40;;:10;:40;;;7012:1311;;7125:11;:28;;;7171:11;:36;;;7100:121;;;;;;;;;;7012:1311;7256:12;:27;;;7242:41;;:10;:41;;;7238:1085;;7353:12;:29;;;7400:12;:37;;;7328:123;;;;;;;;;;7238:1085;7577:26;;7547:27;;7517:21;;7547:56;;;:::i;:::-;7663:26;;7517:87;;-1:-1:-1;7618:17:38;;7650:39;;:10;:39;:::i;:::-;7638:53;;7618:73;;7907:11;7868:15;7782:101;;7815:11;:28;;;7783:12;:29;;;:60;;;;:::i;:::-;7782:101;;;;:::i;:::-;7781:137;;;;:::i;:::-;7730:11;:28;;;:188;;;;:::i;:::-;8260:15;8028:248;;8211:11;8196:28;;8106:11;:36;;;8066:12;:37;;;:76;;;;:::i;:::-;-1:-1:-1;;;;;8029:139:38;:195;;;;:::i;:::-;8028:248;;;;:::i;:::-;7936:11;:36;;;:362;;;;:::i;:::-;7705:607;;;;;;;;;;5804:2525;;;;;;;:::o;7865:2864:48:-;8006:13;8036:15;8061:1;8054:4;:8;;;:57;;8105:4;8098:12;;8054:57;;;8081:4;8074:12;;8073:13;;;:::i;:::-;8036:75;;8150:30;8158:9;:21;;;8150:7;:30::i;:::-;8135:47;;8125:7;:57;8121:99;;;8184:36;;-1:-1:-1;;;8184:36:48;;16020:2:78;8184:36:48;;;16002:21:78;16059:2;16039:18;;;16032:30;-1:-1:-1;;;16078:18:78;;;16071:47;16135:18;;8184:36:48;15818:341:78;8184:36:48;8254:13;8270:7;8280:3;8270:13;8287:1;8270:18;:125;;-1:-1:-1;;;8270:125:48;;;8307:34;8270:125;8254:141;;;-1:-1:-1;8423:3:48;8413:13;;:18;8409:83;;8450:34;8442:42;8489:3;8441:51;8409:83;8520:3;8510:13;;:18;8506:83;;8547:34;8539:42;8586:3;8538:51;8506:83;8617:3;8607:13;;:18;8603:83;;8644:34;8636:42;8683:3;8635:51;8603:83;8714:4;8704:14;;:19;8700:84;;8742:34;8734:42;8781:3;8733:51;8700:84;8812:4;8802:14;;:19;8798:84;;8840:34;8832:42;8879:3;8831:51;8798:84;8910:4;8900:14;;:19;8896:84;;8938:34;8930:42;8977:3;8929:51;8896:84;9008:4;8998:14;;:19;8994:84;;9036:34;9028:42;9075:3;9027:51;8994:84;9106:5;9096:15;;:20;9092:85;;9135:34;9127:42;9174:3;9126:51;9092:85;9205:5;9195:15;;:20;9191:85;;9234:34;9226:42;9273:3;9225:51;9191:85;9304:5;9294:15;;:20;9290:85;;9333:34;9325:42;9372:3;9324:51;9290:85;9403:5;9393:15;;:20;9389:85;;9432:34;9424:42;9471:3;9423:51;9389:85;9502:6;9492:16;;:21;9488:86;;9532:34;9524:42;9571:3;9523:51;9488:86;9602:6;9592:16;;:21;9588:86;;9632:34;9624:42;9671:3;9623:51;9588:86;9702:6;9692:16;;:21;9688:86;;9732:34;9724:42;9771:3;9723:51;9688:86;9802:6;9792:16;;:21;9788:86;;9832:34;9824:42;9871:3;9823:51;9788:86;9902:7;9892:17;;:22;9888:86;;9933:33;9925:41;9971:3;9924:50;9888:86;10002:7;9992:17;;:22;9988:85;;10033:32;10025:40;10070:3;10024:49;9988:85;10101:7;10091:17;;:22;10087:83;;10132:30;10124:38;10167:3;10123:47;10087:83;10198:7;10188:17;;:22;10184:78;;10229:25;10221:33;10259:3;10220:42;10184:78;10288:1;10281:4;:8;;;10277:47;;;10319:5;-1:-1:-1;;10299:25:48;;;;;:::i;:::-;;10291:33;;10277:47;10689:7;10680:5;:17;:22;:30;;10709:1;10680:30;;;10705:1;10680:30;10663:48;;10673:2;10664:5;:11;;10663:48;10647:65;;8230:2493;8026:2703;7865:2864;;;;:::o;5317:481:38:-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;5491:21:38;5526:22;5562:32;5618:4;-1:-1:-1;;;;;5607:24:38;;5632:11;5607:37;;;;;;;;;;;;;16310:25:78;;16298:2;16283:18;;16164:177;5607:37:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5662:129;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;5662:129:38;;;;;;-1:-1:-1;;;;5317:481:38;;;;;:::o;11565:698::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;11846:24:38;;11798:15;;11829:41;;:14;:41;:::i;:::-;11822:49;;11798:74;;11902:354;;;;;;;;11960:14;11902:354;;;;;;12059:9;12039:30;;12045:4;12039:11;;:30;;;;:::i;:::-;12010:9;:26;;;:59;;;;:::i;:::-;11902:354;;;;;;12222:1;12210:9;-1:-1:-1;;;;;12210:13:38;;:29;;12238:1;12210:29;;;12226:9;12210:29;12171:69;;-1:-1:-1;;;;;12171:69:38;-1:-1:-1;;;12202:3:38;12172:33;;;;12171:69;:::i;:::-;12113:9;:34;;;:128;;;;:::i;:::-;-1:-1:-1;;;;;11902:354:38;;;11883:373;11565:698;-1:-1:-1;;;;;;11565:698:38:o;13654:1090::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;13973:26:38;;13968:44;;14001:10;13968:4;:44::i;:::-;13964:328;;;14032:26;;:40;;;;;;;14028:254;;-1:-1:-1;;14113:30:38;;;;;;;;-1:-1:-1;14113:30:38;;;;;;;;;;;;;;;;14100:11;;14092:52;;14028:254;14191:11;14204:62;14211:11;14224:10;14236:6;:11;;;14249:6;:16;;;14204:6;:62::i;:::-;14183:84;;;;;;14028:254;14360:19;;;;14334:18;;14315:65;;14327:4;;14334:22;;14355:1;14334:22;:::i;:::-;14333:46;;;;:::i;:::-;14315:65;;:11;:65::i;:::-;14394:26;;14301:79;;-1:-1:-1;14394:31:38;;:26;:31;14390:96;;14455:20;14467:4;14473:1;14455:11;:20::i;:::-;14441:34;;14390:96;14504:26;;14499:44;;14532:10;14499:4;:44::i;:::-;14495:94;;14545:44;;-1:-1:-1;;;14545:44:38;;17685:2:78;14545:44:38;;;17667:21:78;17724:2;17704:18;;;17697:30;17763:28;17743:18;;;17736:56;17809:18;;14545:44:38;17483:350:78;14545:44:38;14607:130;14634:4;14652:10;14676:6;:18;;;14708:6;:19;;;14607:13;:130::i;:::-;14600:137;;;;13654:1090;;;;;;;;:::o;5886:159:48:-;5968:10;6002:36;;;;;5670:9;-1:-1:-1;;5670:9:48;:::i;:::-;6002:22;;;;:::i;:::-;:36;;;;:::i;11019:540:38:-;11105:4;11149:15;11179:20;;;;;;;;;;;:44;;;11212:11;11203:20;;:5;:20;;;;11179:44;11175:71;;;11241:5;11232:14;;:5;:14;;;;11225:21;;;;;11175:71;11257:29;;;;;11296;;;;11340:20;;;;11336:80;;11392:13;;;;11400:5;11392:13;:::i;:::-;11376:29;;;;11336:80;11438:11;11429:20;;:5;:20;;;11425:80;;11481:13;;;;11489:5;11481:13;:::i;:::-;11465:29;;;;11425:80;-1:-1:-1;11522:30:38;;11019:540;-1:-1:-1;;;;11019:540:38:o;12269:1379::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;12589:12:38;12570:15;:11;12584:1;12570:15;:::i;:::-;12569:32;;;;:::i;:::-;12550:51;;;;12611:16;12656:1;12641:12;12630:23;;:8;:23;;;;:::i;:::-;:27;;;;:::i;:::-;12611:46;;12680:13;12703:939;12797:1;12774:19;12785:8;12774;:19;:::i;:::-;12773:25;;;;:::i;:::-;12765:33;-1:-1:-1;12863:39:38;12875:4;12881:20;;;;12765:33;12881:20;:::i;:::-;12863:11;:39::i;:::-;12963:26;;12849:53;;-1:-1:-1;12963:31:38;;:26;:31;12959:164;;13073:9;:5;13081:1;13073:9;:::i;:::-;13062:20;;13100:8;;12959:164;13190:45;13202:4;13208:26;;;13209:9;:5;13217:1;13209:9;:::i;:::-;13208:26;;;;:::i;13190:45::-;13175:60;;13317:21;13343:44;13348:11;:26;;;13376:10;13343:4;:44::i;:::-;13317:70;;13401:23;13427:45;13432:10;13444:12;:27;;;13427:4;:45::i;:::-;13401:71;;13490:16;:38;;;;;13510:18;13490:38;13486:49;;;13530:5;;;;13486:49;13554:16;13549:82;;13583:9;13591:1;13583:5;:9;:::i;:::-;13572:20;;13549:82;;;13622:9;:5;13630:1;13622:9;:::i;:::-;13611:20;;13549:82;12716:926;;12703:939;;;12540:1108;;;12269:1379;;;;;;;:::o;14:127:78:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:255;218:2;212:9;260:6;248:19;;297:18;282:34;;318:22;;;279:62;276:88;;;344:18;;:::i;:::-;380:2;373:22;146:255;:::o;406:248::-;473:2;467:9;515:4;503:17;;550:18;535:34;;571:22;;;532:62;529:88;;;597:18;;:::i;659:159::-;726:20;;786:6;775:18;;765:29;;755:57;;808:1;805;798:12;755:57;659:159;;;:::o;823:557::-;881:5;929:4;917:9;912:3;908:19;904:30;901:50;;;947:1;944;937:12;901:50;980:2;974:9;1022:4;1014:6;1010:17;1093:6;1081:10;1078:22;1057:18;1045:10;1042:34;1039:62;1036:88;;;1104:18;;:::i;:::-;1140:2;1133:22;1173:6;-1:-1:-1;1173:6:78;1203:28;1221:9;1203:28;:::i;:::-;1195:6;1188:44;1265:37;1298:2;1287:9;1283:18;1265:37;:::i;:::-;1260:2;1252:6;1248:15;1241:62;1336:37;1369:2;1358:9;1354:18;1336:37;:::i;:::-;1331:2;1323:6;1319:15;1312:62;;823:557;;;;:::o;1385:173::-;1453:20;;-1:-1:-1;;;;;1502:31:78;;1492:42;;1482:70;;1548:1;1545;1538:12;1563:131;-1:-1:-1;;;;;1638:31:78;;1628:42;;1618:70;;1684:1;1681;1674:12;1618:70;1563:131;:::o;1699:134::-;1767:20;;1796:31;1767:20;1796:31;:::i;1838:188::-;1906:20;;-1:-1:-1;;;;;1955:46:78;;1945:57;;1935:85;;2016:1;2013;2006:12;2031:118;2118:5;2115:1;2104:20;2097:5;2094:31;2084:59;;2139:1;2136;2129:12;2154:130;2220:20;;2249:29;2220:20;2249:29;:::i;2289:160::-;2355:20;;2415:1;2404:20;;;2394:31;;2384:59;;2439:1;2436;2429:12;2454:942;2515:5;2563:6;2551:9;2546:3;2542:19;2538:32;2535:52;;;2583:1;2580;2573:12;2535:52;2605:22;;:::i;:::-;2596:31;;2650:45;2691:3;2680:9;2650:45;:::i;:::-;2643:5;2636:60;2730:38;2764:2;2753:9;2749:18;2730:38;:::i;:::-;2723:4;2716:5;2712:16;2705:64;2803:39;2837:3;2826:9;2822:19;2803:39;:::i;:::-;2796:4;2789:5;2785:16;2778:65;2875:39;2909:3;2898:9;2894:19;2875:39;:::i;:::-;2870:2;2863:5;2859:14;2852:63;2948:39;2982:3;2971:9;2967:19;2948:39;:::i;:::-;2942:3;2935:5;2931:15;2924:64;3021:39;3055:3;3044:9;3040:19;3021:39;:::i;:::-;3015:3;3008:5;3004:15;2997:64;3080:3;3116:36;3148:2;3137:9;3133:18;3116:36;:::i;:::-;3110:3;3103:5;3099:15;3092:61;3172:3;3208:36;3240:2;3229:9;3225:18;3208:36;:::i;:::-;3202:3;3195:5;3191:15;3184:61;3277:41;3310:6;3299:9;3295:22;3277:41;:::i;:::-;3272:2;3265:5;3261:14;3254:65;3351:38;3384:3;3373:9;3369:19;3351:38;:::i;:::-;3335:14;;;3328:62;-1:-1:-1;3339:5:78;2454:942;-1:-1:-1;;2454:942:78:o;3401:773::-;3462:5;3510:4;3498:9;3493:3;3489:19;3485:30;3482:50;;;3528:1;3525;3518:12;3482:50;3561:2;3555:9;3603:4;3595:6;3591:17;3674:6;3662:10;3659:22;3638:18;3626:10;3623:34;3620:62;3617:88;;;3685:18;;:::i;:::-;3721:2;3714:22;3754:6;-1:-1:-1;3754:6:78;3784:23;;3816:33;3784:23;3816:33;:::i;:::-;3858:23;;3914:38;3948:2;3933:18;;3914:38;:::i;:::-;3909:2;3901:6;3897:15;3890:63;3986:38;4020:2;4009:9;4005:18;3986:38;:::i;:::-;3981:2;3973:6;3969:15;3962:63;4058:37;4091:2;4080:9;4076:18;4058:37;:::i;:::-;4053:2;4045:6;4041:15;4034:62;4130:37;4162:3;4151:9;4147:19;4130:37;:::i;:::-;4124:3;4116:6;4112:16;4105:63;;3401:773;;;;:::o;4179:121::-;4264:10;4257:5;4253:22;4246:5;4243:33;4233:61;;4290:1;4287;4280:12;4305:132;4372:20;;4401:30;4372:20;4401:30;:::i;4442:156::-;4508:20;;4568:4;4557:16;;4547:27;;4537:55;;4588:1;4585;4578:12;4603:626;4661:5;4709:4;4697:9;4692:3;4688:19;4684:30;4681:50;;;4727:1;4724;4717:12;4681:50;4760:4;4754:11;4804:4;4796:6;4792:17;4875:6;4863:10;4860:22;4839:18;4827:10;4824:34;4821:62;4818:88;;;4886:18;;:::i;:::-;4922:4;4915:24;4957:6;-1:-1:-1;4957:6:78;4987:23;;5019:33;4987:23;5019:33;:::i;:::-;5061:23;;5136:2;5121:18;;5108:32;5149:33;5108:32;5149:33;:::i;:::-;5210:2;5198:15;;;;5191:32;4603:626;;-1:-1:-1;;4603:626:78:o;5234:160::-;5300:20;;5360:1;5349:20;;;5339:31;;5329:59;;5384:1;5381;5374:12;5399:940;5461:5;5509:6;5497:9;5492:3;5488:19;5484:32;5481:52;;;5529:1;5526;5519:12;5481:52;5551:22;;:::i;:::-;5542:31;;5596:29;5615:9;5596:29;:::i;:::-;5589:5;5582:44;5658:38;5692:2;5681:9;5677:18;5658:38;:::i;:::-;5653:2;5646:5;5642:14;5635:62;5729:38;5763:2;5752:9;5748:18;5729:38;:::i;:::-;5724:2;5717:5;5713:14;5706:62;5800:54;5850:3;5845:2;5834:9;5830:18;5800:54;:::i;:::-;5795:2;5788:5;5784:14;5777:78;5889:39;5923:3;5912:9;5908:19;5889:39;:::i;:::-;5882:4;5875:5;5871:16;5864:65;5962:39;5996:3;5985:9;5981:19;5962:39;:::i;:::-;5956:3;5949:5;5945:15;5938:64;6035:39;6069:3;6058:9;6054:19;6035:39;:::i;:::-;6029:3;6022:5;6018:15;6011:64;6094:3;6130:37;6163:2;6152:9;6148:18;6130:37;:::i;:::-;6124:3;6117:5;6113:15;6106:62;6187:3;6222:36;6254:2;6243:9;6239:18;6222:36;:::i;:::-;6217:2;6210:5;6206:14;6199:60;6291:41;6324:6;6313:9;6309:22;6291:41;:::i;6344:975::-;6397:5;6450:3;6443:4;6435:6;6431:17;6427:27;6417:55;;6468:1;6465;6458:12;6417:55;6504:6;6491:20;6530:4;6553:18;6590:2;6586;6583:10;6580:36;;;6596:18;;:::i;:::-;6642:2;6639:1;6635:10;6674:2;6668:9;6737:2;6733:7;6728:2;6724;6720:11;6716:25;6708:6;6704:38;6792:6;6780:10;6777:22;6772:2;6760:10;6757:18;6754:46;6751:72;;;6803:18;;:::i;:::-;6839:2;6832:22;6889:18;;;6965:15;;;6961:24;;;6923:15;;;;-1:-1:-1;6997:15:78;;;6994:35;;;7025:1;7022;7015:12;6994:35;7061:2;7053:6;7049:15;7038:26;;7073:216;7089:6;7084:3;7081:15;7073:216;;;7169:3;7156:17;7186:30;7210:5;7186:30;:::i;:::-;7229:18;;7267:12;;;;7106;;;;7073:216;;;7307:6;6344:975;-1:-1:-1;;;;;;;6344:975:78:o;7324:1247::-;7487:6;7495;7503;7547:9;7538:7;7534:23;7577:4;7573:2;7569:13;7566:33;;;7595:1;7592;7585:12;7566:33;7618:6;7644:2;7640;7636:11;7633:31;;;7660:1;7657;7650:12;7633:31;7686:17;;:::i;:::-;7673:30;;7726:52;7770:7;7759:9;7726:52;:::i;:::-;7719:5;7712:67;7813:62;7867:7;7861:3;7850:9;7846:19;7813:62;:::i;:::-;7806:4;7799:5;7795:16;7788:88;7910:62;7964:7;7958:3;7947:9;7943:19;7910:62;:::i;:::-;7903:4;7896:5;7892:16;7885:88;8007:39;8041:3;8030:9;8026:19;8007:39;:::i;:::-;8000:4;7993:5;7989:16;7982:65;8081:38;8114:3;8103:9;8099:19;8081:38;:::i;:::-;8074:4;8067:5;8063:16;8056:64;8154:38;8187:3;8176:9;8172:19;8154:38;:::i;:::-;8147:4;8140:5;8136:16;8129:64;8227:37;8259:3;8248:9;8244:19;8227:37;:::i;:::-;8220:4;8213:5;8209:16;8202:63;8284:5;8274:15;;8308:62;8362:7;8357:2;8346:9;8342:18;8308:62;:::i;:::-;8298:72;;;;8421:4;8410:9;8406:20;8393:34;8450:18;8442:6;8439:30;8436:50;;;8482:1;8479;8472:12;8436:50;8505:60;8557:7;8548:6;8537:9;8533:22;8505:60;:::i;:::-;8495:70;;;7324:1247;;;;;:::o;8905:1444::-;9257:3;9270:22;;;9341:13;;9242:19;;;9363:22;;;9209:4;;9439;;9416:3;9401:19;;;9466:15;;;9209:4;9509:184;9523:6;9520:1;9517:13;9509:184;;;9598:13;;9595:1;9584:28;9572:41;;9633:12;;;;9668:15;;;;9545:1;9538:9;9509:184;;;-1:-1:-1;;;9729:19:78;;;9709:18;;;9702:47;9799:13;;9821:21;;;9897:15;;;;9860:12;;;9932:1;9942:215;9958:8;9953:3;9950:17;9942:215;;;10031:15;;-1:-1:-1;;;;;10027:41:78;10013:56;;10130:17;;;;10091:14;;;;10065:1;9977:11;9942:215;;;-1:-1:-1;;;;;;;8642:31:78;;10230:2;10215:18;;8630:44;10174:5;-1:-1:-1;10188:46:78;;-1:-1:-1;;8576:104:78;10188:46;-1:-1:-1;;;;;8751:46:78;;10285:2;10270:18;;8739:59;10298:45;10338:3;10327:9;10323:19;10315:6;8884:1;8873:20;8861:33;;8809:91;10298:45;8905:1444;;;;;;;;:::o;11462:127::-;11523:10;11518:3;11514:20;11511:1;11504:31;11554:4;11551:1;11544:15;11578:4;11575:1;11568:15;11594:127;11655:10;11650:3;11646:20;11643:1;11636:31;11686:4;11683:1;11676:15;11710:4;11707:1;11700:15;11726:228;11765:3;11793:10;11830:2;11827:1;11823:10;11860:2;11857:1;11853:10;11891:3;11887:2;11883:12;11878:3;11875:21;11872:47;;;11899:18;;:::i;:::-;11935:13;;11726:228;-1:-1:-1;;;;11726:228:78:o;11959:125::-;11999:4;12027:1;12024;12021:8;12018:34;;;12032:18;;:::i;:::-;-1:-1:-1;12069:9:78;;11959:125::o;12446:135::-;12485:3;12506:17;;;12503:43;;12526:18;;:::i;:::-;-1:-1:-1;12573:1:78;12562:13;;12446:135::o;12586:221::-;12625:4;12654:10;12714;;;;12684;;12736:12;;;12733:38;;;12751:18;;:::i;:::-;12788:13;;12586:221;-1:-1:-1;;;12586:221:78:o;12812:359::-;12850:4;12894:1;12891;12880:16;12930:1;12927;12916:16;12960:1;12955:3;12951:11;13026:3;13007:16;13003:21;12999:31;12994:3;12990:41;12985:2;12978:10;12974:58;12971:84;;;13035:18;;:::i;:::-;13106:3;13088:16;13084:26;13079:3;13075:36;13071:2;13067:45;13064:71;;;13115:18;;:::i;:::-;-1:-1:-1;13152:13:78;;;12812:359;-1:-1:-1;;;12812:359:78:o;13176:127::-;13237:10;13232:3;13228:20;13225:1;13218:31;13268:4;13265:1;13258:15;13292:4;13289:1;13282:15;13308:284;13346:1;13387;13384;13373:16;13423:1;13420;13409:16;13444:3;13434:37;;13451:18;;:::i;:::-;-1:-1:-1;;13487:30:78;;-1:-1:-1;;13519:15:78;;13483:52;13480:78;;;13538:18;;:::i;:::-;13572:14;;;13308:284;-1:-1:-1;;;13308:284:78:o;13597:231::-;13637:4;-1:-1:-1;;;;;13735:10:78;;;;13705;;13757:12;;;13754:38;;;13772:18;;:::i;13833:272::-;13873:7;-1:-1:-1;;;;;13944:10:78;;;13974;;;14007:11;;14000:19;14029:12;;;14021:21;;13996:47;13993:73;;;14046:18;;:::i;:::-;14086:13;;13833:272;-1:-1:-1;;;;13833:272:78:o;14110:659::-;14148:7;14195:1;14192;14181:16;14231:1;14228;14217:16;14252;14296:1;14291:3;14287:11;14326:1;14321:3;14317:11;14373:3;14369:2;14365:12;14360:3;14357:21;14352:2;14348;14344:11;14340:39;14337:65;;;14382:18;;:::i;:::-;-1:-1:-1;;14470:1:78;14461:11;;14488;;;14510:13;;;14501:23;;14484:41;14481:67;;;14528:18;;:::i;:::-;14576:1;14571:3;14567:11;14557:21;;14625:3;14621:2;14616:13;14611:3;14607:23;14602:2;14598;14594:11;14590:41;14587:67;;;14634:18;;:::i;:::-;14701:3;14697:2;14692:13;14687:3;14683:23;14678:2;14674;14670:11;14666:41;14663:67;;;14710:18;;:::i;:::-;-1:-1:-1;;;14750:13:78;;;;;14110:659;-1:-1:-1;;;;;14110:659:78:o;14774:357::-;14812:3;14855:1;14852;14841:16;14891:1;14888;14877:16;14921:1;14916:3;14912:11;14982:3;14964:16;14960:26;14955:3;14951:36;14946:2;14939:10;14935:53;14932:79;;;14991:18;;:::i;:::-;15067:3;15048:16;15044:21;15040:31;15035:3;15031:41;15027:2;15023:50;15020:76;;;15076:18;;:::i;:::-;-1:-1:-1;15112:13:78;;14774:357;-1:-1:-1;;;14774:357:78:o;15136:168::-;15176:7;15242:1;15238;15234:6;15230:14;15227:1;15224:21;15219:1;15212:9;15205:17;15201:45;15198:71;;;15249:18;;:::i;:::-;-1:-1:-1;15289:9:78;;15136:168::o;15309:120::-;15349:1;15375;15365:35;;15380:18;;:::i;:::-;-1:-1:-1;15414:9:78;;15309:120::o;15434:238::-;15474:3;-1:-1:-1;;;;;15541:10:78;;;15571;;;15601:12;;;15593:21;;15590:47;;;15617:18;;:::i;15677:136::-;15712:3;-1:-1:-1;;;15733:22:78;;15730:48;;15758:18;;:::i;:::-;-1:-1:-1;15798:1:78;15794:13;;15677:136::o;16346:513::-;16431:6;16439;16447;16500:2;16488:9;16479:7;16475:23;16471:32;16468:52;;;16516:1;16513;16506:12;16468:52;16548:9;16542:16;16567:30;16591:5;16567:30;:::i;:::-;16666:2;16651:18;;16645:25;16616:5;;-1:-1:-1;16679:31:78;16645:25;16679:31;:::i;:::-;16781:2;16766:18;;16760:25;16729:7;;-1:-1:-1;16794:33:78;16760:25;16794:33;:::i;:::-;16846:7;16836:17;;;16346:513;;;;;:::o;16864:201::-;16904:1;-1:-1:-1;;;;;16969:10:78;;;;16988:37;;17005:18;;:::i;:::-;17043:10;;17039:20;;;;;16864:201;-1:-1:-1;;16864:201:78:o;17070:224::-;17109:3;17137:6;17170:2;17167:1;17163:10;17200:2;17197:1;17193:10;17231:3;17227:2;17223:12;17218:3;17215:21;17212:47;;;17239:18;;:::i;17299:179::-;17330:1;17356:6;17389:2;17386:1;17382:10;17411:3;17401:37;;17418:18;;:::i;:::-;17456:10;;17452:20;;;;;17299:179;-1:-1:-1;;17299:179:78:o;17838:184::-;17872:3;17919:5;17916:1;17905:20;17953:7;17949:12;17940:7;17937:25;17934:51;;17965:18;;:::i;:::-;18005:1;18001:15;;17838:184;-1:-1:-1;;17838:184:78:o;18027:275::-;18065:1;18106;18103;18092:16;18142:1;18139;18128:16;18163:3;18153:37;;18170:18;;:::i;:::-;-1:-1:-1;;18206:21:78;;-1:-1:-1;;18229:15:78;;18202:43;18199:69;;;18248:18;;:::i;18307:642::-;18345:7;18392:1;18389;18378:16;18428:1;18425;18414:16;18449:8;18485:1;18480:3;18476:11;18515:1;18510:3;18506:11;18562:3;18558:2;18554:12;18549:3;18546:21;18541:2;18537;18533:11;18529:39;18526:65;;;18571:18;;:::i;:::-;-1:-1:-1;;18650:1:78;18641:11;;18668;;;18690:13;;;18681:23;;18664:41;18661:67;;;18708:18;;:::i;18954:230::-;18993:3;19021:12;19060:2;19057:1;19053:10;19090:2;19087:1;19083:10;19121:3;19117:2;19113:12;19108:3;19105:21;19102:47;;;19129:18;;:::i;19189:128::-;19229:3;19260:1;19256:6;19253:1;19250:13;19247:39;;;19266:18;;:::i;:::-;-1:-1:-1;19302:9:78;;19189:128::o;19322:112::-;19354:1;19380;19370:35;;19385:18;;:::i;:::-;-1:-1:-1;19419:9:78;;19322:112::o", "linkReferences": {}}, "methodIdentifiers": {"perform(PoolsharkStructs.GlobalState,PoolsharkStructs.LimitImmutables,uint32[])": "1fb854c0"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"sampleLengthNext\",\"type\":\"uint16\"}],\"name\":\"SampleLengthIncreased\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int56\",\"name\":\"tickSecondsAccum\",\"type\":\"int56\"},{\"indexed\":false,\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160\"}],\"name\":\"SampleRecorded\",\"type\":\"event\"},{\"inputs\":[{\"components\":[{\"components\":[{\"components\":[{\"internalType\":\"uint16\",\"name\":\"index\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"count\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"countMax\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.SampleState\",\"name\":\"samples\",\"type\":\"tuple\"},{\"internalType\":\"uint200\",\"name\":\"feeGrowthGlobal0\",\"type\":\"uint200\"},{\"internalType\":\"uint200\",\"name\":\"feeGrowthGlobal1\",\"type\":\"uint200\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"int56\",\"name\":\"tickSecondsAccum\",\"type\":\"int56\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"},{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee0\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee1\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.RangePoolState\",\"name\":\"pool\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"protocolFees\",\"type\":\"uint128\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee\",\"type\":\"uint16\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"}],\"internalType\":\"struct PoolsharkStructs.LimitPoolState\",\"name\":\"pool0\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"protocolFees\",\"type\":\"uint128\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee\",\"type\":\"uint16\"},{\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"}],\"internalType\":\"struct PoolsharkStructs.LimitPoolState\",\"name\":\"pool1\",\"type\":\"tuple\"},{\"internalType\":\"uint128\",\"name\":\"liquidityGlobal\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"positionIdNext\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"},{\"internalType\":\"uint8\",\"name\":\"unlocked\",\"type\":\"uint8\"}],\"internalType\":\"struct PoolsharkStructs.GlobalState\",\"name\":\"state\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"factory\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"min\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"max\",\"type\":\"uint160\"}],\"internalType\":\"struct PoolsharkStructs.PriceBounds\",\"name\":\"bounds\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"genesisTime\",\"type\":\"uint32\"},{\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.LimitImmutables\",\"name\":\"constants\",\"type\":\"tuple\"},{\"internalType\":\"uint32[]\",\"name\":\"secondsAgo\",\"type\":\"uint32[]\"}],\"name\":\"perform\",\"outputs\":[{\"internalType\":\"int56[]\",\"name\":\"tickSecondsAccum\",\"type\":\"int56[]\"},{\"internalType\":\"uint160[]\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160[]\"},{\"internalType\":\"uint160\",\"name\":\"averagePrice\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"averageLiquidity\",\"type\":\"uint128\"},{\"internalType\":\"int24\",\"name\":\"averageTick\",\"type\":\"int24\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/pool/SampleCall.sol\":\"SampleCall\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/range/IRangePool.sol\":{\"keccak256\":\"0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab\",\"dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt\"]},\"contracts/interfaces/range/IRangePoolManager.sol\":{\"keccak256\":\"0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065\",\"dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP\"]},\"contracts/interfaces/structs/LimitPoolStructs.sol\":{\"keccak256\":\"0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef\",\"dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/interfaces/structs/RangePoolStructs.sol\":{\"keccak256\":\"0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9\",\"dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx\"]},\"contracts/libraries/Samples.sol\":{\"keccak256\":\"0xae162c0e142bbe444c3748135d74bf272849768abca62c1908794bb72551c0eb\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://cf38a14a2a9653dc9b44ad37f091b7c6117be6ca8a76b5f9e279998bd9060763\",\"dweb:/ipfs/QmYxJm1w9YL4bYHJqNcLTRxiwYKH68dTzNGGQiQxFmnADE\"]},\"contracts/libraries/math/ConstantProduct.sol\":{\"keccak256\":\"0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8\",\"dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED\"]},\"contracts/libraries/math/OverflowMath.sol\":{\"keccak256\":\"0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29\",\"dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T\"]},\"contracts/libraries/pool/SampleCall.sol\":{\"keccak256\":\"0x164ef9d8eabaeeee2219e8a9ff2ee0d8c9f9b580b09cf766205e2ecef5e899cb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d16c401f176615cfe62b4f242426c3d674367b14feda3f5e5c0c6ad99403d4be\",\"dweb:/ipfs/QmbhohgcHi5bMvc6ogiyv1YaqrLwq1gfWFS3JAXH4x718R\"]},\"contracts/libraries/utils/SafeCast.sol\":{\"keccak256\":\"0xc4ec846404c1111c6353c8b2251469e07f32570b1ffafc58701c385c458c21e2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://51b7397ff450c2aedafdcb40a6ca181c8e1947fb409dbbcd8b000b8eee2e9fcf\",\"dweb:/ipfs/QmQ3wPdCtuLreoEkvFoifsx4drC5kT3p2CwGKBBn2qQso4\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint16", "name": "sampleLengthNext", "type": "uint16", "indexed": false}], "type": "event", "name": "SampleLengthIncreased", "anonymous": false}, {"inputs": [{"internalType": "int56", "name": "tickSecondsAccum", "type": "int56", "indexed": false}, {"internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160", "indexed": false}], "type": "event", "name": "SampleRecorded", "anonymous": false}, {"inputs": [{"internalType": "struct PoolsharkStructs.GlobalState", "name": "state", "type": "tuple", "components": [{"internalType": "struct PoolsharkStructs.RangePoolState", "name": "pool", "type": "tuple", "components": [{"internalType": "struct PoolsharkStructs.SampleState", "name": "samples", "type": "tuple", "components": [{"internalType": "uint16", "name": "index", "type": "uint16"}, {"internalType": "uint16", "name": "count", "type": "uint16"}, {"internalType": "uint16", "name": "countMax", "type": "uint16"}]}, {"internalType": "uint200", "name": "feeGrowthGlobal0", "type": "uint200"}, {"internalType": "uint200", "name": "feeGrowthGlobal1", "type": "uint200"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160"}, {"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "int56", "name": "tickSecondsAccum", "type": "int56"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}, {"internalType": "uint16", "name": "protocolSwapFee0", "type": "uint16"}, {"internalType": "uint16", "name": "protocolSwapFee1", "type": "uint16"}]}, {"internalType": "struct PoolsharkStructs.LimitPoolState", "name": "pool0", "type": "tuple", "components": [{"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint128", "name": "protocolFees", "type": "uint128"}, {"internalType": "uint16", "name": "protocolFillFee", "type": "uint16"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}]}, {"internalType": "struct PoolsharkStructs.LimitPoolState", "name": "pool1", "type": "tuple", "components": [{"internalType": "uint160", "name": "price", "type": "uint160"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "uint128", "name": "protocolFees", "type": "uint128"}, {"internalType": "uint16", "name": "protocolFillFee", "type": "uint16"}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24"}]}, {"internalType": "uint128", "name": "liquidityGlobal", "type": "uint128"}, {"internalType": "uint32", "name": "positionIdNext", "type": "uint32"}, {"internalType": "uint32", "name": "epoch", "type": "uint32"}, {"internalType": "uint8", "name": "unlocked", "type": "uint8"}]}, {"internalType": "struct PoolsharkStructs.LimitImmutables", "name": "constants", "type": "tuple", "components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "poolImpl", "type": "address"}, {"internalType": "address", "name": "factory", "type": "address"}, {"internalType": "struct PoolsharkStructs.PriceBounds", "name": "bounds", "type": "tuple", "components": [{"internalType": "uint160", "name": "min", "type": "uint160"}, {"internalType": "uint160", "name": "max", "type": "uint160"}]}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "uint32", "name": "genesisTime", "type": "uint32"}, {"internalType": "int16", "name": "tickSpacing", "type": "int16"}, {"internalType": "uint16", "name": "swapFee", "type": "uint16"}]}, {"internalType": "uint32[]", "name": "secondsAgo", "type": "uint32[]"}], "stateMutability": "view", "type": "function", "name": "perform", "outputs": [{"internalType": "int56[]", "name": "tickSecondsAccum", "type": "int56[]"}, {"internalType": "uint160[]", "name": "secondsPerLiquidityAccum", "type": "uint160[]"}, {"internalType": "uint160", "name": "averagePrice", "type": "uint160"}, {"internalType": "uint128", "name": "averageLiquidity", "type": "uint128"}, {"internalType": "int24", "name": "averageTick", "type": "int24"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/pool/SampleCall.sol": "SampleCall"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePool.sol": {"keccak256": "0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf", "urls": ["bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab", "dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePoolManager.sol": {"keccak256": "0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139", "urls": ["bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065", "dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/LimitPoolStructs.sol": {"keccak256": "0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4", "urls": ["bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef", "dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/interfaces/structs/RangePoolStructs.sol": {"keccak256": "0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd", "urls": ["bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9", "dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx"], "license": "GPLv3"}, "contracts/libraries/Samples.sol": {"keccak256": "0xae162c0e142bbe444c3748135d74bf272849768abca62c1908794bb72551c0eb", "urls": ["bzz-raw://cf38a14a2a9653dc9b44ad37f091b7c6117be6ca8a76b5f9e279998bd9060763", "dweb:/ipfs/QmYxJm1w9YL4bYHJqNcLTRxiwYKH68dTzNGGQiQxFmnADE"], "license": "GPLv3"}, "contracts/libraries/math/ConstantProduct.sol": {"keccak256": "0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3", "urls": ["bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8", "dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED"], "license": "GPL-3.0-or-later"}, "contracts/libraries/math/OverflowMath.sol": {"keccak256": "0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b", "urls": ["bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29", "dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T"], "license": "MIT"}, "contracts/libraries/pool/SampleCall.sol": {"keccak256": "0x164ef9d8eabaeeee2219e8a9ff2ee0d8c9f9b580b09cf766205e2ecef5e899cb", "urls": ["bzz-raw://d16c401f176615cfe62b4f242426c3d674367b14feda3f5e5c0c6ad99403d4be", "dweb:/ipfs/QmbhohgcHi5bMvc6ogiyv1YaqrLwq1gfWFS3JAXH4x718R"], "license": "MIT"}, "contracts/libraries/utils/SafeCast.sol": {"keccak256": "0xc4ec846404c1111c6353c8b2251469e07f32570b1ffafc58701c385c458c21e2", "urls": ["bzz-raw://51b7397ff450c2aedafdcb40a6ca181c8e1947fb409dbbcd8b000b8eee2e9fcf", "dweb:/ipfs/QmQ3wPdCtuLreoEkvFoifsx4drC5kT3p2CwGKBBn2qQso4"], "license": "GPL-2.0-or-later"}}, "version": 1}, "id": 52}