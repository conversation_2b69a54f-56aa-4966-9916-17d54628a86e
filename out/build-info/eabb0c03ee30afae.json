{"id": "eabb0c03ee30afae", "source_id_to_path": {"0": "contracts/LimitPool.sol", "1": "contracts/LimitPoolFactory.sol", "2": "contracts/base/events/FinStakerEvents.sol", "3": "contracts/base/events/LimitPoolEvents.sol", "4": "contracts/base/events/LimitPoolFactoryEvents.sol", "5": "contracts/base/events/LimitPoolManagerEvents.sol", "6": "contracts/base/events/PoolsharkRouterEvents.sol", "7": "contracts/base/events/PositionERC1155Events.sol", "8": "contracts/base/events/RangeStakerEvents.sol", "9": "contracts/base/storage/LimitPoolFactoryStorage.sol", "10": "contracts/base/storage/LimitPoolImmutables.sol", "11": "contracts/base/storage/LimitPoolStorage.sol", "12": "contracts/base/storage/PositionERC1155Immutables.sol", "13": "contracts/external/openzeppelin/security/LimitReentrancyGuard.sol", "14": "contracts/external/openzeppelin/security/ReentrancyGuard.sol", "15": "contracts/external/solady/Clone.sol", "16": "contracts/external/solady/LibClone.sol", "17": "contracts/interfaces/IERC20Minimal.sol", "18": "contracts/interfaces/IPool.sol", "19": "contracts/interfaces/IPositionERC1155.sol", "20": "contracts/interfaces/IWETH9.sol", "21": "contracts/interfaces/callbacks/ICoverPoolCallback.sol", "22": "contracts/interfaces/callbacks/ILimitPoolCallback.sol", "23": "contracts/interfaces/cover/ICoverPool.sol", "24": "contracts/interfaces/cover/ICoverPoolFactory.sol", "25": "contracts/interfaces/cover/ITwapSource.sol", "26": "contracts/interfaces/limit/ILimitPool.sol", "27": "contracts/interfaces/limit/ILimitPoolFactory.sol", "28": "contracts/interfaces/limit/ILimitPoolManager.sol", "29": "contracts/interfaces/limit/ILimitPoolStorageView.sol", "30": "contracts/interfaces/limit/ILimitPoolView.sol", "31": "contracts/interfaces/range/IRangePool.sol", "32": "contracts/interfaces/range/IRangePoolFactory.sol", "33": "contracts/interfaces/range/IRangePoolManager.sol", "34": "contracts/interfaces/staking/IRangeStaker.sol", "35": "contracts/interfaces/structs/LimitPoolStructs.sol", "36": "contracts/interfaces/structs/PoolsharkStructs.sol", "37": "contracts/interfaces/structs/RangePoolStructs.sol", "38": "contracts/libraries/Samples.sol", "39": "contracts/libraries/TickMap.sol", "40": "contracts/libraries/Ticks.sol", "41": "contracts/libraries/limit/Claims.sol", "42": "contracts/libraries/limit/EpochMap.sol", "43": "contracts/libraries/limit/LimitPositions.sol", "44": "contracts/libraries/limit/LimitTicks.sol", "45": "contracts/libraries/limit/pool/BurnLimitCall.sol", "46": "contracts/libraries/limit/pool/MintLimitCall.sol", "47": "contracts/libraries/limit/pool/SnapshotLimitCall.sol", "48": "contracts/libraries/math/ConstantProduct.sol", "49": "contracts/libraries/math/OverflowMath.sol", "50": "contracts/libraries/pool/FeesCall.sol", "51": "contracts/libraries/pool/QuoteCall.sol", "52": "contracts/libraries/pool/SampleCall.sol", "53": "contracts/libraries/pool/SwapCall.sol", "54": "contracts/libraries/range/RangePositions.sol", "55": "contracts/libraries/range/RangeTicks.sol", "56": "contracts/libraries/range/math/FeeMath.sol", "57": "contracts/libraries/range/pool/BurnRangeCall.sol", "58": "contracts/libraries/range/pool/MintRangeCall.sol", "59": "contracts/libraries/range/pool/SnapshotRangeCall.sol", "60": "contracts/libraries/utils/Bytes.sol", "61": "contracts/libraries/utils/Collect.sol", "62": "contracts/libraries/utils/PositionTokens.sol", "63": "contracts/libraries/utils/SafeCast.sol", "64": "contracts/libraries/utils/SafeTransfers.sol", "65": "contracts/libraries/utils/String.sol", "66": "contracts/staking/RangeStaker.sol", "67": "contracts/test/Token20.sol", "68": "contracts/test/WETH9.sol", "69": "contracts/utils/LimitPoolManager.sol", "70": "contracts/utils/PoolsharkRouter.sol", "71": "contracts/utils/PositionERC1155.sol", "72": "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "73": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "74": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "75": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "76": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "77": "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol"}, "language": "Solidity"}