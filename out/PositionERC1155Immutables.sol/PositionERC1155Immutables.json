{"abi": [{"type": "function", "name": "tokenName", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "tokenSymbol", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}], "bytecode": {"object": "0x6080604052348015600f57600080fd5b5060a18061001e6000396000f3fe6080604052348015600f57600080fd5b506004361060325760003560e01c80636c02a9311460375780637b61c320146057575b600080fd5b3660011981013560f01c9003355b60405190815260200160405180910390f35b3660011981013560f01c900360200135604556fea2646970667358221220a257cb3abe2ec384a51b654040b939aeee1c9dbb78c7fc23563d0868ec98415664736f6c634300080d0033", "sourceMap": "115:245:12:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080604052348015600f57600080fd5b506004361060325760003560e01c80636c02a9311460375780637b61c320146057575b600080fd5b3660011981013560f01c9003355b60405190815260200160405180910390f35b3660011981013560f01c900360200135604556fea2646970667358221220a257cb3abe2ec384a51b654040b939aeee1c9dbb78c7fc23563d0868ec98415664736f6c634300080d0033", "sourceMap": "115:245:12:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;165:92;13904:14:15;-1:-1:-1;;13900:22:15;;13887:36;13882:3;13878:46;13858:67;;2840:36;165:92:12;;;160:25:78;;;148:2;133:18;165:92:12;;;;;;;263:95;13904:14:15;-1:-1:-1;;13900:22:15;;13887:36;13882:3;13878:46;13858:67;;348:2:12;2853:22:15;2840:36;263:95:12;165:92::o", "linkReferences": {}}, "methodIdentifiers": {"tokenName()": "6c02a931", "tokenSymbol()": "7b61c320"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"tokenName\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"tokenSymbol\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/base/storage/PositionERC1155Immutables.sol\":\"PositionERC1155Immutables\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/base/storage/PositionERC1155Immutables.sol\":{\"keccak256\":\"0x5697a90a5648294a0023133c8dee7087d15627808486ee8fbe0aa9e64003fd6c\",\"license\":\"BSD\",\"urls\":[\"bzz-raw://ed55a2729cd43e6eb528819abe7a42384ee932948b13a1c1663069abf8f2efc6\",\"dweb:/ipfs/QmWksdYmZUqvAG88m8kPYE5fFQBRUDwjcxHz1fJ6rUpvA1\"]},\"contracts/external/solady/Clone.sol\":{\"keccak256\":\"0xe7dc35cc81529e1e43b249f99de3a26e595ffd209450b85f3e8bc74a0d9aea01\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6d0471fad81c71706936f7b014b0cfcd5f03337450838409c8c02fedc38a8560\",\"dweb:/ipfs/Qmede4qMoU29saLxaBxaA72kuWt4794HLcB7ZdXHRrBFXC\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "pure", "type": "function", "name": "tokenName", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "tokenSymbol", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/base/storage/PositionERC1155Immutables.sol": "PositionERC1155Immutables"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/base/storage/PositionERC1155Immutables.sol": {"keccak256": "0x5697a90a5648294a0023133c8dee7087d15627808486ee8fbe0aa9e64003fd6c", "urls": ["bzz-raw://ed55a2729cd43e6eb528819abe7a42384ee932948b13a1c1663069abf8f2efc6", "dweb:/ipfs/QmWksdYmZUqvAG88m8kPYE5fFQBRUDwjcxHz1fJ6rUpvA1"], "license": "BSD"}, "contracts/external/solady/Clone.sol": {"keccak256": "0xe7dc35cc81529e1e43b249f99de3a26e595ffd209450b85f3e8bc74a0d9aea01", "urls": ["bzz-raw://6d0471fad81c71706936f7b014b0cfcd5f03337450838409c8c02fedc38a8560", "dweb:/ipfs/Qmede4qMoU29saLxaBxaA72kuWt4794HLcB7ZdXHRrBFXC"], "license": "MIT"}}, "version": 1}, "id": 12}