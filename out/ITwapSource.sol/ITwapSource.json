{"abi": [{"type": "function", "name": "calculateAverageTick", "inputs": [{"name": "constants", "type": "tuple", "internalType": "struct PoolsharkStructs.CoverImmutables", "components": [{"name": "source", "type": "address", "internalType": "contract ITwapSource"}, {"name": "bounds", "type": "tuple", "internalType": "struct PoolsharkStructs.PriceBounds", "components": [{"name": "min", "type": "uint160", "internalType": "uint160"}, {"name": "max", "type": "uint160", "internalType": "uint160"}]}, {"name": "owner", "type": "address", "internalType": "address"}, {"name": "token0", "type": "address", "internalType": "address"}, {"name": "token1", "type": "address", "internalType": "address"}, {"name": "poolImpl", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "inputPool", "type": "address", "internalType": "address"}, {"name": "minAmountPerAuction", "type": "uint128", "internalType": "uint128"}, {"name": "genesisTime", "type": "uint32", "internalType": "uint32"}, {"name": "minPositionWidth", "type": "int16", "internalType": "int16"}, {"name": "tickSpread", "type": "int16", "internalType": "int16"}, {"name": "twap<PERSON><PERSON>th", "type": "uint16", "internalType": "uint16"}, {"name": "auctionLength", "type": "uint16", "internalType": "uint16"}, {"name": "sampleInterval", "type": "uint16", "internalType": "uint16"}, {"name": "token0Decimals", "type": "uint8", "internalType": "uint8"}, {"name": "token1Decimals", "type": "uint8", "internalType": "uint8"}, {"name": "minAmountLowerPriced", "type": "bool", "internalType": "bool"}]}, {"name": "latestTick", "type": "int24", "internalType": "int24"}], "outputs": [{"name": "averageTick", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "factory", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "feeTierTickSpacing", "inputs": [{"name": "feeTier", "type": "uint16", "internalType": "uint16"}], "outputs": [{"name": "tickSpacing", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "getPool", "inputs": [{"name": "tokenA", "type": "address", "internalType": "address"}, {"name": "tokenB", "type": "address", "internalType": "address"}, {"name": "feeTier", "type": "uint16", "internalType": "uint16"}], "outputs": [{"name": "pool", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "constants", "type": "tuple", "internalType": "struct PoolsharkStructs.CoverImmutables", "components": [{"name": "source", "type": "address", "internalType": "contract ITwapSource"}, {"name": "bounds", "type": "tuple", "internalType": "struct PoolsharkStructs.PriceBounds", "components": [{"name": "min", "type": "uint160", "internalType": "uint160"}, {"name": "max", "type": "uint160", "internalType": "uint160"}]}, {"name": "owner", "type": "address", "internalType": "address"}, {"name": "token0", "type": "address", "internalType": "address"}, {"name": "token1", "type": "address", "internalType": "address"}, {"name": "poolImpl", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "inputPool", "type": "address", "internalType": "address"}, {"name": "minAmountPerAuction", "type": "uint128", "internalType": "uint128"}, {"name": "genesisTime", "type": "uint32", "internalType": "uint32"}, {"name": "minPositionWidth", "type": "int16", "internalType": "int16"}, {"name": "tickSpread", "type": "int16", "internalType": "int16"}, {"name": "twap<PERSON><PERSON>th", "type": "uint16", "internalType": "uint16"}, {"name": "auctionLength", "type": "uint16", "internalType": "uint16"}, {"name": "sampleInterval", "type": "uint16", "internalType": "uint16"}, {"name": "token0Decimals", "type": "uint8", "internalType": "uint8"}, {"name": "token1Decimals", "type": "uint8", "internalType": "uint8"}, {"name": "minAmountLowerPriced", "type": "bool", "internalType": "bool"}]}], "outputs": [{"name": "initializable", "type": "uint8", "internalType": "uint8"}, {"name": "startingTick", "type": "int24", "internalType": "int24"}], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"calculateAverageTick((address,(uint160,uint160),address,address,address,address,address,address,uint128,uint32,int16,int16,uint16,uint16,uint16,uint8,uint8,bool),int24)": "7ab156be", "factory()": "c45a0155", "feeTierTickSpacing(uint16)": "5447cea5", "getPool(address,address,uint16)": "b1779714", "initialize((address,(uint160,uint160),address,address,address,address,address,address,uint128,uint32,int16,int16,uint16,uint16,uint16,uint8,uint8,bool))": "aa937c30"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"contract ITwapSource\",\"name\":\"source\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"min\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"max\",\"type\":\"uint160\"}],\"internalType\":\"struct PoolsharkStructs.PriceBounds\",\"name\":\"bounds\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"inputPool\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"minAmountPerAuction\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"genesisTime\",\"type\":\"uint32\"},{\"internalType\":\"int16\",\"name\":\"minPositionWidth\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"tickSpread\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"twapLength\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"auctionLength\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"sampleInterval\",\"type\":\"uint16\"},{\"internalType\":\"uint8\",\"name\":\"token0Decimals\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"token1Decimals\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"minAmountLowerPriced\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.CoverImmutables\",\"name\":\"constants\",\"type\":\"tuple\"},{\"internalType\":\"int24\",\"name\":\"latestTick\",\"type\":\"int24\"}],\"name\":\"calculateAverageTick\",\"outputs\":[{\"internalType\":\"int24\",\"name\":\"averageTick\",\"type\":\"int24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"factory\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"feeTier\",\"type\":\"uint16\"}],\"name\":\"feeTierTickSpacing\",\"outputs\":[{\"internalType\":\"int24\",\"name\":\"tickSpacing\",\"type\":\"int24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenA\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenB\",\"type\":\"address\"},{\"internalType\":\"uint16\",\"name\":\"feeTier\",\"type\":\"uint16\"}],\"name\":\"getPool\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"contract ITwapSource\",\"name\":\"source\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"min\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"max\",\"type\":\"uint160\"}],\"internalType\":\"struct PoolsharkStructs.PriceBounds\",\"name\":\"bounds\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"inputPool\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"minAmountPerAuction\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"genesisTime\",\"type\":\"uint32\"},{\"internalType\":\"int16\",\"name\":\"minPositionWidth\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"tickSpread\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"twapLength\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"auctionLength\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"sampleInterval\",\"type\":\"uint16\"},{\"internalType\":\"uint8\",\"name\":\"token0Decimals\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"token1Decimals\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"minAmountLowerPriced\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.CoverImmutables\",\"name\":\"constants\",\"type\":\"tuple\"}],\"name\":\"initialize\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"initializable\",\"type\":\"uint8\"},{\"internalType\":\"int24\",\"name\":\"startingTick\",\"type\":\"int24\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/cover/ITwapSource.sol\":\"ITwapSource\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct PoolsharkStructs.CoverImmutables", "name": "constants", "type": "tuple", "components": [{"internalType": "contract ITwapSource", "name": "source", "type": "address"}, {"internalType": "struct PoolsharkStructs.PriceBounds", "name": "bounds", "type": "tuple", "components": [{"internalType": "uint160", "name": "min", "type": "uint160"}, {"internalType": "uint160", "name": "max", "type": "uint160"}]}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "address", "name": "poolImpl", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "address", "name": "inputPool", "type": "address"}, {"internalType": "uint128", "name": "minAmountPerAuction", "type": "uint128"}, {"internalType": "uint32", "name": "genesisTime", "type": "uint32"}, {"internalType": "int16", "name": "minPositionWidth", "type": "int16"}, {"internalType": "int16", "name": "tickSpread", "type": "int16"}, {"internalType": "uint16", "name": "twap<PERSON><PERSON>th", "type": "uint16"}, {"internalType": "uint16", "name": "auctionLength", "type": "uint16"}, {"internalType": "uint16", "name": "sampleInterval", "type": "uint16"}, {"internalType": "uint8", "name": "token0Decimals", "type": "uint8"}, {"internalType": "uint8", "name": "token1Decimals", "type": "uint8"}, {"internalType": "bool", "name": "minAmountLowerPriced", "type": "bool"}]}, {"internalType": "int24", "name": "latestTick", "type": "int24"}], "stateMutability": "view", "type": "function", "name": "calculateAverageTick", "outputs": [{"internalType": "int24", "name": "averageTick", "type": "int24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "factory", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint16", "name": "feeTier", "type": "uint16"}], "stateMutability": "view", "type": "function", "name": "feeTierTickSpacing", "outputs": [{"internalType": "int24", "name": "tickSpacing", "type": "int24"}]}, {"inputs": [{"internalType": "address", "name": "tokenA", "type": "address"}, {"internalType": "address", "name": "tokenB", "type": "address"}, {"internalType": "uint16", "name": "feeTier", "type": "uint16"}], "stateMutability": "view", "type": "function", "name": "getPool", "outputs": [{"internalType": "address", "name": "pool", "type": "address"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.CoverImmutables", "name": "constants", "type": "tuple", "components": [{"internalType": "contract ITwapSource", "name": "source", "type": "address"}, {"internalType": "struct PoolsharkStructs.PriceBounds", "name": "bounds", "type": "tuple", "components": [{"internalType": "uint160", "name": "min", "type": "uint160"}, {"internalType": "uint160", "name": "max", "type": "uint160"}]}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "address", "name": "poolImpl", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "address", "name": "inputPool", "type": "address"}, {"internalType": "uint128", "name": "minAmountPerAuction", "type": "uint128"}, {"internalType": "uint32", "name": "genesisTime", "type": "uint32"}, {"internalType": "int16", "name": "minPositionWidth", "type": "int16"}, {"internalType": "int16", "name": "tickSpread", "type": "int16"}, {"internalType": "uint16", "name": "twap<PERSON><PERSON>th", "type": "uint16"}, {"internalType": "uint16", "name": "auctionLength", "type": "uint16"}, {"internalType": "uint16", "name": "sampleInterval", "type": "uint16"}, {"internalType": "uint8", "name": "token0Decimals", "type": "uint8"}, {"internalType": "uint8", "name": "token1Decimals", "type": "uint8"}, {"internalType": "bool", "name": "minAmountLowerPriced", "type": "bool"}]}], "stateMutability": "nonpayable", "type": "function", "name": "initialize", "outputs": [{"internalType": "uint8", "name": "initializable", "type": "uint8"}, {"internalType": "int24", "name": "startingTick", "type": "int24"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/cover/ITwapSource.sol": "ITwapSource"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}}, "version": 1}, "id": 25}