{"abi": [{"type": "function", "name": "getIndices", "inputs": [{"name": "tick", "type": "int24", "internalType": "int24"}, {"name": "tickSpacing", "type": "int24", "internalType": "int24"}], "outputs": [{"name": "tickIndex", "type": "uint256", "internalType": "uint256"}, {"name": "wordIndex", "type": "uint256", "internalType": "uint256"}, {"name": "blockIndex", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}], "bytecode": {"object": "0x61038a61003a600b82828239805160001a60731461002d57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600436106100355760003560e01c80638bea96201461003a575b600080fd5b61004d610048366004610220565b61006c565b6040805193845260208401929092529082015260600160405180910390f35b60008080620d89e8600286900b13156100c25760405162461bcd60e51b81526020600482015260136024820152725469636b496e6465784f766572666c6f77282960681b60448201526064015b60405180910390fd5b620d89e719600286900b12156101115760405162461bcd60e51b81526020600482015260146024820152735469636b496e646578556e646572666c6f77282960601b60448201526064016100b9565b600284810b0560020b8560020b8161012b5761012b610253565b0760020b156101475761014485600286810b5b056101eb565b94505b600284810b0560020b61016b620d89e71960028760020b8161013e5761013e610253565b61017987600288810b61013e565b0360020b8161018a5761018a610253565b0560020b92505050600881901c601082901c60ff8111156101e45760405162461bcd60e51b8152602060048201526014602482015273426c6f636b496e6465784f766572666c6f77282960601b60448201526064016100b9565b9250925092565b6000816101f8818561027f565b61020291906102c7565b9392505050565b8035600281900b811461021b57600080fd5b919050565b6000806040838503121561023357600080fd5b61023c83610209565b915061024a60208401610209565b90509250929050565b634e487b7160e01b600052601260045260246000fd5b634e487b7160e01b600052601160045260246000fd5b60008160020b8360020b806102a457634e487b7160e01b600052601260045260246000fd5b627fffff198214600019821416156102be576102be610269565b90059392505050565b60008160020b8360020b627fffff6000821360008413838304851182821616156102f3576102f3610269565b627fffff19600085128281168783058712161561031257610312610269565b6000871292508582058712848416161561032e5761032e610269565b8585058712818416161561034457610344610269565b505050929091029594505050505056fea264697066735822122058bc7a57d747cfb234afe7f4ffba29fb6c78f40c75d752ef18fd7f278eca786b64736f6c634300080d0033", "sourceMap": "150:16274:39:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;150:16274:39;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x73000000000000000000000000000000000000000030146080604052600436106100355760003560e01c80638bea96201461003a575b600080fd5b61004d610048366004610220565b61006c565b6040805193845260208401929092529082015260600160405180910390f35b60008080620d89e8600286900b13156100c25760405162461bcd60e51b81526020600482015260136024820152725469636b496e6465784f766572666c6f77282960681b60448201526064015b60405180910390fd5b620d89e719600286900b12156101115760405162461bcd60e51b81526020600482015260146024820152735469636b496e646578556e646572666c6f77282960601b60448201526064016100b9565b600284810b0560020b8560020b8161012b5761012b610253565b0760020b156101475761014485600286810b5b056101eb565b94505b600284810b0560020b61016b620d89e71960028760020b8161013e5761013e610253565b61017987600288810b61013e565b0360020b8161018a5761018a610253565b0560020b92505050600881901c601082901c60ff8111156101e45760405162461bcd60e51b8152602060048201526014602482015273426c6f636b496e6465784f766572666c6f77282960601b60448201526064016100b9565b9250925092565b6000816101f8818561027f565b61020291906102c7565b9392505050565b8035600281900b811461021b57600080fd5b919050565b6000806040838503121561023357600080fd5b61023c83610209565b915061024a60208401610209565b90509250929050565b634e487b7160e01b600052601260045260246000fd5b634e487b7160e01b600052601160045260246000fd5b60008160020b8360020b806102a457634e487b7160e01b600052601260045260246000fd5b627fffff198214600019821416156102be576102be610269565b90059392505050565b60008160020b8360020b627fffff6000821360008413838304851182821616156102f3576102f3610269565b627fffff19600085128281168783058712161561031257610312610269565b6000871292508582058712848416161561032e5761032e610269565b8585058712818416161561034457610344610269565b505050929091029594505050505056fea264697066735822122058bc7a57d747cfb234afe7f4ffba29fb6c78f40c75d752ef18fd7f278eca786b64736f6c634300080d0033", "sourceMap": "150:16274:39:-:0;;;;;;;;;;;;;;;;;;;;;;;;9446:929;;;;;;:::i;:::-;;:::i;:::-;;;;646:25:78;;;702:2;687:18;;680:34;;;;730:18;;;723:34;634:2;619:18;9446:929:39;;;;;;;;9553:17;;;5670:9:48;9686:31:39;;;;;9682:74;;;9719:37;;-1:-1:-1;;;9719:37:39;;970:2:78;9719:37:39;;;952:21:78;1009:2;989:18;;;982:30;-1:-1:-1;;;1028:18:78;;;1021:49;1087:18;;9719:37:39;;;;;;;;;-1:-1:-1;;9774:31:39;;;;;9770:75;;;9807:38;;-1:-1:-1;;;9807:38:39;;1318:2:78;9807:38:39;;;1300:21:78;1357:2;1337:18;;;1330:30;-1:-1:-1;;;1376:18:78;;;1369:50;1436:18;;9807:38:39;1116:344:78;9807:38:39;9885:1;9871:15;;;;9863:24;;:4;:24;;;;;;;:::i;:::-;;:29;;;9859:70;;9901:28;9907:4;9927:1;9913:15;;;;;9901:5;:28::i;:::-;9894:35;;9859:70;10151:1;10137:15;;;;9970:183;;10043:48;-1:-1:-1;;10089:1:39;10075:11;:15;;;;;;;:::i;10043:48::-;9971:28;9977:4;9997:1;9983:15;;;;;9971:28;:120;9970:183;;;;;;;:::i;:::-;;9963:191;;;-1:-1:-1;;;10194:1:39;10181:14;;;10259:2;10246:15;;;10315:3;10302:16;;10298:60;;;10320:38;;-1:-1:-1;;;10320:38:39;;1799:2:78;10320:38:39;;;1781:21:78;1838:2;1818:18;;;1811:30;-1:-1:-1;;;1857:18:78;;;1850:50;1917:18;;10320:38:39;1597:344:78;10320:38:39;9446:929;;;;;:::o;12827:180::-;12927:17;12989:11;12968:18;12989:11;12968:4;:18;:::i;:::-;:32;;;;:::i;:::-;12961:39;12827:180;-1:-1:-1;;;12827:180:39:o;14:160:78:-;80:20;;140:1;129:20;;;119:31;;109:59;;164:1;161;154:12;109:59;14:160;;;:::o;179:252::-;243:6;251;304:2;292:9;283:7;279:23;275:32;272:52;;;320:1;317;310:12;272:52;343:27;360:9;343:27;:::i;:::-;333:37;;389:36;421:2;410:9;406:18;389:36;:::i;:::-;379:46;;179:252;;;;;:::o;1465:127::-;1526:10;1521:3;1517:20;1514:1;1507:31;1557:4;1554:1;1547:15;1581:4;1578:1;1571:15;1946:127;2007:10;2002:3;1998:20;1995:1;1988:31;2038:4;2035:1;2028:15;2062:4;2059:1;2052:15;2078:372;2116:1;2157;2154;2143:16;2193:1;2190;2179:16;2214:3;2204:134;;2260:10;2255:3;2251:20;2248:1;2241:31;2295:4;2292:1;2285:15;2323:4;2320:1;2313:15;2204:134;-1:-1:-1;;2354:21:78;;-1:-1:-1;;2377:15:78;;2350:43;2347:69;;;2396:18;;:::i;:::-;2430:14;;;2078:372;-1:-1:-1;;;2078:372:78:o;2455:642::-;2493:7;2540:1;2537;2526:16;2576:1;2573;2562:16;2597:8;2633:1;2628:3;2624:11;2663:1;2658:3;2654:11;2710:3;2706:2;2702:12;2697:3;2694:21;2689:2;2685;2681:11;2677:39;2674:65;;;2719:18;;:::i;:::-;-1:-1:-1;;2798:1:78;2789:11;;2816;;;2838:13;;;2829:23;;2812:41;2809:67;;;2856:18;;:::i;:::-;2904:1;2899:3;2895:11;2885:21;;2953:3;2949:2;2944:13;2939:3;2935:23;2930:2;2926;2922:11;2918:41;2915:67;;;2962:18;;:::i;:::-;3029:3;3025:2;3020:13;3015:3;3011:23;3006:2;3002;2998:11;2994:41;2991:67;;;3038:18;;:::i;:::-;-1:-1:-1;;;3078:13:78;;;;;2455:642;-1:-1:-1;;;;;2455:642:78:o", "linkReferences": {}}, "methodIdentifiers": {"getIndices(int24,int24)": "8bea9620"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"int24\",\"name\":\"tick\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"tickSpacing\",\"type\":\"int24\"}],\"name\":\"getIndices\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"tickIndex\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"wordIndex\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"blockIndex\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/TickMap.sol\":\"TickMap\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/structs/LimitPoolStructs.sol\":{\"keccak256\":\"0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef\",\"dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/libraries/TickMap.sol\":{\"keccak256\":\"0xa77a5fb9acdce8ed769375832ba046cd4173eb47112b3083021b1637df903555\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://71ed48dae8ec94546989456c1d2dff0ff412db9d364d2996dc2f5877a1c82c64\",\"dweb:/ipfs/QmStzY8Q86bF8A7SLasHtuKF3B6MNzruBidc8YhU1yLwsK\"]},\"contracts/libraries/math/ConstantProduct.sol\":{\"keccak256\":\"0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8\",\"dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED\"]},\"contracts/libraries/math/OverflowMath.sol\":{\"keccak256\":\"0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29\",\"dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "int24", "name": "tick", "type": "int24"}, {"internalType": "int24", "name": "tickSpacing", "type": "int24"}], "stateMutability": "pure", "type": "function", "name": "getIndices", "outputs": [{"internalType": "uint256", "name": "tickIndex", "type": "uint256"}, {"internalType": "uint256", "name": "wordIndex", "type": "uint256"}, {"internalType": "uint256", "name": "blockIndex", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/TickMap.sol": "TickMap"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/LimitPoolStructs.sol": {"keccak256": "0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4", "urls": ["bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef", "dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/libraries/TickMap.sol": {"keccak256": "0xa77a5fb9acdce8ed769375832ba046cd4173eb47112b3083021b1637df903555", "urls": ["bzz-raw://71ed48dae8ec94546989456c1d2dff0ff412db9d364d2996dc2f5877a1c82c64", "dweb:/ipfs/QmStzY8Q86bF8A7SLasHtuKF3B6MNzruBidc8YhU1yLwsK"], "license": "GPLv3"}, "contracts/libraries/math/ConstantProduct.sol": {"keccak256": "0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3", "urls": ["bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8", "dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED"], "license": "GPL-3.0-or-later"}, "contracts/libraries/math/OverflowMath.sol": {"keccak256": "0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b", "urls": ["bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29", "dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T"], "license": "MIT"}}, "version": 1}, "id": 39}