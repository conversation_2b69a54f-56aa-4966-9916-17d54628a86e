{"abi": [{"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"Contract module that helps prevent reentrant calls to a function. Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier available, which can be applied to functions to make sure there are no nested (reentrant) calls to them. Note that because there is a single `nonReentrant` guard, functions marked as `nonReentrant` may not call one another. This can be worked around by making those functions `private`, and then adding `external` `nonReentrant` entry points to them. TIP: If you would like to learn more about reentrancy and alternative ways to protect against it, check out our blog post https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\",\"errors\":{\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/external/openzeppelin/security/ReentrancyGuard.sol\":\"ReentrancyGuard\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/external/openzeppelin/security/ReentrancyGuard.sol\":{\"keccak256\":\"0x8f3450fd8545027688ff962487f30bf52c25cc5412eff92bd17595e112febfcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://414839b8ec460a9f910f0c5db5a2c487bcdec7b361ea3f80010055014f7f7d12\",\"dweb:/ipfs/QmfAbDJrAsgafqDPciKizBZFgaHujSKD7XmwD5s3zP8S3E\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/external/openzeppelin/security/ReentrancyGuard.sol": "Reentrancy<PERSON><PERSON>"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/external/openzeppelin/security/ReentrancyGuard.sol": {"keccak256": "0x8f3450fd8545027688ff962487f30bf52c25cc5412eff92bd17595e112febfcc", "urls": ["bzz-raw://414839b8ec460a9f910f0c5db5a2c487bcdec7b361ea3f80010055014f7f7d12", "dweb:/ipfs/QmfAbDJrAsgafqDPciKizBZFgaHujSKD7XmwD5s3zP8S3E"], "license": "MIT"}}, "version": 1}, "id": 14}