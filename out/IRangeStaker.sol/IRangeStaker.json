{"abi": [{"type": "function", "name": "stakeRange", "inputs": [{"name": "", "type": "tuple", "internalType": "struct PoolsharkStructs.StakeRangeParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "pool", "type": "address", "internalType": "address"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}]}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"stakeRange((address,address,uint32))": "0d55b879"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"}],\"internalType\":\"struct PoolsharkStructs.StakeRangeParams\",\"name\":\"\",\"type\":\"tuple\"}],\"name\":\"stakeRange\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/staking/IRangeStaker.sol\":\"IRangeStaker\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/staking/IRangeStaker.sol\":{\"keccak256\":\"0x3a280bb26c22584e1a528c3faf23f54b8555c61a8f75a2e5180bd6cc477a47f7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f391f83d817d5f26a9cda93f7782da134f4a02777b7e00e105f0b340553558f6\",\"dweb:/ipfs/Qmak2UUjairzcYKBYEi1xtUMb8oCVNvPYwe5QabJ7sSzXk\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct PoolsharkStructs.StakeRangeParams", "name": "", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}]}], "stateMutability": "nonpayable", "type": "function", "name": "stakeRange"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/staking/IRangeStaker.sol": "IRangeStaker"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/staking/IRangeStaker.sol": {"keccak256": "0x3a280bb26c22584e1a528c3faf23f54b8555c61a8f75a2e5180bd6cc477a47f7", "urls": ["bzz-raw://f391f83d817d5f26a9cda93f7782da134f4a02777b7e00e105f0b340553558f6", "dweb:/ipfs/Qmak2UUjairzcYKBYEi1xtUMb8oCVNvPYwe5QabJ7sSzXk"], "license": "MIT"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}}, "version": 1}, "id": 34}