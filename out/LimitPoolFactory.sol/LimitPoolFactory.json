{"abi": [{"type": "constructor", "inputs": [{"name": "owner_", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "createLimitPool", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitPoolParams", "components": [{"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "startPrice", "type": "uint160", "internalType": "uint160"}, {"name": "swapFee", "type": "uint16", "internalType": "uint16"}, {"name": "poolTypeId", "type": "uint16", "internalType": "uint16"}]}], "outputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getLimitPool", "inputs": [{"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "swapFee", "type": "uint16", "internalType": "uint16"}, {"name": "poolTypeId", "type": "uint16", "internalType": "uint16"}], "outputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "original", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pools", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "PoolCreated", "inputs": [{"name": "pool", "type": "address", "indexed": false, "internalType": "address"}, {"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "token0", "type": "address", "indexed": true, "internalType": "address"}, {"name": "token1", "type": "address", "indexed": true, "internalType": "address"}, {"name": "swapFee", "type": "uint16", "indexed": true, "internalType": "uint16"}, {"name": "tickSpacing", "type": "int16", "indexed": false, "internalType": "int16"}, {"name": "poolTypeId", "type": "uint16", "indexed": false, "internalType": "uint16"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "632:4941:1:-:0;;;903:107;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;955:14:1;;;998:4;979:24;;632:4941;;14:290:78;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;179:16;;-1:-1:-1;;;;;224:31:78;;214:42;;204:70;;270:1;267;260:12;204:70;293:5;14:290;-1:-1:-1;;;14:290:78:o;:::-;632:4941:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "632:4941:1:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4178:1393;;;;;;:::i;:::-;;:::i;:::-;;;;-1:-1:-1;;;;;1082:15:78;;;1064:34;;1134:15;;;;1129:2;1114:18;;1107:43;999:18;4178:1393:1;;;;;;;;863:33;;;;;;;;-1:-1:-1;;;;;1325:32:78;;;1307:51;;1295:2;1280:18;863:33:1;1161:203:78;827:30:1;;;;;1016:3156;;;;;;:::i;:::-;;:::i;110:40:9:-;;;;;;:::i;:::-;;;;;;;;;;;;;-1:-1:-1;;;;;110:40:9;;;4178:1393:1;4347:12;4369:17;4455:14;4482:8;-1:-1:-1;;;;;4472:18:1;:7;-1:-1:-1;;;;;4472:18:1;;:39;;4503:8;4472:39;;;4493:7;4472:39;4455:56;;4521:14;4548:8;-1:-1:-1;;;;;4538:18:1;:7;-1:-1:-1;;;;;4538:18:1;;:39;;4570:7;4538:39;;;4559:8;4538:39;4651:42;;-1:-1:-1;;;4651:42:1;;2802:6:78;2790:19;;4651:42:1;;;2772:38:78;4521:56:1;;-1:-1:-1;4631:17:1;;-1:-1:-1;;;;;4669:5:1;4651:33;;;;2745:18:78;;4651:42:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4631:62;;4707:11;:16;;4722:1;4707:16;4703:61;;4725:39;;-1:-1:-1;;;4725:39:1;;3305:2:78;4725:39:1;;;3287:21:78;3344:2;3324:18;;;3317:30;-1:-1:-1;;;3363:18:78;;;3356:51;3424:18;;4725:39:1;;;;;;;;;4890:46;;-1:-1:-1;;;4890:46:1;;2802:6:78;2790:19;;4890:46:1;;;2772:38:78;4829:16:1;;;;-1:-1:-1;;;;;4908:5:1;4890:34;;;;2745:18:78;;4890:46:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4815:121;;-1:-1:-1;4815:121:1;-1:-1:-1;;;;;;4950:22:1;;;;:49;;-1:-1:-1;;;;;;4976:23:1;;;4950:49;4946:95;;;5001:40;;-1:-1:-1;;;5001:40:1;;4045:2:78;5001:40:1;;;4027:21:78;4084:2;4064:18;;;4057:30;-1:-1:-1;;;4103:18:78;;;4096:52;4165:18;;5001:40:1;3843:346:78;5001:40:1;5109:103;;;-1:-1:-1;;;;;4479:15:78;;;5109:103:1;;;4461:34:78;4531:15;;;4511:18;;;4504:43;;;;4583:15;;;4563:18;;;4556:43;4647:6;4635:19;;4615:18;;;4608:47;5085:11:1;;4395:19:78;;5109:103:1;;;-1:-1:-1;;5109:103:1;;;;;;;;;5099:114;;5109:103;5099:114;;;;5231:5;:10;;;;;;;;;;-1:-1:-1;;;;;5231:10:1;;-1:-1:-1;5099:114:1;-1:-1:-1;5264:265:1;5314:9;5371:35;5391:6;5399;5371:19;:35::i;:::-;5424:37;5446:6;5454;5424:21;:37::i;:::-;5337:138;;;;;;4823:19:78;;;;4858:12;;4851:28;4895:12;;5337:138:1;;;;;;;;;;;;5489:3;5514:4;5264:36;:265::i;:::-;5252:277;;5540:24;;;;;;4178:1393;;;;;;;:::o;1016:3156::-;1120:12;1142:17;1229:6;:15;;;-1:-1:-1;;;;;1211:33:1;:6;:14;;;-1:-1:-1;;;;;1211:33:1;;:65;;;-1:-1:-1;1248:14:1;;-1:-1:-1;;;;;1248:28:1;;1211:65;:98;;;-1:-1:-1;1280:15:1;;;;-1:-1:-1;;;;;1280:29:1;;1211:98;1207:168;;;1325:39;;-1:-1:-1;;;1325:39:1;;5120:2:78;1325:39:1;;;5102:21:78;5159:2;5139:18;;;5132:30;-1:-1:-1;;;5178:18:78;;;5171:51;5239:18;;1325:39:1;4918:345:78;1325:39:1;1419:32;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1419:32:1;1517:6;:15;;;-1:-1:-1;;;;;1500:32:1;:6;:14;;;-1:-1:-1;;;;;1500:32:1;;:186;;1654:15;;;;1671:14;;1500:186;;;1536:14;;1553:15;;;;1500:186;-1:-1:-1;;;;;1461:225:1;;;1480:16;;;1461:225;;;;1462:16;;;1461:225;1760:14;;;;;1740:34;;;;:17;;;:34;1842:14;;1808:49;;-1:-1:-1;;;1808:49:1;;2790:19:78;;1808:49:1;;;2772:38:78;1826:5:1;1808:33;;;;;;2745:18:78;;1808:49:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1784:73;;:21;;;:73;;;1896:1;1871:26;1867:71;;1899:39;;-1:-1:-1;;;1899:39:1;;3305:2:78;1899:39:1;;;3287:21:78;3344:2;3324:18;;;3317:30;-1:-1:-1;;;3363:18:78;;;3356:51;3424:18;;1899:39:1;3103:345:78;1899:39:1;2099:17;;;;2064:53;;-1:-1:-1;;;2064:53:1;;2802:6:78;2790:19;;;2064:53:1;;;2772:38:78;2003:16:1;;;;-1:-1:-1;;;;;2082:5:1;2064:34;;;;2745:18:78;;2064:53:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1989:128;;-1:-1:-1;1989:128:1;-1:-1:-1;;;;;;2131:22:1;;;;:49;;-1:-1:-1;;;;;;2157:23:1;;;2131:49;2127:95;;;2182:40;;-1:-1:-1;;;2182:40:1;;4045:2:78;2182:40:1;;;4027:21:78;4084:2;4064:18;;;4057:30;-1:-1:-1;;;4103:18:78;;;4096:52;4165:18;;2182:40:1;3843:346:78;2182:40:1;2266:11;2314:8;2336:9;:16;;;2366:9;:16;;;2396:9;:17;;;2290:133;;;;;;;;;;-1:-1:-1;;;;;4479:15:78;;;4461:34;;4531:15;;;4526:2;4511:18;;4504:43;4583:15;;4578:2;4563:18;;4556:43;4647:6;4635:19;;;;4630:2;4615:18;;4608:47;4410:3;4395:19;;4194:467;2290:133:1;;;;-1:-1:-1;;2290:133:1;;;;;;;;;2280:144;;2290:133;2280:144;;;;2501:1;2479:10;;;;;;;;;;2280:144;;-1:-1:-1;;;;;;2479:10:1;:24;2475:67;;2505:37;;-1:-1:-1;;;2505:37:1;;5470:2:78;2505:37:1;;;5452:21:78;5509:2;5489:18;;;5482:30;-1:-1:-1;;;5528:18:78;;;5521:49;5587:18;;2505:37:1;5268:343:78;2505:37:1;-1:-1:-1;;;;;2597:5:1;2579:23;;;;2632:8;2612:28;:17;;;:28;2674:26;:15;:24;:26::i;:::-;2650:50;;:21;;;:50;2828:21;;;;2791:59;;-1:-1:-1;;;2791:59:1;;5787:1:78;5776:21;;;;2791:59:1;;;5758:40:78;-1:-1:-1;;;;;2791:36:1;;;;;5731:18:78;;2791:59:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2724:16;;;;-1:-1:-1;;;;;2710:140:1;;;2758:20;;;2710:140;;;;;3108:16;;;;3126;;;;2982:261;;3088:55;;:19;:55::i;:::-;3161:57;3183:9;:16;;;3201:9;:16;;;3161:21;:57::i;:::-;3054:178;;;;;;4823:19:78;;;;4858:12;;4851:28;4895:12;;3054:178:1;;;-1:-1:-1;;3054:178:1;;;;;;;;;-1:-1:-1;;;;;2982:28:1;;;3031:3;2982:28;:261::i;:::-;-1:-1:-1;;;;;2960:283:1;:19;;;:283;;;3389:15;;3422:16;;;;3456;;;;3527;;;;:20;;3565;;;;;3603:21;;;;3642;;;;3681:17;;;;3355:357;;3284:439;;3355:357;;3389:15;3422:16;3456;2960:283;;3527:20;3565;3603:21;3642;3681:17;3355:357;6718:2:78;6745:15;;;-1:-1:-1;;6741:24:78;;;6729:37;;6800:15;;;6796:24;;6791:2;6782:12;;6775:46;6855:15;;;6851:24;;6846:2;6837:12;;6830:46;6910:15;;;6906:24;;6901:2;6892:12;;6885:46;6965:15;;;6961:24;;6956:2;6947:12;;6940:46;7021:15;;7017:24;;;7011:3;7002:13;;6995:47;7099:3;7077:16;;;;-1:-1:-1;;;;;;7073:43:78;7067:3;7058:13;;7051:66;7152:3;7148:16;;;7142:3;7133:13;;7126:39;6268:15;;;-1:-1:-1;;;;;;6264:37:78;7209:3;7200:13;;6252:50;7239:3;7230:13;;6313:936;3355:357:1;;;;-1:-1:-1;;3355:357:1;;;;;;;;;-1:-1:-1;;;;;3284:27:1;;;3332:3;3284:27;:439::i;:::-;3797:17;;;;;3769:46;;-1:-1:-1;;;3769:46:1;;-1:-1:-1;;;;;1325:32:78;;;3769:46:1;;;1307:51:78;3277:446:1;;-1:-1:-1;3769:27:1;;;;;1280:18:78;;3769:46:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3871:4;3858:5;:10;3864:3;3858:10;;;;;;;;;;;;:17;;;;;-1:-1:-1;;;;;3858:17:1;;;;;-1:-1:-1;;;;;3858:17:1;;;;;;4027:9;:17;;;3891:229;;3997:9;:16;;;-1:-1:-1;;;;;3891:229:1;3967:9;:16;;;-1:-1:-1;;;;;3891:229:1;;3916:4;3934:9;:19;;;4058:9;:21;;;4093:6;:17;;;3891:229;;;;;;;;-1:-1:-1;;;;;7743:15:78;;;7725:34;;7795:15;;;;7790:2;7775:18;;7768:43;7714:1;7847:21;7842:2;7827:18;;7820:49;7917:6;7905:19;;;;7900:2;7885:18;;7878:47;7674:3;7659:19;;7462:469;3891:229:1;;;;;;;;-1:-1:-1;;;4145:19:1;;;4139:4;;4145:19;;-1:-1:-1;;1016:3156:1:o;785:297:62:-;854:14;880:24;966:6;-1:-1:-1;;;;;960:20:62;;:22;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;960:22:62;;;;;;;;;;;;:::i;:::-;1007:6;-1:-1:-1;;;;;1001:20:62;;:22;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1001:22:62;;;;;;;;;;;;:::i;:::-;907:126;;;;;;;;;:::i;:::-;;;;;;;;;;;;;880:153;;1053:22;1064:10;1053;:22::i;:::-;1044:31;785:297;-1:-1:-1;;;;785:297:62:o;1088:300::-;1159:14;1185:26;1270:6;-1:-1:-1;;;;;1264:20:62;;:22;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1264:22:62;;;;;;;;;;;;:::i;:::-;1311:6;-1:-1:-1;;;;;1305:20:62;;:22;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1305:22:62;;;;;;;;;;;;:::i;:::-;1214:123;;;;;;;;;:::i;6482:329:16:-;-1:-1:-1;;4679:15:16;;4673:22;;-1:-1:-1;;4730:15:16;;4724:22;;-1:-1:-1;;4781:15:16;;4775:22;;4828:11;;4867:32;;;4791:4;4867:32;4927:14;;5151:28;5138:42;;-1:-1:-1;;5256:15:16;;5249:39;;;5071:1;5055:18;;5412:4;5408:22;;;5432:52;5405:80;-1:-1:-1;;5372:15:16;;5348:151;5603:66;-1:-1:-1;;5586:15:16;;5562:121;5766:22;;;5760:4;5756:33;5791:38;5753:77;-1:-1:-1;;5720:15:16;;5696:148;5877:4;5873:22;5857:39;;5997:22;;;-1:-1:-1;;5980:15:16;;5970:50;6100:24;;6137;;;6174:33;;6220;;6266;;-1:-1:-1;;6755:49:16;6783:4;6789;6795:8;6755:27;:49::i;:::-;6743:61;6482:329;-1:-1:-1;;;;;;6482:329:16:o;1344:148:63:-;1438:1;1419:20;;;;;1416:69;;1441:44;;-1:-1:-1;;;1441:44:63;;10764:2:78;1441:44:63;;;10746:21:78;10803:2;10783:18;;;10776:30;10842:28;10822:18;;;10815:56;10888:18;;1441:44:63;10562:350:78;1441:44:63;1344:148;;;:::o;1998:2205:16:-;2117:16;2292:4;2286;2282:15;2276:22;2343:4;2337;2333:15;2327:22;2394:4;2388;2384:15;2378:22;2437:4;2431:11;2491:10;2484:4;2478;2474:15;2470:32;2536:7;2530:14;2674:1;2662:10;2658:18;2754:28;2748:4;2741:42;2876:14;2869:4;2863;2859:15;2852:39;3035:52;3021:11;3015:4;3011:22;3008:80;2985:4;2979;2975:15;2951:151;3206:66;3199:4;3193;3189:15;3165:121;3394:38;3386:4;3373:11;3369:22;3363:4;3359:33;3356:77;3333:4;3327;3323:15;3299:148;3486:11;3480:4;3476:22;3467:7;3460:39;3613:4;3606;3593:11;3589:22;3582:4;3576;3572:15;3569:1;3561:57;3549:69;;;3688:8;3678:230;;3801:10;3795:4;3788:24;3889:4;3883;3876:18;3678:230;3988:24;;4025;;-1:-1:-1;;4069:15:16;;4062:33;-1:-1:-1;;4115:15:16;;4108:33;-1:-1:-1;;4161:15:16;;;4154:33;;;;-1:-1:-1;1998:2205:16;;-1:-1:-1;1998:2205:16:o;138:296:60:-;285:26;;197:14;;264:6;;285:31;;281:72;;-1:-1:-1;339:3:60;;138:296;-1:-1:-1;;138:296:60:o;281:72::-;-1:-1:-1;;414:2:60;402:15;396:22;;138:296::o;7236:604:16:-;7366:17;7531:4;7525;7517:19;-1:-1:-1;7577:4:16;7570:18;;;7618:2;7614:17;7608:4;7601:31;7652:4;7645:18;7705:4;7699;7689:21;;;7809:15;;7689:21;7236:604::o;14:131:78:-;-1:-1:-1;;;;;89:31:78;;79:42;;69:70;;135:1;132;125:12;69:70;14:131;:::o;150:159::-;217:20;;277:6;266:18;;256:29;;246:57;;299:1;296;289:12;314:533;398:6;406;414;422;475:3;463:9;454:7;450:23;446:33;443:53;;;492:1;489;482:12;443:53;531:9;518:23;550:31;575:5;550:31;:::i;:::-;600:5;-1:-1:-1;657:2:78;642:18;;629:32;670:33;629:32;670:33;:::i;:::-;722:7;-1:-1:-1;748:37:78;781:2;766:18;;748:37;:::i;:::-;738:47;;804:37;837:2;826:9;822:18;804:37;:::i;:::-;794:47;;314:533;;;;;;;:::o;1369:127::-;1430:10;1425:3;1421:20;1418:1;1411:31;1461:4;1458:1;1451:15;1485:4;1482:1;1475:15;1501:937;1593:6;1646:3;1634:9;1625:7;1621:23;1617:33;1614:53;;;1663:1;1660;1653:12;1614:53;1696:2;1690:9;1738:3;1730:6;1726:16;1808:6;1796:10;1793:22;1772:18;1760:10;1757:34;1754:62;1751:88;;;1819:18;;:::i;:::-;1855:2;1848:22;1892:23;;1924:31;1892:23;1924:31;:::i;:::-;1964:21;;2037:2;2022:18;;2009:32;2050:33;2009:32;2050:33;:::i;:::-;2111:2;2099:15;;2092:32;2176:2;2161:18;;2148:32;2189:33;2148:32;2189:33;:::i;:::-;2250:2;2238:15;;2231:32;2296:37;2329:2;2314:18;;2296:37;:::i;:::-;2291:2;2283:6;2279:15;2272:62;2368:38;2401:3;2390:9;2386:19;2368:38;:::i;:::-;2362:3;2350:16;;2343:64;2354:6;1501:937;-1:-1:-1;;;1501:937:78:o;2443:180::-;2502:6;2555:2;2543:9;2534:7;2530:23;2526:32;2523:52;;;2571:1;2568;2561:12;2523:52;-1:-1:-1;2594:23:78;;2443:180;-1:-1:-1;2443:180:78:o;2821:277::-;2889:6;2942:2;2930:9;2921:7;2917:23;2913:32;2910:52;;;2958:1;2955;2948:12;2910:52;2990:9;2984:16;3043:5;3040:1;3029:20;3022:5;3019:31;3009:59;;3064:1;3061;3054:12;3009:59;3087:5;2821:277;-1:-1:-1;;;2821:277:78:o;3453:385::-;3532:6;3540;3593:2;3581:9;3572:7;3568:23;3564:32;3561:52;;;3609:1;3606;3599:12;3561:52;3641:9;3635:16;3660:31;3685:5;3660:31;:::i;:::-;3760:2;3745:18;;3739:25;3710:5;;-1:-1:-1;3773:33:78;3739:25;3773:33;:::i;:::-;3825:7;3815:17;;;3453:385;;;;;:::o;7936:258::-;8008:1;8018:113;8032:6;8029:1;8026:13;8018:113;;;8108:11;;;8102:18;8089:11;;;8082:39;8054:2;8047:10;8018:113;;;8149:6;8146:1;8143:13;8140:48;;;8184:1;8175:6;8170:3;8166:16;8159:27;8140:48;;7936:258;;;:::o;8199:884::-;8279:6;8332:2;8320:9;8311:7;8307:23;8303:32;8300:52;;;8348:1;8345;8338:12;8300:52;8381:9;8375:16;8410:18;8451:2;8443:6;8440:14;8437:34;;;8467:1;8464;8457:12;8437:34;8505:6;8494:9;8490:22;8480:32;;8550:7;8543:4;8539:2;8535:13;8531:27;8521:55;;8572:1;8569;8562:12;8521:55;8601:2;8595:9;8623:2;8619;8616:10;8613:36;;;8629:18;;:::i;:::-;8704:2;8698:9;8672:2;8758:13;;-1:-1:-1;;8754:22:78;;;8778:2;8750:31;8746:40;8734:53;;;8802:18;;;8822:22;;;8799:46;8796:72;;;8848:18;;:::i;:::-;8888:10;8884:2;8877:22;8923:2;8915:6;8908:18;8963:7;8958:2;8953;8949;8945:11;8941:20;8938:33;8935:53;;;8984:1;8981;8974:12;8935:53;8997:55;9049:2;9044;9036:6;9032:15;9027:2;9023;9019:11;8997:55;:::i;:::-;9071:6;8199:884;-1:-1:-1;;;;;;;8199:884:78:o;9088:736::-;-1:-1:-1;;;9473:3:78;9466:25;9448:3;9520:6;9514:13;9536:62;9591:6;9586:2;9581:3;9577:12;9570:4;9562:6;9558:17;9536:62;:::i;:::-;-1:-1:-1;;;9657:2:78;9617:16;;;9649:11;;;9642:24;9691:13;;9713:63;9691:13;9762:2;9754:11;;9747:4;9735:17;;9713:63;:::i;:::-;9796:17;9815:2;9792:26;;9088:736;-1:-1:-1;;;;9088:736:78:o;9829:728::-;-1:-1:-1;;;10213:3:78;10206:22;10188:3;10257:6;10251:13;10273:61;10327:6;10323:1;10318:3;10314:11;10307:4;10299:6;10295:17;10273:61;:::i;:::-;-1:-1:-1;;;10393:1:78;10353:16;;;10385:10;;;10378:23;10426:13;;10448:62;10426:13;10497:1;10489:10;;10482:4;10470:17;;10448:62;:::i;:::-;10530:17;10549:1;10526:25;;9829:728;-1:-1:-1;;;;9829:728:78:o", "linkReferences": {}, "immutableReferences": {"612": [{"start": 216, "length": 32}, {"start": 424, "length": 32}, {"start": 651, "length": 32}, {"start": 1407, "length": 32}, {"start": 1642, "length": 32}, {"start": 2056, "length": 32}], "614": [{"start": 153, "length": 32}, {"start": 2093, "length": 32}]}}, "methodIdentifiers": {"createLimitPool((address,address,uint160,uint16,uint16))": "aa5c269b", "getLimitPool(address,address,uint16,uint16)": "2f6fce98", "original()": "46c715fa", "owner()": "8da5cb5b", "pools(bytes32)": "b5217bb4"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner_\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"poolTypeId\",\"type\":\"uint16\"}],\"name\":\"PoolCreated\",\"type\":\"event\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"tokenIn\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenOut\",\"type\":\"address\"},{\"internalType\":\"uint160\",\"name\":\"startPrice\",\"type\":\"uint160\"},{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"poolTypeId\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.LimitPoolParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"createLimitPool\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenIn\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenOut\",\"type\":\"address\"},{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"poolTypeId\",\"type\":\"uint16\"}],\"name\":\"getLimitPool\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"original\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"pools\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/LimitPoolFactory.sol\":\"LimitPoolFactory\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/LimitPoolFactory.sol\":{\"keccak256\":\"0x2301fa5115dbd6d6a6ef16849dd1cac16f134060e31ac1b63380079c89760658\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://defdb771cbda7556097677bc5154b1a33ad5a982b5c926837a92ade390091462\",\"dweb:/ipfs/QmZAMnBVyAkArLerH67afJVgHeVhaZsQKDjp2J5QddPsb1\"]},\"contracts/base/events/LimitPoolFactoryEvents.sol\":{\"keccak256\":\"0x35cab619b1e4aabd39e6469fed087e07da1c44c9eb527508d11217b4edc396ea\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://b4a6de023a8d68691505f42745947e5aa833a4b5a20c53e7dfea5615db25e9ad\",\"dweb:/ipfs/QmWQ4CxBiWiX6SZMZ5Cefuhw69ANYddsmwgia2vpaBAQke\"]},\"contracts/base/storage/LimitPoolFactoryStorage.sol\":{\"keccak256\":\"0xec8efbb85bf6d831dc0c3910eccbe1b6def550ac0b88eef62b4a7c0c314a31ae\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://363a51daf8ce8d46c62bb8d9821b99ea2ec7d4f81a0512f5b83901d3a9a4631f\",\"dweb:/ipfs/QmeKyWJwsX9LwB5A8ovGm7J2nToJvR1dnE3sMww46ERb55\"]},\"contracts/external/solady/LibClone.sol\":{\"keccak256\":\"0x93750a76e235631c1438283750f8b096026a11d82399fdc002816c55acc1f55a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e99db3fbe71ac90a31b411481b148d17ef2cb6c748d38216ae233778d0228d4d\",\"dweb:/ipfs/QmWkt4Q5fQkMkq8a3Vf2KjKBqkAvqzpGFLy1rFvWzyYN5k\"]},\"contracts/interfaces/IPositionERC1155.sol\":{\"keccak256\":\"0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11\",\"dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe\"]},\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/limit/ILimitPool.sol\":{\"keccak256\":\"0x8754512ae636a8871b11412ad16735be460142a48ee9f0fc50310f4d4aa45227\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://9621227f4ab660043534cb124559d96d42cefad3441b61a7a82be81ef8674dcf\",\"dweb:/ipfs/QmYZsFqXwFW6dcYSrjc4R1Lu1r9MbMbo6EQH6Rk2yqztKA\"]},\"contracts/interfaces/limit/ILimitPoolFactory.sol\":{\"keccak256\":\"0x89fc412319c30451896a1db8d77bb528c6df383523cf7f278534a645a4a49733\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://495509c3a546e0b85b34a8ef7fc114ca5bb98d831c8bbd176b42d36703f00fd0\",\"dweb:/ipfs/QmcFR6a32Fp7s4hRDmJfp7GaC65uQUtRo6VhE6ygCpZefm\"]},\"contracts/interfaces/limit/ILimitPoolManager.sol\":{\"keccak256\":\"0x3569f41d49e23238d34b3dc91e7fe7587c8fa141cacc97bf382caa1b580837de\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://aa5545c53994d586c1e7e6aaeb7d0522a678ae012867268e93e3f65060e7360f\",\"dweb:/ipfs/QmU34ZDnYxCDV6i8fxq13AU5LptW3rXgY5ZYpLy2t8sLqk\"]},\"contracts/interfaces/limit/ILimitPoolView.sol\":{\"keccak256\":\"0x298606a582d43209c74b9abf469e3576876444b964ce02be0b5d52d8662fd88a\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://adad5cd4a5cbf1c874e8351def6f31877d305e28b6aaf6d9dc539c9d7417c420\",\"dweb:/ipfs/QmZTbR2jJzQe2T6GpDoGLPD5gAgLks6XFqzPewSUR1kBpw\"]},\"contracts/interfaces/range/IRangePool.sol\":{\"keccak256\":\"0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab\",\"dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt\"]},\"contracts/interfaces/range/IRangePoolFactory.sol\":{\"keccak256\":\"0xa38c314350de1c59bd90e521fe0920d1e5008009aca592697b07194346da8cc1\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://53551b95615f6a6259753c7e9fc189a46dfe971b83cb417af4d4c593278f9d60\",\"dweb:/ipfs/QmXFnKtuRVJgs9HbNaNZna7mkmBRhPxtiVfkJKWuqytscS\"]},\"contracts/interfaces/range/IRangePoolManager.sol\":{\"keccak256\":\"0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065\",\"dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP\"]},\"contracts/interfaces/structs/LimitPoolStructs.sol\":{\"keccak256\":\"0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef\",\"dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/interfaces/structs/RangePoolStructs.sol\":{\"keccak256\":\"0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9\",\"dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx\"]},\"contracts/libraries/math/ConstantProduct.sol\":{\"keccak256\":\"0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8\",\"dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED\"]},\"contracts/libraries/math/OverflowMath.sol\":{\"keccak256\":\"0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29\",\"dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T\"]},\"contracts/libraries/utils/Bytes.sol\":{\"keccak256\":\"0x461969264908704100e3195ed51ca299ddcfa8bafcd8c2bf55e159e44d4b6b73\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ded39fcbe8dd11f7ba5aa5bb319a733529076c03a2d332812913fbab4dbecc00\",\"dweb:/ipfs/QmeZmu4exQa8hSbfEabw3hxcS9n1qNf8Uiun5hxao9GZT9\"]},\"contracts/libraries/utils/PositionTokens.sol\":{\"keccak256\":\"0xf08d7ba95f79d677e2b4e32abc41c0f86ca11fbf6d056d1de06fe5c3ebdc9960\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://fabb91eb2b0f6823b48bdb245f9ac650958b526179e86b44683ba7b691843d79\",\"dweb:/ipfs/QmWsGVaBvQ3RFwu94BvAebRHP1NCzJ5dLfX7VTeWXjG4if\"]},\"contracts/libraries/utils/SafeCast.sol\":{\"keccak256\":\"0xc4ec846404c1111c6353c8b2251469e07f32570b1ffafc58701c385c458c21e2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://51b7397ff450c2aedafdcb40a6ca181c8e1947fb409dbbcd8b000b8eee2e9fcf\",\"dweb:/ipfs/QmQ3wPdCtuLreoEkvFoifsx4drC5kT3p2CwGKBBn2qQso4\"]},\"contracts/libraries/utils/String.sol\":{\"keccak256\":\"0x0d03855bfeabee266fa8252cbcac9bab81b5a951d3c0a99ae51f2e4a1942dbf2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2fba9aae08e1406ee8c7c21da98bd72c4f34e55323ffbb0146e9ec8f0a4a06e3\",\"dweb:/ipfs/QmbT189JtHc8esSwJ9TCkLhYVBVguJr2noepxKh5XrjEfj\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0\",\"dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34\",\"dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd\",\"dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92\",\"dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f\",\"dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "owner_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "pool", "type": "address", "indexed": false}, {"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "address", "name": "token0", "type": "address", "indexed": true}, {"internalType": "address", "name": "token1", "type": "address", "indexed": true}, {"internalType": "uint16", "name": "swapFee", "type": "uint16", "indexed": true}, {"internalType": "int16", "name": "tickSpacing", "type": "int16", "indexed": false}, {"internalType": "uint16", "name": "poolTypeId", "type": "uint16", "indexed": false}], "type": "event", "name": "PoolCreated", "anonymous": false}, {"inputs": [{"internalType": "struct PoolsharkStructs.LimitPoolParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "address", "name": "tokenOut", "type": "address"}, {"internalType": "uint160", "name": "startPrice", "type": "uint160"}, {"internalType": "uint16", "name": "swapFee", "type": "uint16"}, {"internalType": "uint16", "name": "poolTypeId", "type": "uint16"}]}], "stateMutability": "nonpayable", "type": "function", "name": "createLimitPool", "outputs": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "address", "name": "tokenOut", "type": "address"}, {"internalType": "uint16", "name": "swapFee", "type": "uint16"}, {"internalType": "uint16", "name": "poolTypeId", "type": "uint16"}], "stateMutability": "view", "type": "function", "name": "getLimitPool", "outputs": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "original", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "pools", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/LimitPoolFactory.sol": "LimitPoolFactory"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/LimitPoolFactory.sol": {"keccak256": "0x2301fa5115dbd6d6a6ef16849dd1cac16f134060e31ac1b63380079c89760658", "urls": ["bzz-raw://defdb771cbda7556097677bc5154b1a33ad5a982b5c926837a92ade390091462", "dweb:/ipfs/QmZAMnBVyAkArLerH67afJVgHeVhaZsQKDjp2J5QddPsb1"], "license": "BUSL-1.1"}, "contracts/base/events/LimitPoolFactoryEvents.sol": {"keccak256": "0x35cab619b1e4aabd39e6469fed087e07da1c44c9eb527508d11217b4edc396ea", "urls": ["bzz-raw://b4a6de023a8d68691505f42745947e5aa833a4b5a20c53e7dfea5615db25e9ad", "dweb:/ipfs/QmWQ4CxBiWiX6SZMZ5Cefuhw69ANYddsmwgia2vpaBAQke"], "license": "GPL-3.0-or-later"}, "contracts/base/storage/LimitPoolFactoryStorage.sol": {"keccak256": "0xec8efbb85bf6d831dc0c3910eccbe1b6def550ac0b88eef62b4a7c0c314a31ae", "urls": ["bzz-raw://363a51daf8ce8d46c62bb8d9821b99ea2ec7d4f81a0512f5b83901d3a9a4631f", "dweb:/ipfs/QmeKyWJwsX9LwB5A8ovGm7J2nToJvR1dnE3sMww46ERb55"], "license": "BUSL-1.1"}, "contracts/external/solady/LibClone.sol": {"keccak256": "0x93750a76e235631c1438283750f8b096026a11d82399fdc002816c55acc1f55a", "urls": ["bzz-raw://e99db3fbe71ac90a31b411481b148d17ef2cb6c748d38216ae233778d0228d4d", "dweb:/ipfs/QmWkt4Q5fQkMkq8a3Vf2KjKBqkAvqzpGFLy1rFvWzyYN5k"], "license": "MIT"}, "contracts/interfaces/IPositionERC1155.sol": {"keccak256": "0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c", "urls": ["bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11", "dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe"], "license": "MIT"}, "contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPool.sol": {"keccak256": "0x8754512ae636a8871b11412ad16735be460142a48ee9f0fc50310f4d4aa45227", "urls": ["bzz-raw://9621227f4ab660043534cb124559d96d42cefad3441b61a7a82be81ef8674dcf", "dweb:/ipfs/QmYZsFqXwFW6dcYSrjc4R1Lu1r9MbMbo6EQH6Rk2yqztKA"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPoolFactory.sol": {"keccak256": "0x89fc412319c30451896a1db8d77bb528c6df383523cf7f278534a645a4a49733", "urls": ["bzz-raw://495509c3a546e0b85b34a8ef7fc114ca5bb98d831c8bbd176b42d36703f00fd0", "dweb:/ipfs/QmcFR6a32Fp7s4hRDmJfp7GaC65uQUtRo6VhE6ygCpZefm"], "license": "BUSL-1.1"}, "contracts/interfaces/limit/ILimitPoolManager.sol": {"keccak256": "0x3569f41d49e23238d34b3dc91e7fe7587c8fa141cacc97bf382caa1b580837de", "urls": ["bzz-raw://aa5545c53994d586c1e7e6aaeb7d0522a678ae012867268e93e3f65060e7360f", "dweb:/ipfs/QmU34ZDnYxCDV6i8fxq13AU5LptW3rXgY5ZYpLy2t8sLqk"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPoolView.sol": {"keccak256": "0x298606a582d43209c74b9abf469e3576876444b964ce02be0b5d52d8662fd88a", "urls": ["bzz-raw://adad5cd4a5cbf1c874e8351def6f31877d305e28b6aaf6d9dc539c9d7417c420", "dweb:/ipfs/QmZTbR2jJzQe2T6GpDoGLPD5gAgLks6XFqzPewSUR1kBpw"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePool.sol": {"keccak256": "0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf", "urls": ["bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab", "dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePoolFactory.sol": {"keccak256": "0xa38c314350de1c59bd90e521fe0920d1e5008009aca592697b07194346da8cc1", "urls": ["bzz-raw://53551b95615f6a6259753c7e9fc189a46dfe971b83cb417af4d4c593278f9d60", "dweb:/ipfs/QmXFnKtuRVJgs9HbNaNZna7mkmBRhPxtiVfkJKWuqytscS"], "license": "GPLv3"}, "contracts/interfaces/range/IRangePoolManager.sol": {"keccak256": "0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139", "urls": ["bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065", "dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/LimitPoolStructs.sol": {"keccak256": "0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4", "urls": ["bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef", "dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/interfaces/structs/RangePoolStructs.sol": {"keccak256": "0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd", "urls": ["bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9", "dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx"], "license": "GPLv3"}, "contracts/libraries/math/ConstantProduct.sol": {"keccak256": "0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3", "urls": ["bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8", "dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED"], "license": "GPL-3.0-or-later"}, "contracts/libraries/math/OverflowMath.sol": {"keccak256": "0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b", "urls": ["bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29", "dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T"], "license": "MIT"}, "contracts/libraries/utils/Bytes.sol": {"keccak256": "0x461969264908704100e3195ed51ca299ddcfa8bafcd8c2bf55e159e44d4b6b73", "urls": ["bzz-raw://ded39fcbe8dd11f7ba5aa5bb319a733529076c03a2d332812913fbab4dbecc00", "dweb:/ipfs/QmeZmu4exQa8hSbfEabw3hxcS9n1qNf8Uiun5hxao9GZT9"], "license": "MIT"}, "contracts/libraries/utils/PositionTokens.sol": {"keccak256": "0xf08d7ba95f79d677e2b4e32abc41c0f86ca11fbf6d056d1de06fe5c3ebdc9960", "urls": ["bzz-raw://fabb91eb2b0f6823b48bdb245f9ac650958b526179e86b44683ba7b691843d79", "dweb:/ipfs/QmWsGVaBvQ3RFwu94BvAebRHP1NCzJ5dLfX7VTeWXjG4if"], "license": "GPLv3"}, "contracts/libraries/utils/SafeCast.sol": {"keccak256": "0xc4ec846404c1111c6353c8b2251469e07f32570b1ffafc58701c385c458c21e2", "urls": ["bzz-raw://51b7397ff450c2aedafdcb40a6ca181c8e1947fb409dbbcd8b000b8eee2e9fcf", "dweb:/ipfs/QmQ3wPdCtuLreoEkvFoifsx4drC5kT3p2CwGKBBn2qQso4"], "license": "GPL-2.0-or-later"}, "contracts/libraries/utils/String.sol": {"keccak256": "0x0d03855bfeabee266fa8252cbcac9bab81b5a951d3c0a99ae51f2e4a1942dbf2", "urls": ["bzz-raw://2fba9aae08e1406ee8c7c21da98bd72c4f34e55323ffbb0146e9ec8f0a4a06e3", "dweb:/ipfs/QmbT189JtHc8esSwJ9TCkLhYVBVguJr2noepxKh5XrjEfj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238", "urls": ["bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0", "dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b", "urls": ["bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34", "dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca", "urls": ["bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd", "dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7", "urls": ["bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92", "dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1", "urls": ["bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f", "dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy"], "license": "MIT"}}, "version": 1}, "id": 1}