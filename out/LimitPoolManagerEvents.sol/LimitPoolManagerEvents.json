{"abi": [{"type": "event", "name": "FactoryChanged", "inputs": [{"name": "previousFactory", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newFactory", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "FeeTier<PERSON>nabled", "inputs": [{"name": "swapFee", "type": "uint16", "indexed": false, "internalType": "uint16"}, {"name": "tickSpacing", "type": "int16", "indexed": false, "internalType": "int16"}], "anonymous": false}, {"type": "event", "name": "FeeToTransfer", "inputs": [{"name": "previousFeeTo", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newFeeTo", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnerTransfer", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PoolTypeEnabled", "inputs": [{"name": "poolTypeName", "type": "bytes32", "indexed": false, "internalType": "bytes32"}, {"name": "poolImpl", "type": "address", "indexed": false, "internalType": "address"}, {"name": "tokenImpl", "type": "address", "indexed": false, "internalType": "address"}, {"name": "poolTypeId", "type": "uint16", "indexed": false, "internalType": "uint16"}], "anonymous": false}, {"type": "event", "name": "ProtocolFeesCollected", "inputs": [{"name": "pools", "type": "address[]", "indexed": false, "internalType": "address[]"}, {"name": "token0FeesCollected", "type": "uint128[]", "indexed": false, "internalType": "uint128[]"}, {"name": "token1FeesCollected", "type": "uint128[]", "indexed": false, "internalType": "uint128[]"}], "anonymous": false}, {"type": "event", "name": "ProtocolFillFeesModified", "inputs": [{"name": "pools", "type": "address[]", "indexed": false, "internalType": "address[]"}, {"name": "protocolFillFees0", "type": "int16[]", "indexed": false, "internalType": "int16[]"}, {"name": "protocolFillFees1", "type": "int16[]", "indexed": false, "internalType": "int16[]"}], "anonymous": false}, {"type": "event", "name": "ProtocolSwapFeesModified", "inputs": [{"name": "pools", "type": "address[]", "indexed": false, "internalType": "address[]"}, {"name": "protocolSwapFees0", "type": "int16[]", "indexed": false, "internalType": "int16[]"}, {"name": "protocolSwapFees1", "type": "int16[]", "indexed": false, "internalType": "int16[]"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousFactory\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newFactory\",\"type\":\"address\"}],\"name\":\"FactoryChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"}],\"name\":\"FeeTierEnabled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousFeeTo\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newFeeTo\",\"type\":\"address\"}],\"name\":\"FeeToTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnerTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"poolTypeName\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"tokenImpl\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"poolTypeId\",\"type\":\"uint16\"}],\"name\":\"PoolTypeEnabled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"pools\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"uint128[]\",\"name\":\"token0FeesCollected\",\"type\":\"uint128[]\"},{\"indexed\":false,\"internalType\":\"uint128[]\",\"name\":\"token1FeesCollected\",\"type\":\"uint128[]\"}],\"name\":\"ProtocolFeesCollected\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"pools\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"int16[]\",\"name\":\"protocolFillFees0\",\"type\":\"int16[]\"},{\"indexed\":false,\"internalType\":\"int16[]\",\"name\":\"protocolFillFees1\",\"type\":\"int16[]\"}],\"name\":\"ProtocolFillFeesModified\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"pools\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"int16[]\",\"name\":\"protocolSwapFees0\",\"type\":\"int16[]\"},{\"indexed\":false,\"internalType\":\"int16[]\",\"name\":\"protocolSwapFees1\",\"type\":\"int16[]\"}],\"name\":\"ProtocolSwapFeesModified\",\"type\":\"event\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/base/events/LimitPoolManagerEvents.sol\":\"LimitPoolManagerEvents\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/base/events/LimitPoolManagerEvents.sol\":{\"keccak256\":\"0x4eeb34b66fda2de8db22f7f5be0285bba160143c843672fe933d201f9fc0baed\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://9ad3bc0cffe948244c970de0ceecf1957a336c32f55c05de94058a772b2523d0\",\"dweb:/ipfs/QmRthqV8jUgN84UFKNusAFgmu6tudu2Trx4Rwn3T5gaQeG\"]},\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "previousFactory", "type": "address", "indexed": true}, {"internalType": "address", "name": "newFactory", "type": "address", "indexed": true}], "type": "event", "name": "FactoryChanged", "anonymous": false}, {"inputs": [{"internalType": "uint16", "name": "swapFee", "type": "uint16", "indexed": false}, {"internalType": "int16", "name": "tickSpacing", "type": "int16", "indexed": false}], "type": "event", "name": "FeeTier<PERSON>nabled", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousFeeTo", "type": "address", "indexed": true}, {"internalType": "address", "name": "newFeeTo", "type": "address", "indexed": true}], "type": "event", "name": "FeeToTransfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnerTransfer", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "poolTypeName", "type": "bytes32", "indexed": false}, {"internalType": "address", "name": "poolImpl", "type": "address", "indexed": false}, {"internalType": "address", "name": "tokenImpl", "type": "address", "indexed": false}, {"internalType": "uint16", "name": "poolTypeId", "type": "uint16", "indexed": false}], "type": "event", "name": "PoolTypeEnabled", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "pools", "type": "address[]", "indexed": false}, {"internalType": "uint128[]", "name": "token0FeesCollected", "type": "uint128[]", "indexed": false}, {"internalType": "uint128[]", "name": "token1FeesCollected", "type": "uint128[]", "indexed": false}], "type": "event", "name": "ProtocolFeesCollected", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "pools", "type": "address[]", "indexed": false}, {"internalType": "int16[]", "name": "protocolFillFees0", "type": "int16[]", "indexed": false}, {"internalType": "int16[]", "name": "protocolFillFees1", "type": "int16[]", "indexed": false}], "type": "event", "name": "ProtocolFillFeesModified", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "pools", "type": "address[]", "indexed": false}, {"internalType": "int16[]", "name": "protocolSwapFees0", "type": "int16[]", "indexed": false}, {"internalType": "int16[]", "name": "protocolSwapFees1", "type": "int16[]", "indexed": false}], "type": "event", "name": "ProtocolSwapFeesModified", "anonymous": false}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/base/events/LimitPoolManagerEvents.sol": "LimitPoolManagerEvents"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/base/events/LimitPoolManagerEvents.sol": {"keccak256": "0x4eeb34b66fda2de8db22f7f5be0285bba160143c843672fe933d201f9fc0baed", "urls": ["bzz-raw://9ad3bc0cffe948244c970de0ceecf1957a336c32f55c05de94058a772b2523d0", "dweb:/ipfs/QmRthqV8jUgN84UFKNusAFgmu6tudu2Trx4Rwn3T5gaQeG"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}}, "version": 1}, "id": 5}