{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "testAffectedFunctions", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testEthConsumptionScenario", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testMsgValueRemainsConstantInLoop", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "87:2588:19:-:0;;;3126:44:2;;;3166:4;-1:-1:-1;;3126:44:2;;;;;;;;1065:26:13;;;;;;;;;;;87:2588:19;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561001057600080fd5b50600436106100f55760003560e01c806385226c8111610097578063ba414fa611610066578063ba414fa61461019e578063e20c9f71146101b6578063e70f9968146101be578063fa7626d4146101c657600080fd5b806385226c8114610164578063916a17c614610179578063b0464fdc1461018e578063b5508aa91461019657600080fd5b80633e5e3c23116100d35780633e5e3c23146101375780633f7286f41461013f57806343676ba31461014757806366d9a9a01461014f57600080fd5b80631ed7831c146100fa5780632ade38801461011857806338e3388b1461012d575b600080fd5b6101026101d3565b60405161010f91906110a1565b60405180910390f35b610120610235565b60405161010f919061113b565b610135610377565b005b6101026104c6565b610102610526565b610135610586565b61015761074d565b60405161010f9190611240565b61016c6108ba565b60405161010f91906112c5565b61018161098a565b60405161010f9190611327565b610181610a70565b61016c610b56565b6101a6610c26565b604051901515815260200161010f565b610102610cc6565b610135610d26565b601f546101a69060ff1681565b6060601680548060200260200160405190810160405280929190818152602001828054801561022b57602002820191906000526020600020905b81546001600160a01b0316815260019091019060200180831161020d575b5050505050905090565b6060601e805480602002602001604051908101604052809291908181526020016000905b8282101561036e57600084815260208082206040805180820182526002870290920180546001600160a01b03168352600181018054835181870281018701909452808452939591948681019491929084015b828210156103575783829060005260206000200180546102ca90611396565b80601f01602080910402602001604051908101604052809291908181526020018280546102f690611396565b80156103435780601f1061031857610100808354040283529160200191610343565b820191906000526020600020905b81548152906001019060200180831161032657829003601f168201915b5050505050815260200190600101906102ab565b505050508152505081526020019060010190610259565b50505050905090565b6040805160c0810182526010608082018181526f6d756c74694d696e744c696d6974282960801b60a08401528252825180840184528181526f6d756c74694d696e7452616e6765282960801b60208281019190915280840191909152835180850185529182526f6d756c74694d696e74436f766572282960801b82820152828401919091528251808401909352601883527f6372656174654c696d6974506f6f6c416e644d696e742829000000000000000090830152606081019190915260005b600481101561049f5761048d604051806040016040528060148152602001732b3ab63732b930b1363290333ab731ba34b7b71d60611b815250838360048110610483576104836113d0565b6020020151610ec9565b80610497816113fc565b915050610438565b506104c3600160405180606001604052806037815260200161158960379139610f0e565b50565b6060601880548060200260200160405190810160405280929190818152602001828054801561022b576020028201919060005260206000209081546001600160a01b0316815260019091019060200180831161020d575050505050905090565b6060601780548060200260200160405190810160405280929190818152602001828054801561022b576020028201919060005260206000209081546001600160a01b0316815260019091019060200180831161020d575050505050905090565b60408051600380825260808201909252670de0b6b3a7640000916706f05b59d3b20000918390600090836020820160608036833701905050905060005b8381101561062257815186151590868510159082908590859081106105ea576105ea6113d0565b91151560209283029190910190910152801561060d5761060a8786611415565b94505b5050808061061a906113fc565b9150506105c3565b506106a581600081518110610639576106396113d0565b6020026020010151801561066457508160018151811061065b5761065b6113d0565b60200260200101515b801561068757508160028151811061067e5761067e6113d0565b60200260200101515b60405180606001604052806022815260200161156760229139610f0e565b6106e68260006040518060400160405280601e81526020017f416c6c2045544820636f6e73756d656420627920666972737420706f6f6c0000815250610f80565b6107256040518060400160405280601f81526020017f52656d61696e696e672045544820616674657220666972737420706f6f6c3a0081525083610ff8565b61074660405180606001604052806038815260200161152f6038913961103d565b5050505050565b6060601b805480602002602001604051908101604052809291908181526020016000905b8282101561036e57838290600052602060002090600202016040518060400160405290816000820180546107a490611396565b80601f01602080910402602001604051908101604052809291908181526020018280546107d090611396565b801561081d5780601f106107f25761010080835404028352916020019161081d565b820191906000526020600020905b81548152906001019060200180831161080057829003601f168201915b50505050508152602001600182018054806020026020016040519081016040528092919081815260200182805480156108a257602002820191906000526020600020906000905b82829054906101000a900460e01b6001600160e01b031916815260200190600401906020826003010492830192600103820291508084116108645790505b50505050508152505081526020019060010190610771565b6060601a805480602002602001604051908101604052809291908181526020016000905b8282101561036e5783829060005260206000200180546108fd90611396565b80601f016020809104026020016040519081016040528092919081815260200182805461092990611396565b80156109765780601f1061094b57610100808354040283529160200191610976565b820191906000526020600020905b81548152906001019060200180831161095957829003601f168201915b5050505050815260200190600101906108de565b6060601d805480602002602001604051908101604052809291908181526020016000905b8282101561036e5760008481526020908190206040805180820182526002860290920180546001600160a01b03168352600181018054835181870281018701909452808452939491938583019392830182828015610a5857602002820191906000526020600020906000905b82829054906101000a900460e01b6001600160e01b03191681526020019060040190602082600301049283019260010382029150808411610a1a5790505b505050505081525050815260200190600101906109ae565b6060601c805480602002602001604051908101604052809291908181526020016000905b8282101561036e5760008481526020908190206040805180820182526002860290920180546001600160a01b03168352600181018054835181870281018701909452808452939491938583019392830182828015610b3e57602002820191906000526020600020906000905b82829054906101000a900460e01b6001600160e01b03191681526020019060040190602082600301049283019260010382029150808411610b005790505b50505050508152505081526020019060010190610a94565b60606019805480602002602001604051908101604052809291908181526020016000905b8282101561036e578382906000526020600020018054610b9990611396565b80601f0160208091040260200160405190810160405280929190818152602001828054610bc590611396565b8015610c125780601f10610be757610100808354040283529160200191610c12565b820191906000526020600020905b815481529060010190602001808311610bf557829003601f168201915b505050505081526020019060010190610b7a565b60085460009060ff1615610c3a5750600190565b604051630667f9d760e41b8152737109709ecfa91a80626ff3989d68f67f5b1dd12d600482018190526519985a5b195960d21b602483015260009163667f9d7090604401602060405180830381865afa158015610c9b573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610cbf919061142c565b1415905090565b6060601580548060200260200160405190810160405280929190818152602001828054801561022b576020028201919060005260206000209081546001600160a01b0316815260019091019060200180831161020d575050505050905090565b60408051600380825260808201909252670de0b6b3a764000091600091906020820160608036833701905050905060005b6003811015610d985760008311828281518110610d7657610d766113d0565b9115156020928302919091019091015280610d90816113fc565b915050610d57565b50610df281600081518110610daf57610daf6113d0565b60200260200101516040518060400160405280601c81526020017f466972737420706f6f6c206765747320777261707065643d7472756500000000815250610f0e565b610e4b81600181518110610e0857610e086113d0565b60200260200101516040518060400160405280601d81526020017f5365636f6e6420706f6f6c206765747320777261707065643d74727565000000815250610f0e565b610ea481600281518110610e6157610e616113d0565b60200260200101516040518060400160405280601c81526020017f546869726420706f6f6c206765747320777261707065643d7472756500000000815250610f0e565b610ec56040518060600160405280603d81526020016114f2603d913961103d565b5050565b610ec58282604051602401610edf929190611445565b60408051601f198184030181529190526020810180516001600160e01b0316634b5c427760e01b17905261107c565b81610ec55760405163a34edc0360e01b8152737109709ecfa91a80626ff3989d68f67f5b1dd12d9063a34edc0390610f4c9085908590600401611473565b60006040518083038186803b158015610f6457600080fd5b505afa158015610f78573d6000803e3d6000fd5b505050505050565b818314610ff3576040516388b44c8560e01b8152737109709ecfa91a80626ff3989d68f67f5b1dd12d906388b44c8590610fc290869086908690600401611496565b60006040518083038186803b158015610fda57600080fd5b505afa158015610fee573d6000803e3d6000fd5b505050505b505050565b610ec5828260405160240161100e9291906114b5565b60408051601f198184030181529190526020810180516001600160e01b0316632d839cb360e21b17905261107c565b6104c38160405160240161105191906114d7565b60408051601f198184030181529190526020810180516001600160e01b031663104c13eb60e21b1790525b6104c38160006a636f6e736f6c652e6c6f679050600080835160208501845afa505050565b6020808252825182820181905260009190848201906040850190845b818110156110e25783516001600160a01b0316835292840192918401916001016110bd565b50909695505050505050565b6000815180845260005b81811015611114576020818501810151868301820152016110f8565b81811115611126576000602083870101525b50601f01601f19169290920160200192915050565b602080825282518282018190526000919060409081850190600581811b8701840188860187805b858110156111eb57603f198b8503018752825180516001600160a01b031685528901518985018990528051898601819052908a0190606081881b870181019190870190855b818110156111d557605f198985030183526111c38486516110ee565b948e01949350918d01916001016111a7565b505050978a019794505091880191600101611162565b50919a9950505050505050505050565b600081518084526020808501945080840160005b838110156112355781516001600160e01b0319168752958201959082019060010161120f565b509495945050505050565b60006020808301818452808551808352604092508286019150828160051b87010184880160005b838110156112b757888303603f190185528151805187855261128b888601826110ee565b91890151858303868b01529190506112a381836111fb565b968901969450505090860190600101611267565b509098975050505050505050565b6000602080830181845280855180835260408601915060408160051b870101925083870160005b8281101561131a57603f198886030184526113088583516110ee565b945092850192908501906001016112ec565b5092979650505050505050565b60006020808301818452808551808352604092508286019150828160051b87010184880160005b838110156112b757888303603f19018552815180516001600160a01b03168452870151878401879052611383878501826111fb565b958801959350509086019060010161134e565b600181811c908216806113aa57607f821691505b6020821081036113ca57634e487b7160e01b600052602260045260246000fd5b50919050565b634e487b7160e01b600052603260045260246000fd5b634e487b7160e01b600052601160045260246000fd5b60006001820161140e5761140e6113e6565b5060010190565b600082821015611427576114276113e6565b500390565b60006020828403121561143e57600080fd5b5051919050565b60408152600061145860408301856110ee565b828103602084015261146a81856110ee565b95945050505050565b821515815260406020820152600061148e60408301846110ee565b949350505050565b83815282602082015260606040820152600061146a60608301846110ee565b6040815260006114c860408301856110ee565b90508260208301529392505050565b6020815260006114ea60208301846110ee565b939250505056fe49737375653a20416c6c20706f6f6c732067657420777261707065643d74727565207265676172646c657373206f6620617661696c61626c65204554485365636f6e6420616e6420746869726420706f6f6c732077696c6c206661696c207768656e20747279696e6720746f207772617020455448466c617765643a20616c6c20706f6f6c732067657420777261707065643d74727565416c6c2066756e6374696f6e732075736520666c617765642027777261707065643a206d73672e76616c7565203e203027206c6f676963a2646970667358221220e9a4752aca74fd61f81c4a1d7534407fcb5fb771dd59872771e8a7c3faa3cf5564736f6c634300080d0033", "sourceMap": "87:2588:19:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2907:134:6;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3823:151;;;:::i;:::-;;;;;;;:::i;2141:532:19:-;;;:::i;:::-;;3684:133:6;;;:::i;3385:141::-;;;:::i;971:1164:19:-;;;:::i;3193:186:6:-;;;:::i;:::-;;;;;;;:::i;3047:140::-;;;:::i;:::-;;;;;;;:::i;3532:146::-;;;:::i;:::-;;;;;;;:::i;2754:147::-;;;:::i;2459:141::-;;;:::i;1306:195:1:-;;;:::i;:::-;;;6608:14:20;;6601:22;6583:41;;6571:2;6556:18;1306:195:1;6443:187:20;2606:142:6;;;:::i;139:826:19:-;;;:::i;1065:26:13:-;;;;;;;;;2907:134:6;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:6;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;3823:151::-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;2141:532:19:-;2253:183;;;;;;;;;;;;;;;-1:-1:-1;;;2253:183:19;;;;;;;;;;;;;;;;-1:-1:-1;;;2253:183:19;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2253:183:19;;;;-1:-1:-1;;;2253:183:19;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2253:183:19;;;;:34;2447:134;2468:24;2464:1;:28;2447:134;;;2513:57;;;;;;;;;;;;;;-1:-1:-1;;;2513:57:19;;;2549:17;2567:1;2549:20;;;;;;;:::i;:::-;;;;;2513:11;:57::i;:::-;2494:3;;;;:::i;:::-;;;;2447:134;;;;2591:75;2602:4;2591:75;;;;;;;;;;;;;;;;;:10;:75::i;:::-;2181:492;2141:532::o;3684:133:6:-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:6;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:6;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;971:1164:19:-;1238:21;;;1122:1;1238:21;;;;;;;;;1045:7;;1083:9;;1045:7;;1026:16;;1122:1;1238:21;;;;;;;;;;-1:-1:-1;1238:21:19;1211:48;;1275:6;1270:420;1291:9;1287:1;:13;1270:420;;;1561:13;;1415:12;;;;1520:26;;;;;1415:12;;1561:10;;1572:1;;1561:13;;;;;;:::i;:::-;:27;;;:13;;;;;;;;;;;:27;1603:77;;;;1639:26;1655:10;1639:26;;:::i;:::-;;;1603:77;1307:383;;1302:3;;;;;:::i;:::-;;;;1270:420;;;;1758:97;1769:10;1780:1;1769:13;;;;;;;;:::i;:::-;;;;;;;:30;;;;;1786:10;1797:1;1786:13;;;;;;;;:::i;:::-;;;;;;;1769:30;:47;;;;;1803:10;1814:1;1803:13;;;;;;;;:::i;:::-;;;;;;;1769:47;1758:97;;;;;;;;;;;;;;;;;:10;:97::i;:::-;1917:59;1926:12;1940:1;1917:59;;;;;;;;;;;;;;;;;:8;:59::i;:::-;1987:60;;;;;;;;;;;;;;;;;;2034:12;1987:11;:60::i;:::-;2057:71;;;;;;;;;;;;;;;;;;:11;:71::i;:::-;1016:1119;;;;;971:1164::o;3193:186:6:-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3047:140;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3532:146;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2754:147;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2459:141;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1306:195:1;1365:7;;1345:4;;1365:7;;1361:134;;;-1:-1:-1;1395:4:1;;1306:195::o;1361:134::-;1437:33;;-1:-1:-1;;;1437:33:1;;:7;:33;;;7860:51:20;;;-1:-1:-1;;;7927:18:20;;;7920:34;1482:1:1;;1437:7;;7833:18:20;;1437:33:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:47;;1430:54;;1306:195;:::o;2606:142:6:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:6;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;139:826:19:-;378:13;;;389:1;378:13;;;;;;;;;294:7;;271:20;;378:13;;;;;;;;;;;-1:-1:-1;378:13:19;349:42;;483:6;478:120;499:1;495;:5;478:120;;;554:1;539:12;:16;521:12;534:1;521:15;;;;;;;;:::i;:::-;:34;;;:15;;;;;;;;;;;:34;502:3;;;;:::i;:::-;;;;478:120;;;;673:59;684:12;697:1;684:15;;;;;;;;:::i;:::-;;;;;;;673:59;;;;;;;;;;;;;;;;;:10;:59::i;:::-;742:60;753:12;766:1;753:15;;;;;;;;:::i;:::-;;;;;;;742:60;;;;;;;;;;;;;;;;;:10;:60::i;:::-;812:59;823:12;836:1;823:15;;;;;;;;:::i;:::-;;;;;;;812:59;;;;;;;;;;;;;;;;;:10;:59::i;:::-;882:76;;;;;;;;;;;;;;;;;;:11;:76::i;:::-;191:774;;139:826::o;7439:150:15:-;7512:70;7574:2;7578;7528:53;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7528:53:15;;;;;;;;;;;;;;-1:-1:-1;;;;;7528:53:15;-1:-1:-1;;;7528:53:15;;;7512:15;:70::i;1894:148:1:-;1981:4;1976:60;;2001:24;;-1:-1:-1;;;2001:24:1;;:13;;;;:24;;2015:4;;2021:3;;2001:24;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1894:148;;:::o;2823:177::-;2933:5;2925:4;:13;2921:73;;2954:29;;-1:-1:-1;;;2954:29:1;;:11;;;;:29;;2966:4;;2972:5;;2979:3;;2954:29;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2921:73;2823:177;;;:::o;7139:145:15:-;7206:71;7269:2;7273;7222:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7222:54:15;;;;;;;;;;;;;;-1:-1:-1;;;;;7222:54:15;-1:-1:-1;;;7222:54:15;;;7206:15;:71::i;6191:121::-;6246:59;6301:2;6262:42;;;;;;;;:::i;:::-;;;;-1:-1:-1;;6262:42:15;;;;;;;;;;;;;;-1:-1:-1;;;;;6262:42:15;-1:-1:-1;;;6262:42:15;;;851:129;922:51;965:7;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;14:658:20:-;185:2;237:21;;;307:13;;210:18;;;329:22;;;156:4;;185:2;408:15;;;;382:2;367:18;;;156:4;451:195;465:6;462:1;459:13;451:195;;;530:13;;-1:-1:-1;;;;;526:39:20;514:52;;621:15;;;;586:12;;;;562:1;480:9;451:195;;;-1:-1:-1;663:3:20;;14:658;-1:-1:-1;;;;;;14:658:20:o;677:472::-;719:3;757:5;751:12;784:6;779:3;772:19;809:1;819:162;833:6;830:1;827:13;819:162;;;895:4;951:13;;;947:22;;941:29;923:11;;;919:20;;912:59;848:12;819:162;;;999:6;996:1;993:13;990:87;;;1065:1;1058:4;1049:6;1044:3;1040:16;1036:27;1029:38;990:87;-1:-1:-1;1131:2:20;1110:15;-1:-1:-1;;1106:29:20;1097:39;;;;1138:4;1093:50;;677:472;-1:-1:-1;;677:472:20:o;1154:1742::-;1387:2;1439:21;;;1509:13;;1412:18;;;1531:22;;;1358:4;;1387:2;1572;;1590:18;;;;1627:1;1670:15;;;1655:31;;1651:40;;1714:15;;;1358:4;;1778:1089;1794:6;1789:3;1786:15;1778:1089;;;-1:-1:-1;;1863:22:20;;;1859:36;1847:49;;1919:13;;2006:9;;-1:-1:-1;;;;;2002:35:20;1987:51;;2077:11;;2071:18;2109:15;;;2102:27;;;2190:19;;1959:15;;;2222:24;;;2403:21;;;;2269:2;2351:17;;;2339:30;;2335:39;;;2293:15;;;;2448:1;2462:296;2478:8;2473:3;2470:17;2462:296;;;2584:2;2580:7;2571:6;2563;2559:19;2555:33;2548:5;2541:48;2616:42;2651:6;2640:8;2634:15;2616:42;:::i;:::-;2687:17;;;;2606:52;-1:-1:-1;2730:14:20;;;;2506:1;2497:11;2462:296;;;-1:-1:-1;;;2845:12:20;;;;2781:6;-1:-1:-1;;2810:15:20;;;;1820:1;1811:11;1778:1089;;;-1:-1:-1;2884:6:20;;1154:1742;-1:-1:-1;;;;;;;;;;1154:1742:20:o;2901:461::-;2953:3;2991:5;2985:12;3018:6;3013:3;3006:19;3044:4;3073:2;3068:3;3064:12;3057:19;;3110:2;3103:5;3099:14;3131:1;3141:196;3155:6;3152:1;3149:13;3141:196;;;3220:13;;-1:-1:-1;;;;;;3216:40:20;3204:53;;3277:12;;;;3312:15;;;;3177:1;3170:9;3141:196;;;-1:-1:-1;3353:3:20;;2901:461;-1:-1:-1;;;;;2901:461:20:o;3367:1185::-;3585:4;3614:2;3654;3643:9;3639:18;3684:2;3673:9;3666:21;3707:6;3742;3736:13;3773:6;3765;3758:22;3799:2;3789:12;;3832:2;3821:9;3817:18;3810:25;;3894:2;3884:6;3881:1;3877:14;3866:9;3862:30;3858:39;3932:2;3924:6;3920:15;3953:1;3963:560;3977:6;3974:1;3971:13;3963:560;;;4042:22;;;-1:-1:-1;;4038:36:20;4026:49;;4098:13;;4144:9;;4166:18;;;4211:48;4243:15;;;4144:9;4211:48;:::i;:::-;4300:11;;;4294:18;4349:19;;;4332:15;;;4325:44;4294:18;4197:62;-1:-1:-1;4392:51:20;4197:62;4294:18;4392:51;:::i;:::-;4501:12;;;;4382:61;-1:-1:-1;;;4466:15:20;;;;3999:1;3992:9;3963:560;;;-1:-1:-1;4540:6:20;;3367:1185;-1:-1:-1;;;;;;;;3367:1185:20:o;4557:803::-;4719:4;4748:2;4788;4777:9;4773:18;4818:2;4807:9;4800:21;4841:6;4876;4870:13;4907:6;4899;4892:22;4945:2;4934:9;4930:18;4923:25;;5007:2;4997:6;4994:1;4990:14;4979:9;4975:30;4971:39;4957:53;;5045:2;5037:6;5033:15;5066:1;5076:255;5090:6;5087:1;5084:13;5076:255;;;5183:2;5179:7;5167:9;5159:6;5155:22;5151:36;5146:3;5139:49;5211:40;5244:6;5235;5229:13;5211:40;:::i;:::-;5201:50;-1:-1:-1;5309:12:20;;;;5274:15;;;;5112:1;5105:9;5076:255;;;-1:-1:-1;5348:6:20;;4557:803;-1:-1:-1;;;;;;;4557:803:20:o;5365:1073::-;5567:4;5596:2;5636;5625:9;5621:18;5666:2;5655:9;5648:21;5689:6;5724;5718:13;5755:6;5747;5740:22;5781:2;5771:12;;5814:2;5803:9;5799:18;5792:25;;5876:2;5866:6;5863:1;5859:14;5848:9;5844:30;5840:39;5914:2;5906:6;5902:15;5935:1;5945:464;5959:6;5956:1;5953:13;5945:464;;;6024:22;;;-1:-1:-1;;6020:36:20;6008:49;;6080:13;;6125:9;;-1:-1:-1;;;;;6121:35:20;6106:51;;6196:11;;6190:18;6228:15;;;6221:27;;;6271:58;6313:15;;;6190:18;6271:58;:::i;:::-;6387:12;;;;6261:68;-1:-1:-1;;6352:15:20;;;;5981:1;5974:9;5945:464;;6635:380;6714:1;6710:12;;;;6757;;;6778:61;;6832:4;6824:6;6820:17;6810:27;;6778:61;6885:2;6877:6;6874:14;6854:18;6851:38;6848:161;;6931:10;6926:3;6922:20;6919:1;6912:31;6966:4;6963:1;6956:15;6994:4;6991:1;6984:15;6848:161;;6635:380;;;:::o;7020:127::-;7081:10;7076:3;7072:20;7069:1;7062:31;7112:4;7109:1;7102:15;7136:4;7133:1;7126:15;7152:127;7213:10;7208:3;7204:20;7201:1;7194:31;7244:4;7241:1;7234:15;7268:4;7265:1;7258:15;7284:135;7323:3;7344:17;;;7341:43;;7364:18;;:::i;:::-;-1:-1:-1;7411:1:20;7400:13;;7284:135::o;7556:125::-;7596:4;7624:1;7621;7618:8;7615:34;;;7629:18;;:::i;:::-;-1:-1:-1;7666:9:20;;7556:125::o;7965:184::-;8035:6;8088:2;8076:9;8067:7;8063:23;8059:32;8056:52;;;8104:1;8101;8094:12;8056:52;-1:-1:-1;8127:16:20;;7965:184;-1:-1:-1;7965:184:20:o;8154:383::-;8351:2;8340:9;8333:21;8314:4;8377:45;8418:2;8407:9;8403:18;8395:6;8377:45;:::i;:::-;8470:9;8462:6;8458:22;8453:2;8442:9;8438:18;8431:50;8498:33;8524:6;8516;8498:33;:::i;:::-;8490:41;8154:383;-1:-1:-1;;;;;8154:383:20:o;8542:301::-;8727:6;8720:14;8713:22;8702:9;8695:41;8772:2;8767;8756:9;8752:18;8745:30;8676:4;8792:45;8833:2;8822:9;8818:18;8810:6;8792:45;:::i;:::-;8784:53;8542:301;-1:-1:-1;;;;8542:301:20:o;8848:362::-;9053:6;9042:9;9035:25;9096:6;9091:2;9080:9;9076:18;9069:34;9139:2;9134;9123:9;9119:18;9112:30;9016:4;9159:45;9200:2;9189:9;9185:18;9177:6;9159:45;:::i;9215:291::-;9392:2;9381:9;9374:21;9355:4;9412:45;9453:2;9442:9;9438:18;9430:6;9412:45;:::i;:::-;9404:53;;9493:6;9488:2;9477:9;9473:18;9466:34;9215:291;;;;;:::o;9511:220::-;9660:2;9649:9;9642:21;9623:4;9680:45;9721:2;9710:9;9706:18;9698:6;9680:45;:::i;:::-;9672:53;9511:220;-1:-1:-1;;;9511:220:20:o", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "testAffectedFunctions()": "38e3388b", "testEthConsumptionScenario()": "43676ba3", "testMsgValueRemainsConstantInLoop()": "e70f9968"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testAffectedFunctions\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testEthConsumptionScenario\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testMsgValueRemainsConstantInLoop\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/SimpleEthIssueDemo.t.sol\":\"EthHandlingVulnerabilityTest\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/forge-std/src/Base.sol\":{\"keccak256\":\"0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d\",\"dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z\"]},\"lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3\",\"dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY\"]},\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/forge-std/src/StdConstants.sol\":{\"keccak256\":\"0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc\",\"dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r\"]},\"lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc\",\"dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi\"]},\"lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a\",\"dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT\"]},\"lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4\",\"dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602\",\"dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR\"]},\"lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"test/SimpleEthIssueDemo.t.sol\":{\"keccak256\":\"0xfb380ca7296c615711abf282bf3489eda6ef01212e530401a3910efaa9723ae1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28f38df525851105056152c8a2e3d1f6e31a550a8e41d6b79b2c4ac9b582346f\",\"dweb:/ipfs/Qmb4Xcdhyv24a2ghzQmTkzTti6nz1tBv3nV8XcPSyAGR9W\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testAffectedFunctions"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testEthConsumptionScenario"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testMsgValueRemainsConstantInLoop"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/SimpleEthIssueDemo.t.sol": "EthHandlingVulnerabilityTest"}, "evmVersion": "london", "libraries": {}}, "sources": {"lib/forge-std/src/Base.sol": {"keccak256": "0xa3735a6618a3014e42bb4329ad143e3b2c59cd95094350202e4c4a06c9d585dd", "urls": ["bzz-raw://eef9db48d94726ec3d3fa3a84a8d520903705190f7ee7a04a065335a6aeeac4d", "dweb:/ipfs/QmSWmNny7TkzyqRPjGdpERAJuBwwznrGPLUqS4DZy5fX5z"], "license": "MIT"}, "lib/forge-std/src/StdAssertions.sol": {"keccak256": "0xd8eec16034b53b52c90a3d720e121ce7d30d64cc57d854db7d817d5b382dda43", "urls": ["bzz-raw://612780755e32668c7e3b747d94d16c7291101144e084dd9ee563f071711e99e3", "dweb:/ipfs/QmQgtFJXEmDtSHT7tZQTMbb6PCDpq5UDYFvrBnWk1Xo2SY"], "license": "MIT"}, "lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/forge-std/src/StdConstants.sol": {"keccak256": "0x319ccdabfa2c0b2428301445873270ffea20f0e039d4fd5e6eeba65158e4e534", "urls": ["bzz-raw://b633f9d3a719e1d035ce7daa6cc051ddf89a72d34200d14cec37728e245cdabc", "dweb:/ipfs/QmRP7HQJpHMx1CsFrY8tXVVx1DQmi2dcb2BoGfiWaA923r"], "license": "MIT"}, "lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/forge-std/src/StdStorage.sol": {"keccak256": "0x04102de0a79398e4bdea57b7a4818655b4cc66d6f81d1cff08bf428cd0b384cd", "urls": ["bzz-raw://53edc6c8f7f67cafc0129f039637c77d979880f7f1947defea31e8f0c05095bc", "dweb:/ipfs/QmUKXJd1vFCkxxrkXNLURdXrx2apoyWQFrFb5UqNkjdHVi"], "license": "MIT"}, "lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/forge-std/src/StdUtils.sol": {"keccak256": "0xb2469a902a326074034c4f7081d868113db0edbb7cf48b86528af2d6b07295f8", "urls": ["bzz-raw://1430a81c4978be875e2a3b31a8bfa4e1438fecd327f23771b690d64db63c020a", "dweb:/ipfs/QmW6aB2u1LNaRgGQFwjV7L7UbxsRg63iJ7AuujPouEa4cT"], "license": "MIT"}, "lib/forge-std/src/Test.sol": {"keccak256": "0x3dda6083a83dfa3e8526e97bcc28e862ee2442dd58fe94d5c426d65b8e38f73c", "urls": ["bzz-raw://33f8c02e4dabdab86a6825125856446a8657eacd712318b51b7818e4a6f6e3f4", "dweb:/ipfs/QmRKSjVnrk54yr8wTK2e6QxRjiuba2H8HJSKunHAkdo7RG"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0x20163fcc9d22581bb8909966c611bd29d482445eb415227407f0ff7ff1b034ab", "urls": ["bzz-raw://b0ac641e4ae6cd79073d514187eb9089dda9490394f0212523ff6136d818f602", "dweb:/ipfs/QmaDjxAK5dctUyFDndGDvteSyVrxwBx3FFefzvQb2Mz8ZR"], "license": "MIT OR Apache-2.0"}, "lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "test/SimpleEthIssueDemo.t.sol": {"keccak256": "0xfb380ca7296c615711abf282bf3489eda6ef01212e530401a3910efaa9723ae1", "urls": ["bzz-raw://28f38df525851105056152c8a2e3d1f6e31a550a8e41d6b79b2c4ac9b582346f", "dweb:/ipfs/Qmb4Xcdhyv24a2ghzQmTkzTti6nz1tBv3nV8XcPSyAGR9W"], "license": "MIT"}}, "version": 1}, "id": 19}