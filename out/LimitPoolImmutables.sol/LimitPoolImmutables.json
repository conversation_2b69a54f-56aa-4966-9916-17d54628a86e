{"abi": [{"type": "function", "name": "genesisTime", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "pure"}, {"type": "function", "name": "maxPrice", "inputs": [], "outputs": [{"name": "", "type": "uint160", "internalType": "uint160"}], "stateMutability": "pure"}, {"type": "function", "name": "minPrice", "inputs": [], "outputs": [{"name": "", "type": "uint160", "internalType": "uint160"}], "stateMutability": "pure"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "poolToken", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "swapFee", "inputs": [], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}], "stateMutability": "pure"}, {"type": "function", "name": "tickSpacing", "inputs": [], "outputs": [{"name": "", "type": "int16", "internalType": "int16"}], "stateMutability": "pure"}, {"type": "function", "name": "token0", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "token1", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}], "bytecode": {"object": "0x608060405234801561001057600080fd5b50610244806100206000396000f3fe608060405234801561001057600080fd5b50600436106100935760003560e01c8063cbdf382c11610066578063cbdf382c146100fd578063d0c93a7c14610105578063d21220a714610120578063e38d6b5c14610128578063e45be8eb1461013057600080fd5b80630dfe16811461009857806342c6498a146100bd57806354cf2aeb146100da5780638da5cb5b146100f5575b600080fd5b6100a0610138565b6040516001600160a01b0390911681526020015b60405180910390f35b6100c5610149565b60405163ffffffff90911681526020016100b4565b6100e2610155565b60405161ffff90911681526020016100b4565b6100a0610161565b6100a061016d565b61010d610179565b60405160019190910b81526020016100b4565b6100a0610185565b6100a0610191565b6100a061019d565b600061014460146101a5565b905090565b600061014460786101c8565b6000610144607e6101eb565b600061014460006101a5565b6000610144603c6101a5565b6000610144607c6101eb565b600061014460286101a5565b600061014460646101a5565b600061014460505b6000806101ba3660011981013560f01c900390565b929092013560601c92915050565b6000806101dd3660011981013560f01c900390565b929092013560e01c92915050565b6000806102003660011981013560f01c900390565b929092013560f01c9291505056fea2646970667358221220e80be2727a3977e7d3fc9882a0f51e631fe901a28497ef0a424f8dde0a635a5364736f6c634300080d0033", "sourceMap": "115:924:10:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561001057600080fd5b50600436106100935760003560e01c8063cbdf382c11610066578063cbdf382c146100fd578063d0c93a7c14610105578063d21220a714610120578063e38d6b5c14610128578063e45be8eb1461013057600080fd5b80630dfe16811461009857806342c6498a146100bd57806354cf2aeb146100da5780638da5cb5b146100f5575b600080fd5b6100a0610138565b6040516001600160a01b0390911681526020015b60405180910390f35b6100c5610149565b60405163ffffffff90911681526020016100b4565b6100e2610155565b60405161ffff90911681526020016100b4565b6100a0610161565b6100a061016d565b61010d610179565b60405160019190910b81526020016100b4565b6100a0610185565b6100a0610191565b6100a061019d565b600061014460146101a5565b905090565b600061014460786101c8565b6000610144607e6101eb565b600061014460006101a5565b6000610144603c6101a5565b6000610144607c6101eb565b600061014460286101a5565b600061014460646101a5565b600061014460505b6000806101ba3660011981013560f01c900390565b929092013560601c92915050565b6000806101dd3660011981013560f01c900390565b929092013560e01c92915050565b6000806102003660011981013560f01c900390565b929092013560f01c9291505056fea2646970667358221220e80be2727a3977e7d3fc9882a0f51e631fe901a28497ef0a424f8dde0a635a5364736f6c634300080d0033", "sourceMap": "115:924:10:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;253:90;;;:::i;:::-;;;-1:-1:-1;;;;;178:32:78;;;160:51;;148:2;133:18;253:90:10;;;;;;;;741:94;;;:::i;:::-;;;396:10:78;384:23;;;366:42;;354:2;339:18;741:94:10;222:192:78;947:90:10;;;:::i;:::-;;;593:6:78;581:19;;;563:38;;551:2;536:18;947:90:10;419:188:78;159:88:10;;;:::i;445:93::-;;;:::i;841:100::-;;;:::i;:::-;;;783:1:78;772:21;;;;754:40;;742:2;727:18;841:100:10;612:188:78;349:90:10;;;:::i;642:93::-;;;:::i;544:92::-;;;:::i;253:90::-;292:7;318:18;333:2;318:14;:18::i;:::-;311:25;;253:90;:::o;741:94::-;785:6;810:18;824:3;810:13;:18::i;947:90::-;987:6;1012:18;1026:3;1012:13;:18::i;159:88::-;197:7;223:17;238:1;223:14;:17::i;445:93::-;487:7;513:18;528:2;513:14;:18::i;841:100::-;885:5;915:18;929:3;915:13;:18::i;349:90::-;388:7;414:18;429:2;414:14;:18::i;642:93::-;683:7;709:19;724:3;709:14;:19::i;544:92::-;585:7;611:18;626:2;1110:275:15;1176:11;1199:14;1216:25;13904:14;-1:-1:-1;;13900:22:15;;13887:36;13882:3;13878:46;13858:67;;;13698:243;1216:25;1345:22;;;;1332:36;1328:2;1324:45;;1110:275;-1:-1:-1;;1110:275:15:o;12343:274::-;12408:10;12430:14;12447:25;13904:14;-1:-1:-1;;13900:22:15;;13887:36;13882:3;13878:46;13858:67;;;13698:243;12447:25;12577:22;;;;12564:36;12559:3;12555:46;;12343:274;-1:-1:-1;;12343:274:15:o;13011:::-;13076:10;13098:14;13115:25;13904:14;-1:-1:-1;;13900:22:15;;13887:36;13882:3;13878:46;13858:67;;;13698:243;13115:25;13245:22;;;;13232:36;13227:3;13223:46;;13011:274;-1:-1:-1;;13011:274:15:o", "linkReferences": {}}, "methodIdentifiers": {"genesisTime()": "42c6498a", "maxPrice()": "e38d6b5c", "minPrice()": "e45be8eb", "owner()": "8da5cb5b", "poolToken()": "cbdf382c", "swapFee()": "54cf2aeb", "tickSpacing()": "d0c93a7c", "token0()": "0dfe1681", "token1()": "d21220a7"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"genesisTime\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"maxPrice\",\"outputs\":[{\"internalType\":\"uint160\",\"name\":\"\",\"type\":\"uint160\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"minPrice\",\"outputs\":[{\"internalType\":\"uint160\",\"name\":\"\",\"type\":\"uint160\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"poolToken\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"swapFee\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"tickSpacing\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"\",\"type\":\"int16\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token0\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token1\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/base/storage/LimitPoolImmutables.sol\":\"LimitPoolImmutables\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/base/storage/LimitPoolImmutables.sol\":{\"keccak256\":\"0x12df280adc3ad5199cdb4ef07df724427f9694a2eaae240f31ba887b0d5946de\",\"license\":\"BSD\",\"urls\":[\"bzz-raw://829502a9103d01f0ce94459377da9b972629e6b227ceff9a7d6d01f866fc4715\",\"dweb:/ipfs/QmTj5jRT6Pff6efUgU11ASNaK1PoLykwKNWFAoPndyfZgQ\"]},\"contracts/external/solady/Clone.sol\":{\"keccak256\":\"0xe7dc35cc81529e1e43b249f99de3a26e595ffd209450b85f3e8bc74a0d9aea01\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6d0471fad81c71706936f7b014b0cfcd5f03337450838409c8c02fedc38a8560\",\"dweb:/ipfs/Qmede4qMoU29saLxaBxaA72kuWt4794HLcB7ZdXHRrBFXC\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "pure", "type": "function", "name": "genesisTime", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "maxPrice", "outputs": [{"internalType": "uint160", "name": "", "type": "uint160"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "minPrice", "outputs": [{"internalType": "uint160", "name": "", "type": "uint160"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "poolToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "swapFee", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "tickSpacing", "outputs": [{"internalType": "int16", "name": "", "type": "int16"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "token0", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "token1", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/base/storage/LimitPoolImmutables.sol": "LimitPoolImmutables"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/base/storage/LimitPoolImmutables.sol": {"keccak256": "0x12df280adc3ad5199cdb4ef07df724427f9694a2eaae240f31ba887b0d5946de", "urls": ["bzz-raw://829502a9103d01f0ce94459377da9b972629e6b227ceff9a7d6d01f866fc4715", "dweb:/ipfs/QmTj5jRT6Pff6efUgU11ASNaK1PoLykwKNWFAoPndyfZgQ"], "license": "BSD"}, "contracts/external/solady/Clone.sol": {"keccak256": "0xe7dc35cc81529e1e43b249f99de3a26e595ffd209450b85f3e8bc74a0d9aea01", "urls": ["bzz-raw://6d0471fad81c71706936f7b014b0cfcd5f03337450838409c8c02fedc38a8560", "dweb:/ipfs/Qmede4qMoU29saLxaBxaA72kuWt4794HLcB7ZdXHRrBFXC"], "license": "MIT"}}, "version": 1}, "id": 10}