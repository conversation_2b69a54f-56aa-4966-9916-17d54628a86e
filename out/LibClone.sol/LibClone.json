{"abi": [{"type": "error", "name": "DeploymentFailed", "inputs": []}, {"type": "error", "name": "SaltDoesNotStartWithCaller", "inputs": []}], "bytecode": {"object": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea264697066735822122079f22fea52e57811bcdb2dad233fdb4506c95d547bf8f352de6a548c5e86356264736f6c634300080d0033", "sourceMap": "1088:7366:16:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;1088:7366:16;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x73000000000000000000000000000000000000000030146080604052600080fdfea264697066735822122079f22fea52e57811bcdb2dad233fdb4506c95d547bf8f352de6a548c5e86356264736f6c634300080d0033", "sourceMap": "1088:7366:16:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"DeploymentFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SaltDoesNotStartWithCaller\",\"type\":\"error\"}],\"devdoc\":{\"author\":\"Solady (https://github.com/vectorized/solady/blob/main/src/utils/LibClone.sol)Minimal proxy by 0age (https://github.com/0age)Clones with immutable args by wighawag, zefram.eth, <PERSON>-<PERSON> & <PERSON> (https://github.com/Saw-mon-and-<PERSON>/clones-with-immutable-args)\",\"details\":\"Minimal proxy: Although the sw0nt pattern saves 5 gas over the erc-1167 pattern during runtime, it is not supported out-of-the-box on Etherscan. Hence, we choose to use the 0age pattern, which saves 4 gas over the erc-1167 pattern during runtime, and has the smallest bytecode.Clones with immutable args (CWIA): The implementation of CWIA here implements a `receive()` method that emits the `ReceiveETH(uint256)` event. This skips the `DELEGATECALL` when there is no calldata, enabling us to accept hard gas-capped `sends` & `transfers` for maximum backwards composability. The minimal proxy implementation does not offer this feature.\",\"errors\":{\"DeploymentFailed()\":[{\"details\":\"Unable to deploy the clone.\"}],\"SaltDoesNotStartWithCaller()\":[{\"details\":\"The salt must start with either the zero address or the caller.\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Minimal proxy library.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/external/solady/LibClone.sol\":\"LibClone\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/external/solady/LibClone.sol\":{\"keccak256\":\"0x93750a76e235631c1438283750f8b096026a11d82399fdc002816c55acc1f55a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e99db3fbe71ac90a31b411481b148d17ef2cb6c748d38216ae233778d0228d4d\",\"dweb:/ipfs/QmWkt4Q5fQkMkq8a3Vf2KjKBqkAvqzpGFLy1rFvWzyYN5k\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "DeploymentFailed"}, {"inputs": [], "type": "error", "name": "SaltDoesNotStartWithCaller"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/external/solady/LibClone.sol": "LibClone"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/external/solady/LibClone.sol": {"keccak256": "0x93750a76e235631c1438283750f8b096026a11d82399fdc002816c55acc1f55a", "urls": ["bzz-raw://e99db3fbe71ac90a31b411481b148d17ef2cb6c748d38216ae233778d0228d4d", "dweb:/ipfs/QmWkt4Q5fQkMkq8a3Vf2KjKBqkAvqzpGFLy1rFvWzyYN5k"], "license": "MIT"}}, "version": 1}, "id": 16}