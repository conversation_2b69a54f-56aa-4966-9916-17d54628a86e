{"abi": [{"type": "event", "name": "CollectRange0", "inputs": [{"name": "amount0", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "CollectRange1", "inputs": [{"name": "amount1", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}], "bytecode": {"object": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220af231ff334eb702b920bc5f48b5bf679cc16d8d457d2bf1e5c6461d18050673d64736f6c634300080d0033", "sourceMap": "189:1655:61:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;189:1655:61;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220af231ff334eb702b920bc5f48b5bf679cc16d8d457d2bf1e5c6461d18050673d64736f6c634300080d0033", "sourceMap": "189:1655:61:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"amount0\",\"type\":\"uint128\"}],\"name\":\"CollectRange0\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"amount1\",\"type\":\"uint128\"}],\"name\":\"CollectRange1\",\"type\":\"event\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/utils/Collect.sol\":\"Collect\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/base/storage/LimitPoolFactoryStorage.sol\":{\"keccak256\":\"0xec8efbb85bf6d831dc0c3910eccbe1b6def550ac0b88eef62b4a7c0c314a31ae\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://363a51daf8ce8d46c62bb8d9821b99ea2ec7d4f81a0512f5b83901d3a9a4631f\",\"dweb:/ipfs/QmeKyWJwsX9LwB5A8ovGm7J2nToJvR1dnE3sMww46ERb55\"]},\"contracts/interfaces/IPositionERC1155.sol\":{\"keccak256\":\"0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11\",\"dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe\"]},\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/limit/ILimitPool.sol\":{\"keccak256\":\"0x8754512ae636a8871b11412ad16735be460142a48ee9f0fc50310f4d4aa45227\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://9621227f4ab660043534cb124559d96d42cefad3441b61a7a82be81ef8674dcf\",\"dweb:/ipfs/QmYZsFqXwFW6dcYSrjc4R1Lu1r9MbMbo6EQH6Rk2yqztKA\"]},\"contracts/interfaces/limit/ILimitPoolFactory.sol\":{\"keccak256\":\"0x89fc412319c30451896a1db8d77bb528c6df383523cf7f278534a645a4a49733\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://495509c3a546e0b85b34a8ef7fc114ca5bb98d831c8bbd176b42d36703f00fd0\",\"dweb:/ipfs/QmcFR6a32Fp7s4hRDmJfp7GaC65uQUtRo6VhE6ygCpZefm\"]},\"contracts/interfaces/range/IRangePool.sol\":{\"keccak256\":\"0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab\",\"dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt\"]},\"contracts/interfaces/range/IRangePoolManager.sol\":{\"keccak256\":\"0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065\",\"dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP\"]},\"contracts/interfaces/structs/LimitPoolStructs.sol\":{\"keccak256\":\"0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef\",\"dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/interfaces/structs/RangePoolStructs.sol\":{\"keccak256\":\"0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9\",\"dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx\"]},\"contracts/libraries/Samples.sol\":{\"keccak256\":\"0xae162c0e142bbe444c3748135d74bf272849768abca62c1908794bb72551c0eb\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://cf38a14a2a9653dc9b44ad37f091b7c6117be6ca8a76b5f9e279998bd9060763\",\"dweb:/ipfs/QmYxJm1w9YL4bYHJqNcLTRxiwYKH68dTzNGGQiQxFmnADE\"]},\"contracts/libraries/TickMap.sol\":{\"keccak256\":\"0xa77a5fb9acdce8ed769375832ba046cd4173eb47112b3083021b1637df903555\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://71ed48dae8ec94546989456c1d2dff0ff412db9d364d2996dc2f5877a1c82c64\",\"dweb:/ipfs/QmStzY8Q86bF8A7SLasHtuKF3B6MNzruBidc8YhU1yLwsK\"]},\"contracts/libraries/Ticks.sol\":{\"keccak256\":\"0xae060497e2eed43dae98cffe0889ca1d0ee2810efda6d6772c9aa25db384fd94\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://b2bee1eea24c1865372ae5a624f7ccfb85c42b615ac74363e3389d382043eed8\",\"dweb:/ipfs/QmfQ2w8JHgMPk2CokEv1QYJtrSEz84fh6VheyWfMsqQjw3\"]},\"contracts/libraries/limit/Claims.sol\":{\"keccak256\":\"0x2e7796e5042a82fac8e50869ad07b4a97a0d008094b1d8c2ea3add580729b58b\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://78817a63f595cd3f210298a270b99fed9d72b28d87f22f0442b5f03044339b19\",\"dweb:/ipfs/QmZSwiyZcQjTHaEx9KPtD9CUF8ewMnqvxHpTrGvWU3w7Kg\"]},\"contracts/libraries/limit/EpochMap.sol\":{\"keccak256\":\"0x6469820c0b831b3837bb187ab5eecc0e441732745ddd6315ef3fcacf73f2c80f\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://06a2c94be49eb09e6d219b7b4cfe1a8f60e9978b36c5cc5301dc4dc8f314c1d2\",\"dweb:/ipfs/QmYnREJYpkay3ey3S1uhwxawYAxgZdyLzRCSPNtrWZzmid\"]},\"contracts/libraries/limit/LimitPositions.sol\":{\"keccak256\":\"0xbef58cca843b52cedbd4fb7206e61f72fa953a19227978bc52c1799268171947\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://966deff90f8d11bb9f6131ba6eeebc7f5b5fb5d1b4091d95bb33ced09a791d9e\",\"dweb:/ipfs/QmP3Ronwzp79vq4E4Abs5T9Gu8jdk2rg8iiQsZAf32ypGM\"]},\"contracts/libraries/limit/LimitTicks.sol\":{\"keccak256\":\"0x2b2f355f979ebbbcb20932480937b09e45d9b73bf0dd9a8a4aaf48e511d7fe8d\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://ca969198d60151e3e58180f6581a4c29e9a69eeba14c248893c26f9d8cca11e2\",\"dweb:/ipfs/QmR3cbLsddrVtvGvGNx7o8fcYj1w5sqTUeVg1yeTnZZJoo\"]},\"contracts/libraries/math/ConstantProduct.sol\":{\"keccak256\":\"0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8\",\"dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED\"]},\"contracts/libraries/math/OverflowMath.sol\":{\"keccak256\":\"0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29\",\"dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T\"]},\"contracts/libraries/range/math/FeeMath.sol\":{\"keccak256\":\"0x81c976abdc41c8af4e3276741b1824157d82eb60eca512dee17a28539832df4f\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://7435150736fdc891d7c503adc4d027334045d82e67d3112edc30f6dc41892c49\",\"dweb:/ipfs/QmTNAoETKtyBSmq2PaPcFQdxG8VhA56EHfPc3WpeRLPKHH\"]},\"contracts/libraries/utils/Collect.sol\":{\"keccak256\":\"0xc4a6b4dcb543dba1b9cf5288dc8968457771fe4d8d1a980459ea21f5de38e94b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c3bfed6dfcc73048bfcdeea301cbac3c3c712f2a6dce5ecf5c93cba25a63338a\",\"dweb:/ipfs/QmbwoSprsiTna7ZLQMoi7dnnQNsTvcxN7UbcYsNVmE7iyG\"]},\"contracts/libraries/utils/SafeCast.sol\":{\"keccak256\":\"0xc4ec846404c1111c6353c8b2251469e07f32570b1ffafc58701c385c458c21e2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://51b7397ff450c2aedafdcb40a6ca181c8e1947fb409dbbcd8b000b8eee2e9fcf\",\"dweb:/ipfs/QmQ3wPdCtuLreoEkvFoifsx4drC5kT3p2CwGKBBn2qQso4\"]},\"contracts/libraries/utils/SafeTransfers.sol\":{\"keccak256\":\"0x1dcd632dc5123e4f15ad19c8c88ba0a3bf42f0a5b91211169aecfcc8523ed0f8\",\"license\":\"Unlicense\",\"urls\":[\"bzz-raw://67467c0c05b10847e84f3f7229027de3f80f448cb8589a0b4314b45c76220dd1\",\"dweb:/ipfs/QmS17vjUJ16LmtZ7cKdQAGZKcdEYck5NsoAhjGgZdc3b9S\"]},\"contracts/libraries/utils/String.sol\":{\"keccak256\":\"0x0d03855bfeabee266fa8252cbcac9bab81b5a951d3c0a99ae51f2e4a1942dbf2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2fba9aae08e1406ee8c7c21da98bd72c4f34e55323ffbb0146e9ec8f0a4a06e3\",\"dweb:/ipfs/QmbT189JtHc8esSwJ9TCkLhYVBVguJr2noepxKh5XrjEfj\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0\",\"dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34\",\"dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd\",\"dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92\",\"dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f\",\"dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint128", "name": "amount0", "type": "uint128", "indexed": false}], "type": "event", "name": "CollectRange0", "anonymous": false}, {"inputs": [{"internalType": "uint128", "name": "amount1", "type": "uint128", "indexed": false}], "type": "event", "name": "CollectRange1", "anonymous": false}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/utils/Collect.sol": "Collect"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/base/storage/LimitPoolFactoryStorage.sol": {"keccak256": "0xec8efbb85bf6d831dc0c3910eccbe1b6def550ac0b88eef62b4a7c0c314a31ae", "urls": ["bzz-raw://363a51daf8ce8d46c62bb8d9821b99ea2ec7d4f81a0512f5b83901d3a9a4631f", "dweb:/ipfs/QmeKyWJwsX9LwB5A8ovGm7J2nToJvR1dnE3sMww46ERb55"], "license": "BUSL-1.1"}, "contracts/interfaces/IPositionERC1155.sol": {"keccak256": "0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c", "urls": ["bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11", "dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe"], "license": "MIT"}, "contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPool.sol": {"keccak256": "0x8754512ae636a8871b11412ad16735be460142a48ee9f0fc50310f4d4aa45227", "urls": ["bzz-raw://9621227f4ab660043534cb124559d96d42cefad3441b61a7a82be81ef8674dcf", "dweb:/ipfs/QmYZsFqXwFW6dcYSrjc4R1Lu1r9MbMbo6EQH6Rk2yqztKA"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPoolFactory.sol": {"keccak256": "0x89fc412319c30451896a1db8d77bb528c6df383523cf7f278534a645a4a49733", "urls": ["bzz-raw://495509c3a546e0b85b34a8ef7fc114ca5bb98d831c8bbd176b42d36703f00fd0", "dweb:/ipfs/QmcFR6a32Fp7s4hRDmJfp7GaC65uQUtRo6VhE6ygCpZefm"], "license": "BUSL-1.1"}, "contracts/interfaces/range/IRangePool.sol": {"keccak256": "0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf", "urls": ["bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab", "dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePoolManager.sol": {"keccak256": "0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139", "urls": ["bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065", "dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/LimitPoolStructs.sol": {"keccak256": "0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4", "urls": ["bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef", "dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/interfaces/structs/RangePoolStructs.sol": {"keccak256": "0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd", "urls": ["bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9", "dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx"], "license": "GPLv3"}, "contracts/libraries/Samples.sol": {"keccak256": "0xae162c0e142bbe444c3748135d74bf272849768abca62c1908794bb72551c0eb", "urls": ["bzz-raw://cf38a14a2a9653dc9b44ad37f091b7c6117be6ca8a76b5f9e279998bd9060763", "dweb:/ipfs/QmYxJm1w9YL4bYHJqNcLTRxiwYKH68dTzNGGQiQxFmnADE"], "license": "GPLv3"}, "contracts/libraries/TickMap.sol": {"keccak256": "0xa77a5fb9acdce8ed769375832ba046cd4173eb47112b3083021b1637df903555", "urls": ["bzz-raw://71ed48dae8ec94546989456c1d2dff0ff412db9d364d2996dc2f5877a1c82c64", "dweb:/ipfs/QmStzY8Q86bF8A7SLasHtuKF3B6MNzruBidc8YhU1yLwsK"], "license": "GPLv3"}, "contracts/libraries/Ticks.sol": {"keccak256": "0xae060497e2eed43dae98cffe0889ca1d0ee2810efda6d6772c9aa25db384fd94", "urls": ["bzz-raw://b2bee1eea24c1865372ae5a624f7ccfb85c42b615ac74363e3389d382043eed8", "dweb:/ipfs/QmfQ2w8JHgMPk2CokEv1QYJtrSEz84fh6VheyWfMsqQjw3"], "license": "GPLv3"}, "contracts/libraries/limit/Claims.sol": {"keccak256": "0x2e7796e5042a82fac8e50869ad07b4a97a0d008094b1d8c2ea3add580729b58b", "urls": ["bzz-raw://78817a63f595cd3f210298a270b99fed9d72b28d87f22f0442b5f03044339b19", "dweb:/ipfs/QmZSwiyZcQjTHaEx9KPtD9CUF8ewMnqvxHpTrGvWU3w7Kg"], "license": "BUSL-1.1"}, "contracts/libraries/limit/EpochMap.sol": {"keccak256": "0x6469820c0b831b3837bb187ab5eecc0e441732745ddd6315ef3fcacf73f2c80f", "urls": ["bzz-raw://06a2c94be49eb09e6d219b7b4cfe1a8f60e9978b36c5cc5301dc4dc8f314c1d2", "dweb:/ipfs/QmYnREJYpkay3ey3S1uhwxawYAxgZdyLzRCSPNtrWZzmid"], "license": "BUSL-1.1"}, "contracts/libraries/limit/LimitPositions.sol": {"keccak256": "0xbef58cca843b52cedbd4fb7206e61f72fa953a19227978bc52c1799268171947", "urls": ["bzz-raw://966deff90f8d11bb9f6131ba6eeebc7f5b5fb5d1b4091d95bb33ced09a791d9e", "dweb:/ipfs/QmP3Ronwzp79vq4E4Abs5T9Gu8jdk2rg8iiQsZAf32ypGM"], "license": "BUSL-1.1"}, "contracts/libraries/limit/LimitTicks.sol": {"keccak256": "0x2b2f355f979ebbbcb20932480937b09e45d9b73bf0dd9a8a4aaf48e511d7fe8d", "urls": ["bzz-raw://ca969198d60151e3e58180f6581a4c29e9a69eeba14c248893c26f9d8cca11e2", "dweb:/ipfs/QmR3cbLsddrVtvGvGNx7o8fcYj1w5sqTUeVg1yeTnZZJoo"], "license": "GPLv3"}, "contracts/libraries/math/ConstantProduct.sol": {"keccak256": "0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3", "urls": ["bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8", "dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED"], "license": "GPL-3.0-or-later"}, "contracts/libraries/math/OverflowMath.sol": {"keccak256": "0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b", "urls": ["bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29", "dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T"], "license": "MIT"}, "contracts/libraries/range/math/FeeMath.sol": {"keccak256": "0x81c976abdc41c8af4e3276741b1824157d82eb60eca512dee17a28539832df4f", "urls": ["bzz-raw://7435150736fdc891d7c503adc4d027334045d82e67d3112edc30f6dc41892c49", "dweb:/ipfs/QmTNAoETKtyBSmq2PaPcFQdxG8VhA56EHfPc3WpeRLPKHH"], "license": "GPLv3"}, "contracts/libraries/utils/Collect.sol": {"keccak256": "0xc4a6b4dcb543dba1b9cf5288dc8968457771fe4d8d1a980459ea21f5de38e94b", "urls": ["bzz-raw://c3bfed6dfcc73048bfcdeea301cbac3c3c712f2a6dce5ecf5c93cba25a63338a", "dweb:/ipfs/QmbwoSprsiTna7ZLQMoi7dnnQNsTvcxN7UbcYsNVmE7iyG"], "license": "MIT"}, "contracts/libraries/utils/SafeCast.sol": {"keccak256": "0xc4ec846404c1111c6353c8b2251469e07f32570b1ffafc58701c385c458c21e2", "urls": ["bzz-raw://51b7397ff450c2aedafdcb40a6ca181c8e1947fb409dbbcd8b000b8eee2e9fcf", "dweb:/ipfs/QmQ3wPdCtuLreoEkvFoifsx4drC5kT3p2CwGKBBn2qQso4"], "license": "GPL-2.0-or-later"}, "contracts/libraries/utils/SafeTransfers.sol": {"keccak256": "0x1dcd632dc5123e4f15ad19c8c88ba0a3bf42f0a5b91211169aecfcc8523ed0f8", "urls": ["bzz-raw://67467c0c05b10847e84f3f7229027de3f80f448cb8589a0b4314b45c76220dd1", "dweb:/ipfs/QmS17vjUJ16LmtZ7cKdQAGZKcdEYck5NsoAhjGgZdc3b9S"], "license": "Unlicense"}, "contracts/libraries/utils/String.sol": {"keccak256": "0x0d03855bfeabee266fa8252cbcac9bab81b5a951d3c0a99ae51f2e4a1942dbf2", "urls": ["bzz-raw://2fba9aae08e1406ee8c7c21da98bd72c4f34e55323ffbb0146e9ec8f0a4a06e3", "dweb:/ipfs/QmbT189JtHc8esSwJ9TCkLhYVBVguJr2noepxKh5XrjEfj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238", "urls": ["bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0", "dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b", "urls": ["bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34", "dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca", "urls": ["bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd", "dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7", "urls": ["bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92", "dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1", "urls": ["bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f", "dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy"], "license": "MIT"}}, "version": 1}, "id": 61}