{"abi": [{"type": "function", "name": "burn", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.BurnCoverParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "burnPercent", "type": "uint128", "internalType": "uint128"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "claim", "type": "int24", "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}, {"name": "sync", "type": "bool", "internalType": "bool"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "immutables", "inputs": [], "outputs": [{"name": "constants", "type": "tuple", "internalType": "struct PoolsharkStructs.CoverImmutables", "components": [{"name": "source", "type": "address", "internalType": "contract ITwapSource"}, {"name": "bounds", "type": "tuple", "internalType": "struct PoolsharkStructs.PriceBounds", "components": [{"name": "min", "type": "uint160", "internalType": "uint160"}, {"name": "max", "type": "uint160", "internalType": "uint160"}]}, {"name": "owner", "type": "address", "internalType": "address"}, {"name": "token0", "type": "address", "internalType": "address"}, {"name": "token1", "type": "address", "internalType": "address"}, {"name": "poolImpl", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "inputPool", "type": "address", "internalType": "address"}, {"name": "minAmountPerAuction", "type": "uint128", "internalType": "uint128"}, {"name": "genesisTime", "type": "uint32", "internalType": "uint32"}, {"name": "minPositionWidth", "type": "int16", "internalType": "int16"}, {"name": "tickSpread", "type": "int16", "internalType": "int16"}, {"name": "twap<PERSON><PERSON>th", "type": "uint16", "internalType": "uint16"}, {"name": "auctionLength", "type": "uint16", "internalType": "uint16"}, {"name": "sampleInterval", "type": "uint16", "internalType": "uint16"}, {"name": "token0Decimals", "type": "uint8", "internalType": "uint8"}, {"name": "token1Decimals", "type": "uint8", "internalType": "uint8"}, {"name": "minAmountLowerPriced", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.MintCoverParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}, {"name": "callbackData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "quote", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.QuoteParams", "components": [{"name": "priceLimit", "type": "uint160", "internalType": "uint160"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}, {"name": "exactIn", "type": "bool", "internalType": "bool"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}]}], "outputs": [{"name": "inAmount", "type": "int256", "internalType": "int256"}, {"name": "outAmount", "type": "int256", "internalType": "int256"}, {"name": "priceAfter", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "swap", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.SwapParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "priceLimit", "type": "uint160", "internalType": "uint160"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}, {"name": "exactIn", "type": "bool", "internalType": "bool"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}, {"name": "callbackData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "amount0Delta", "type": "int256", "internalType": "int256"}, {"name": "amount1Delta", "type": "int256", "internalType": "int256"}], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"burn((address,uint128,uint32,int24,bool,bool))": "842f9cb2", "immutables()": "fdf53665", "mint((address,uint128,uint32,int24,int24,bool,bytes))": "ae286976", "quote((uint160,uint128,bool,bool))": "43865d4e", "swap((address,uint160,uint128,bool,bool,bytes))": "e323eb0e"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"burnPercent\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"claim\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"sync\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.BurnCoverParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"immutables\",\"outputs\":[{\"components\":[{\"internalType\":\"contract ITwapSource\",\"name\":\"source\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"min\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"max\",\"type\":\"uint160\"}],\"internalType\":\"struct PoolsharkStructs.PriceBounds\",\"name\":\"bounds\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"inputPool\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"minAmountPerAuction\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"genesisTime\",\"type\":\"uint32\"},{\"internalType\":\"int16\",\"name\":\"minPositionWidth\",\"type\":\"int16\"},{\"internalType\":\"int16\",\"name\":\"tickSpread\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"twapLength\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"auctionLength\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"sampleInterval\",\"type\":\"uint16\"},{\"internalType\":\"uint8\",\"name\":\"token0Decimals\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"token1Decimals\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"minAmountLowerPriced\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.CoverImmutables\",\"name\":\"constants\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"callbackData\",\"type\":\"bytes\"}],\"internalType\":\"struct PoolsharkStructs.MintCoverParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint160\",\"name\":\"priceLimit\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"bool\",\"name\":\"exactIn\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.QuoteParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"quote\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"inAmount\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"outAmount\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"priceAfter\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint160\",\"name\":\"priceLimit\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"bool\",\"name\":\"exactIn\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"callbackData\",\"type\":\"bytes\"}],\"internalType\":\"struct PoolsharkStructs.SwapParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"swap\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"amount0Delta\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"amount1Delta\",\"type\":\"int256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"author\":\"Poolshark\",\"kind\":\"dev\",\"methods\":{\"burn((address,uint128,uint32,int24,bool,bool))\":{\"details\":\"The position will be shrunk based on the claim tick passed.The position amounts will be returned to the `to` address specified.The `sync` flag can be set to false so users can exit safely without syncing latestTick.\",\"params\":{\"params\":\"The parameters for the function. See BurnCoverParams.\"}},\"mint((address,uint128,uint32,int24,int24,bool,bytes))\":{\"details\":\"The position will be shrunk onto the correct side of latestTick.The position will be minted with the `to` address as the owner.\",\"params\":{\"params\":\"The parameters for the function. See MintCoverParams.\"}},\"quote((uint160,uint128,bool,bool))\":{\"params\":{\"params\":\"The parameters for the function. See SwapParams above.\"},\"returns\":{\"inAmount\":\" The amount of tokenIn to be spent\",\"outAmount\":\"The amount of tokenOut to be received\",\"priceAfter\":\"The Q64.96 square root price after the swap\"}},\"swap((address,uint160,uint128,bool,bool,bytes))\":{\"params\":{\"params\":\"The parameters for the function. See SwapParams above.\"},\"returns\":{\"amount0Delta\":\"The amount of token0 spent (negative) or received (positive) by the user\",\"amount1Delta\":\"The amount of token1 spent (negative) or received (positive) by the user\"}}},\"title\":\"ICoverPool\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"burn((address,uint128,uint32,int24,bool,bool))\":{\"notice\":\"Withdraws the input token and returns any filled and/or unfilled amounts to the 'to' address specified.  - E.g. User supplies 1 WETH in the range 1500 USDC per WETH to 1400 USDC per WETH As latestTick crosses from 1500 USDC per WETH to 1400 USDC per WETH, the user's liquidity within each tick spacing is auctioned off.\"},\"mint((address,uint128,uint32,int24,int24,bool,bytes))\":{\"notice\":\"Deposits `amountIn` of asset to be auctioned off each time price range is crossed further into. - E.g. User supplies 1 WETH in the range 1500 USDC per WETH to 1400 USDC per WETH As latestTick crosses from 1500 USDC per WETH to 1400 USDC per WETH, the user's liquidity within each tick spacing is auctioned off.\"},\"quote((uint160,uint128,bool,bool))\":{\"notice\":\"Quotes the amount of `tokenIn` for `tokenOut`.  `tokenIn` will be `token0` if `zeroForOne` is true. `tokenIn` will be `token1` if `zeroForOne` is false. The pool price represents token1 per token0. The pool price will decrease if `zeroForOne` is true. The pool price will increase if `zeroForOne` is false. \"},\"swap((address,uint160,uint128,bool,bool,bytes))\":{\"notice\":\"Swaps `tokenIn` for `tokenOut`.  `tokenIn` will be `token0` if `zeroForOne` is true. `tokenIn` will be `token1` if `zeroForOne` is false. The pool price represents token1 per token0. The pool price will decrease if `zeroForOne` is true. The pool price will increase if `zeroForOne` is false. \"}},\"notice\":\"Defines the basic interface for a Cover Pool.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/cover/ICoverPool.sol\":\"ICoverPool\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ICoverPool.sol\":{\"keccak256\":\"0x37be0d1b9ca12a03da3713efeb87b9015874bb04aa27ef828eb2dcf392a2d6a9\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://6b5f9279a0f7b6b04f12cf764428c8f9507545a060cbada3078146ed15a4cd18\",\"dweb:/ipfs/QmZ9JibhCtkVJhLCvVfeJcZn9To5KQtdFVZyp87j7aLdhW\"]},\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct PoolsharkStructs.BurnCoverParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint128", "name": "burnPercent", "type": "uint128"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "int24", "name": "claim", "type": "int24"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"internalType": "bool", "name": "sync", "type": "bool"}]}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "immutables", "outputs": [{"internalType": "struct PoolsharkStructs.CoverImmutables", "name": "constants", "type": "tuple", "components": [{"internalType": "contract ITwapSource", "name": "source", "type": "address"}, {"internalType": "struct PoolsharkStructs.PriceBounds", "name": "bounds", "type": "tuple", "components": [{"internalType": "uint160", "name": "min", "type": "uint160"}, {"internalType": "uint160", "name": "max", "type": "uint160"}]}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "address", "name": "poolImpl", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "address", "name": "inputPool", "type": "address"}, {"internalType": "uint128", "name": "minAmountPerAuction", "type": "uint128"}, {"internalType": "uint32", "name": "genesisTime", "type": "uint32"}, {"internalType": "int16", "name": "minPositionWidth", "type": "int16"}, {"internalType": "int16", "name": "tickSpread", "type": "int16"}, {"internalType": "uint16", "name": "twap<PERSON><PERSON>th", "type": "uint16"}, {"internalType": "uint16", "name": "auctionLength", "type": "uint16"}, {"internalType": "uint16", "name": "sampleInterval", "type": "uint16"}, {"internalType": "uint8", "name": "token0Decimals", "type": "uint8"}, {"internalType": "uint8", "name": "token1Decimals", "type": "uint8"}, {"internalType": "bool", "name": "minAmountLowerPriced", "type": "bool"}]}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.MintCoverParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"internalType": "bytes", "name": "callbackData", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [{"internalType": "struct PoolsharkStructs.QuoteParams", "name": "params", "type": "tuple", "components": [{"internalType": "uint160", "name": "priceLimit", "type": "uint160"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "bool", "name": "exactIn", "type": "bool"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}]}], "stateMutability": "view", "type": "function", "name": "quote", "outputs": [{"internalType": "int256", "name": "inAmount", "type": "int256"}, {"internalType": "int256", "name": "outAmount", "type": "int256"}, {"internalType": "uint256", "name": "priceAfter", "type": "uint256"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.SwapParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint160", "name": "priceLimit", "type": "uint160"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "bool", "name": "exactIn", "type": "bool"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"internalType": "bytes", "name": "callbackData", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "swap", "outputs": [{"internalType": "int256", "name": "amount0Delta", "type": "int256"}, {"internalType": "int256", "name": "amount1Delta", "type": "int256"}]}], "devdoc": {"kind": "dev", "methods": {"burn((address,uint128,uint32,int24,bool,bool))": {"details": "The position will be shrunk based on the claim tick passed.The position amounts will be returned to the `to` address specified.The `sync` flag can be set to false so users can exit safely without syncing latestTick.", "params": {"params": "The parameters for the function. See BurnCoverParams."}}, "mint((address,uint128,uint32,int24,int24,bool,bytes))": {"details": "The position will be shrunk onto the correct side of latestTick.The position will be minted with the `to` address as the owner.", "params": {"params": "The parameters for the function. See MintCoverParams."}}, "quote((uint160,uint128,bool,bool))": {"params": {"params": "The parameters for the function. See <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> above."}, "returns": {"inAmount": " The amount of tokenIn to be spent", "outAmount": "The amount of tokenOut to be received", "priceAfter": "The Q64.96 square root price after the swap"}}, "swap((address,uint160,uint128,bool,bool,bytes))": {"params": {"params": "The parameters for the function. See <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> above."}, "returns": {"amount0Delta": "The amount of token0 spent (negative) or received (positive) by the user", "amount1Delta": "The amount of token1 spent (negative) or received (positive) by the user"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"burn((address,uint128,uint32,int24,bool,bool))": {"notice": "Withdraws the input token and returns any filled and/or unfilled amounts to the 'to' address specified.  - E.g. User supplies 1 WETH in the range 1500 USDC per WETH to 1400 USDC per WETH As latestTick crosses from 1500 USDC per WETH to 1400 USDC per WETH, the user's liquidity within each tick spacing is auctioned off."}, "mint((address,uint128,uint32,int24,int24,bool,bytes))": {"notice": "Deposits `amountIn` of asset to be auctioned off each time price range is crossed further into. - E.g. User supplies 1 WETH in the range 1500 USDC per WETH to 1400 USDC per WETH As latestTick crosses from 1500 USDC per WETH to 1400 USDC per WETH, the user's liquidity within each tick spacing is auctioned off."}, "quote((uint160,uint128,bool,bool))": {"notice": "Quotes the amount of `tokenIn` for `tokenOut`.  `tokenIn` will be `token0` if `zeroForOne` is true. `tokenIn` will be `token1` if `zeroForOne` is false. The pool price represents token1 per token0. The pool price will decrease if `zeroForOne` is true. The pool price will increase if `zeroForOne` is false. "}, "swap((address,uint160,uint128,bool,bool,bytes))": {"notice": "Swaps `tokenIn` for `tokenOut`.  `tokenIn` will be `token0` if `zeroForOne` is true. `tokenIn` will be `token1` if `zeroForOne` is false. The pool price represents token1 per token0. The pool price will decrease if `zeroForOne` is true. The pool price will increase if `zeroForOne` is false. "}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/cover/ICoverPool.sol": "ICoverPool"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ICoverPool.sol": {"keccak256": "0x37be0d1b9ca12a03da3713efeb87b9015874bb04aa27ef828eb2dcf392a2d6a9", "urls": ["bzz-raw://6b5f9279a0f7b6b04f12cf764428c8f9507545a060cbada3078146ed15a4cd18", "dweb:/ipfs/QmZ9JibhCtkVJhLCvVfeJcZn9To5KQtdFVZyp87j7aLdhW"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}}, "version": 1}, "id": 23}