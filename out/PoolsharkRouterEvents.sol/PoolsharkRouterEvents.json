{"abi": [{"type": "event", "name": "RouterDeployed", "inputs": [{"name": "router", "type": "address", "indexed": false, "internalType": "address"}, {"name": "limitPoolFactory", "type": "address", "indexed": false, "internalType": "address"}, {"name": "coverPoolFactory", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"router\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"limitPoolFactory\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"coverPoolFactory\",\"type\":\"address\"}],\"name\":\"RouterDeployed\",\"type\":\"event\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/base/events/PoolsharkRouterEvents.sol\":\"PoolsharkRouterEvents\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/base/events/PoolsharkRouterEvents.sol\":{\"keccak256\":\"0xd5d14b6d202e7ab3ac4566f68065082d224ad472bea35bfbd2d85df73a9fbb22\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://25173808b30c7e8eab3563386f02567abfd93b74de399624256bc50313f9eb4d\",\"dweb:/ipfs/QmeJ7JU9WHBE8HAtM15rS2ms6s6ggHAFN5scNEx6AFYhfz\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "router", "type": "address", "indexed": false}, {"internalType": "address", "name": "limitPoolFactory", "type": "address", "indexed": false}, {"internalType": "address", "name": "coverPoolFactory", "type": "address", "indexed": false}], "type": "event", "name": "RouterDeployed", "anonymous": false}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/base/events/PoolsharkRouterEvents.sol": "PoolsharkRouterEvents"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/base/events/PoolsharkRouterEvents.sol": {"keccak256": "0xd5d14b6d202e7ab3ac4566f68065082d224ad472bea35bfbd2d85df73a9fbb22", "urls": ["bzz-raw://25173808b30c7e8eab3563386f02567abfd93b74de399624256bc50313f9eb4d", "dweb:/ipfs/QmeJ7JU9WHBE8HAtM15rS2ms6s6ggHAFN5scNEx6AFYhfz"], "license": "GPL-3.0-or-later"}}, "version": 1}, "id": 6}