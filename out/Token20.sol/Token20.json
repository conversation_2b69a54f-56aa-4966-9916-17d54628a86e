{"abi": [{"type": "constructor", "inputs": [{"name": "tokenName", "type": "string", "internalType": "string"}, {"name": "tokenSymbol", "type": "string", "internalType": "string"}, {"name": "decimals_", "type": "uint8", "internalType": "uint8"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "burn", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "burnFrom", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "decreaseAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "subtractedValue", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "increaseAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "addedValue", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "mint", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x60806040523480156200001157600080fd5b5060405162000e5e38038062000e5e8339810160408190526200003491620001f7565b8251839083906200004d90600390602085019062000084565b5080516200006390600490602084019062000084565b50506005805460ff191660ff939093169290921790915550620002b8915050565b82805462000092906200027c565b90600052602060002090601f016020900481019282620000b6576000855562000101565b82601f10620000d157805160ff191683800117855562000101565b8280016001018555821562000101579182015b8281111562000101578251825591602001919060010190620000e4565b506200010f92915062000113565b5090565b5b808211156200010f576000815560010162000114565b634e487b7160e01b600052604160045260246000fd5b600082601f8301126200015257600080fd5b81516001600160401b03808211156200016f576200016f6200012a565b604051601f8301601f19908116603f011681019082821181831017156200019a576200019a6200012a565b81604052838152602092508683858801011115620001b757600080fd5b600091505b83821015620001db5785820183015181830184015290820190620001bc565b83821115620001ed5760008385830101525b9695505050505050565b6000806000606084860312156200020d57600080fd5b83516001600160401b03808211156200022557600080fd5b620002338783880162000140565b945060208601519150808211156200024a57600080fd5b50620002598682870162000140565b925050604084015160ff811681146200027157600080fd5b809150509250925092565b600181811c908216806200029157607f821691505b602082108103620002b257634e487b7160e01b600052602260045260246000fd5b50919050565b610b9680620002c86000396000f3fe608060405234801561001057600080fd5b50600436106100ea5760003560e01c806342966c681161008c57806395d89b411161006657806395d89b41146101e1578063a457c2d7146101e9578063a9059cbb146101fc578063dd62ed3e1461020f57600080fd5b806342966c681461019257806370a08231146101a557806379cc6790146101ce57600080fd5b806323b872dd116100c857806323b872dd14610142578063313ce56714610155578063395093511461016a57806340c10f191461017d57600080fd5b806306fdde03146100ef578063095ea7b31461010d57806318160ddd14610130575b600080fd5b6100f7610222565b604051610104919061099c565b60405180910390f35b61012061011b366004610a0d565b6102b4565b6040519015158152602001610104565b6002545b604051908152602001610104565b610120610150366004610a37565b6102cc565b60055460405160ff9091168152602001610104565b610120610178366004610a0d565b6102f0565b61019061018b366004610a0d565b610312565b005b6101906101a0366004610a73565b610320565b6101346101b3366004610a8c565b6001600160a01b031660009081526020819052604090205490565b6101906101dc366004610a0d565b61032d565b6100f7610342565b6101206101f7366004610a0d565b610351565b61012061020a366004610a0d565b6103d1565b61013461021d366004610aae565b6103df565b60606003805461023190610ae1565b80601f016020809104026020016040519081016040528092919081815260200182805461025d90610ae1565b80156102aa5780601f1061027f576101008083540402835291602001916102aa565b820191906000526020600020905b81548152906001019060200180831161028d57829003601f168201915b5050505050905090565b6000336102c281858561040a565b5060019392505050565b6000336102da85828561052f565b6102e58585856105a9565b506001949350505050565b6000336102c281858561030383836103df565b61030d9190610b31565b61040a565b61031c8282610777565b5050565b61032a3382610856565b50565b61033882338361052f565b61031c8282610856565b60606004805461023190610ae1565b6000338161035f82866103df565b9050838110156103c45760405162461bcd60e51b815260206004820152602560248201527f45524332303a2064656372656173656420616c6c6f77616e63652062656c6f77604482015264207a65726f60d81b60648201526084015b60405180910390fd5b6102e5828686840361040a565b6000336102c28185856105a9565b6001600160a01b03918216600090815260016020908152604080832093909416825291909152205490565b6001600160a01b03831661046c5760405162461bcd60e51b8152602060048201526024808201527f45524332303a20617070726f76652066726f6d20746865207a65726f206164646044820152637265737360e01b60648201526084016103bb565b6001600160a01b0382166104cd5760405162461bcd60e51b815260206004820152602260248201527f45524332303a20617070726f766520746f20746865207a65726f206164647265604482015261737360f01b60648201526084016103bb565b6001600160a01b0383811660008181526001602090815260408083209487168084529482529182902085905590518481527f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b92591015b60405180910390a3505050565b600061053b84846103df565b905060001981146105a357818110156105965760405162461bcd60e51b815260206004820152601d60248201527f45524332303a20696e73756666696369656e7420616c6c6f77616e636500000060448201526064016103bb565b6105a3848484840361040a565b50505050565b6001600160a01b03831661060d5760405162461bcd60e51b815260206004820152602560248201527f45524332303a207472616e736665722066726f6d20746865207a65726f206164604482015264647265737360d81b60648201526084016103bb565b6001600160a01b03821661066f5760405162461bcd60e51b815260206004820152602360248201527f45524332303a207472616e7366657220746f20746865207a65726f206164647260448201526265737360e81b60648201526084016103bb565b6001600160a01b038316600090815260208190526040902054818110156106e75760405162461bcd60e51b815260206004820152602660248201527f45524332303a207472616e7366657220616d6f756e7420657863656564732062604482015265616c616e636560d01b60648201526084016103bb565b6001600160a01b0380851660009081526020819052604080822085850390559185168152908120805484929061071e908490610b31565b92505081905550826001600160a01b0316846001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef8460405161076a91815260200190565b60405180910390a36105a3565b6001600160a01b0382166107cd5760405162461bcd60e51b815260206004820152601f60248201527f45524332303a206d696e7420746f20746865207a65726f20616464726573730060448201526064016103bb565b80600260008282546107df9190610b31565b90915550506001600160a01b0382166000908152602081905260408120805483929061080c908490610b31565b90915550506040518181526001600160a01b038316906000907fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef9060200160405180910390a35050565b6001600160a01b0382166108b65760405162461bcd60e51b815260206004820152602160248201527f45524332303a206275726e2066726f6d20746865207a65726f206164647265736044820152607360f81b60648201526084016103bb565b6001600160a01b0382166000908152602081905260409020548181101561092a5760405162461bcd60e51b815260206004820152602260248201527f45524332303a206275726e20616d6f756e7420657863656564732062616c616e604482015261636560f01b60648201526084016103bb565b6001600160a01b0383166000908152602081905260408120838303905560028054849290610959908490610b49565b90915550506040518281526000906001600160a01b038516907fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef90602001610522565b600060208083528351808285015260005b818110156109c9578581018301518582016040015282016109ad565b818111156109db576000604083870101525b50601f01601f1916929092016040019392505050565b80356001600160a01b0381168114610a0857600080fd5b919050565b60008060408385031215610a2057600080fd5b610a29836109f1565b946020939093013593505050565b600080600060608486031215610a4c57600080fd5b610a55846109f1565b9250610a63602085016109f1565b9150604084013590509250925092565b600060208284031215610a8557600080fd5b5035919050565b600060208284031215610a9e57600080fd5b610aa7826109f1565b9392505050565b60008060408385031215610ac157600080fd5b610aca836109f1565b9150610ad8602084016109f1565b90509250929050565b600181811c90821680610af557607f821691505b602082108103610b1557634e487b7160e01b600052602260045260246000fd5b50919050565b634e487b7160e01b600052601160045260246000fd5b60008219821115610b4457610b44610b1b565b500190565b600082821015610b5b57610b5b610b1b565b50039056fea2646970667358221220648f54cbe7c1ef8958ae1263cad2c1aea08c549bbad552731d071d3936b9315764736f6c634300080d0033", "sourceMap": "194:437:67:-:0;;;263:179;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2044:13:72;;380:9:67;;391:11;;2044:13:72;;:5;;:13;;;;;:::i;:::-;-1:-1:-1;2067:17:72;;;;:7;;:17;;;;;:::i;:::-;-1:-1:-1;;414:9:67::1;:21:::0;;-1:-1:-1;;414:21:67::1;;::::0;;;::::1;::::0;;;::::1;::::0;;;-1:-1:-1;194:437:67;;-1:-1:-1;;194:437:67;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;194:437:67;;;-1:-1:-1;194:437:67;:::i;:::-;;;:::o;:::-;;;;;;;;;;;;;;;14:127:78;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:885;200:5;253:3;246:4;238:6;234:17;230:27;220:55;;271:1;268;261:12;220:55;294:13;;-1:-1:-1;;;;;356:10:78;;;353:36;;;369:18;;:::i;:::-;444:2;438:9;412:2;498:13;;-1:-1:-1;;494:22:78;;;518:2;490:31;486:40;474:53;;;542:18;;;562:22;;;539:46;536:72;;;588:18;;:::i;:::-;628:10;624:2;617:22;663:2;655:6;648:18;685:4;675:14;;730:3;725:2;720;712:6;708:15;704:24;701:33;698:53;;;747:1;744;737:12;698:53;769:1;760:10;;779:133;793:2;790:1;787:9;779:133;;;881:14;;;877:23;;871:30;850:14;;;846:23;;839:63;804:10;;;;779:133;;;930:2;927:1;924:9;921:80;;;989:1;984:2;979;971:6;967:15;963:24;956:35;921:80;1019:6;146:885;-1:-1:-1;;;;;;146:885:78:o;1036:712::-;1142:6;1150;1158;1211:2;1199:9;1190:7;1186:23;1182:32;1179:52;;;1227:1;1224;1217:12;1179:52;1254:16;;-1:-1:-1;;;;;1319:14:78;;;1316:34;;;1346:1;1343;1336:12;1316:34;1369:61;1422:7;1413:6;1402:9;1398:22;1369:61;:::i;:::-;1359:71;;1476:2;1465:9;1461:18;1455:25;1439:41;;1505:2;1495:8;1492:16;1489:36;;;1521:1;1518;1511:12;1489:36;;1544:63;1599:7;1588:8;1577:9;1573:24;1544:63;:::i;:::-;1534:73;;;1650:2;1639:9;1635:18;1629:25;1694:4;1687:5;1683:16;1676:5;1673:27;1663:55;;1714:1;1711;1704:12;1663:55;1737:5;1727:15;;;1036:712;;;;;:::o;1753:380::-;1832:1;1828:12;;;;1875;;;1896:61;;1950:4;1942:6;1938:17;1928:27;;1896:61;2003:2;1995:6;1992:14;1972:18;1969:38;1966:161;;2049:10;2044:3;2040:20;2037:1;2030:31;2084:4;2081:1;2074:15;2112:4;2109:1;2102:15;1966:161;;1753:380;;;:::o;:::-;194:437:67;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "194:437:67:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2156:98:72;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;4433:197;;;;;;:::i;:::-;;:::i;:::-;;;1218:14:78;;1211:22;1193:41;;1181:2;1166:18;4433:197:72;1053:187:78;3244:106:72;3331:12;;3244:106;;;1391:25:78;;;1379:2;1364:18;3244:106:72;1245:177:78;5192:286:72;;;;;;:::i;:::-;;:::i;539:90:67:-;613:9;;539:90;;613:9;;;;1902:36:78;;1890:2;1875:18;539:90:67;1760:184:78;5873:234:72;;;;;;:::i;:::-;;:::i;448:85:67:-;;;;;;:::i;:::-;;:::i;:::-;;578:89:74;;;;;;:::i;:::-;;:::i;3408:125:72:-;;;;;;:::i;:::-;-1:-1:-1;;;;;3508:18:72;3482:7;3508:18;;;;;;;;;;;;3408:125;973:161:74;;;;;;:::i;:::-;;:::i;2367:102:72:-;;;:::i;6594:427::-;;;;;;:::i;:::-;;:::i;3729:189::-;;;;;;:::i;:::-;;:::i;3976:149::-;;;;;;:::i;:::-;;:::i;2156:98::-;2210:13;2242:5;2235:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2156:98;:::o;4433:197::-;4516:4;719:10:76;4570:32:72;719:10:76;4586:7:72;4595:6;4570:8;:32::i;:::-;-1:-1:-1;4619:4:72;;4433:197;-1:-1:-1;;;4433:197:72:o;5192:286::-;5319:4;719:10:76;5375:38:72;5391:4;719:10:76;5406:6:72;5375:15;:38::i;:::-;5423:27;5433:4;5439:2;5443:6;5423:9;:27::i;:::-;-1:-1:-1;5467:4:72;;5192:286;-1:-1:-1;;;;5192:286:72:o;5873:234::-;5961:4;719:10:76;6015:64:72;719:10:76;6031:7:72;6068:10;6040:25;719:10:76;6031:7:72;6040:9;:25::i;:::-;:38;;;;:::i;:::-;6015:8;:64::i;448:85:67:-;509:17;515:2;519:6;509:5;:17::i;:::-;448:85;;:::o;578:89:74:-;633:27;719:10:76;653:6:74;633:5;:27::i;:::-;578:89;:::o;973:161::-;1049:46;1065:7;719:10:76;1088:6:74;1049:15;:46::i;:::-;1105:22;1111:7;1120:6;1105:5;:22::i;2367:102:72:-;2423:13;2455:7;2448:14;;;;;:::i;6594:427::-;6687:4;719:10:76;6687:4:72;6768:25;719:10:76;6785:7:72;6768:9;:25::i;:::-;6741:52;;6831:15;6811:16;:35;;6803:85;;;;-1:-1:-1;;;6803:85:72;;3442:2:78;6803:85:72;;;3424:21:78;3481:2;3461:18;;;3454:30;3520:34;3500:18;;;3493:62;-1:-1:-1;;;3571:18:78;;;3564:35;3616:19;;6803:85:72;;;;;;;;;6922:60;6931:5;6938:7;6966:15;6947:16;:34;6922:8;:60::i;3729:189::-;3808:4;719:10:76;3862:28:72;719:10:76;3879:2:72;3883:6;3862:9;:28::i;3976:149::-;-1:-1:-1;;;;;4091:18:72;;;4065:7;4091:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;;;;3976:149::o;10110:370::-;-1:-1:-1;;;;;10241:19:72;;10233:68;;;;-1:-1:-1;;;10233:68:72;;3848:2:78;10233:68:72;;;3830:21:78;3887:2;3867:18;;;3860:30;3926:34;3906:18;;;3899:62;-1:-1:-1;;;3977:18:78;;;3970:34;4021:19;;10233:68:72;3646:400:78;10233:68:72;-1:-1:-1;;;;;10319:21:72;;10311:68;;;;-1:-1:-1;;;10311:68:72;;4253:2:78;10311:68:72;;;4235:21:78;4292:2;4272:18;;;4265:30;4331:34;4311:18;;;4304:62;-1:-1:-1;;;4382:18:78;;;4375:32;4424:19;;10311:68:72;4051:398:78;10311:68:72;-1:-1:-1;;;;;10390:18:72;;;;;;;:11;:18;;;;;;;;:27;;;;;;;;;;;;;:36;;;10441:32;;1391:25:78;;;10441:32:72;;1364:18:78;10441:32:72;;;;;;;;10110:370;;;:::o;10761:441::-;10891:24;10918:25;10928:5;10935:7;10918:9;:25::i;:::-;10891:52;;-1:-1:-1;;10957:16:72;:37;10953:243;;11038:6;11018:16;:26;;11010:68;;;;-1:-1:-1;;;11010:68:72;;4656:2:78;11010:68:72;;;4638:21:78;4695:2;4675:18;;;4668:30;4734:31;4714:18;;;4707:59;4783:18;;11010:68:72;4454:353:78;11010:68:72;11120:51;11129:5;11136:7;11164:6;11145:16;:25;11120:8;:51::i;:::-;10881:321;10761:441;;;:::o;7475:651::-;-1:-1:-1;;;;;7601:18:72;;7593:68;;;;-1:-1:-1;;;7593:68:72;;5014:2:78;7593:68:72;;;4996:21:78;5053:2;5033:18;;;5026:30;5092:34;5072:18;;;5065:62;-1:-1:-1;;;5143:18:78;;;5136:35;5188:19;;7593:68:72;4812:401:78;7593:68:72;-1:-1:-1;;;;;7679:16:72;;7671:64;;;;-1:-1:-1;;;7671:64:72;;5420:2:78;7671:64:72;;;5402:21:78;5459:2;5439:18;;;5432:30;5498:34;5478:18;;;5471:62;-1:-1:-1;;;5549:18:78;;;5542:33;5592:19;;7671:64:72;5218:399:78;7671:64:72;-1:-1:-1;;;;;7817:15:72;;7795:19;7817:15;;;;;;;;;;;7850:21;;;;7842:72;;;;-1:-1:-1;;;7842:72:72;;5824:2:78;7842:72:72;;;5806:21:78;5863:2;5843:18;;;5836:30;5902:34;5882:18;;;5875:62;-1:-1:-1;;;5953:18:78;;;5946:36;5999:19;;7842:72:72;5622:402:78;7842:72:72;-1:-1:-1;;;;;7948:15:72;;;:9;:15;;;;;;;;;;;7966:20;;;7948:38;;8006:13;;;;;;;;:23;;7980:6;;7948:9;8006:23;;7980:6;;8006:23;:::i;:::-;;;;;;;;8060:2;-1:-1:-1;;;;;8045:26:72;8054:4;-1:-1:-1;;;;;8045:26:72;;8064:6;8045:26;;;;1391:25:78;;1379:2;1364:18;;1245:177;8045:26:72;;;;;;;;8082:37;9111:576;8402:389;-1:-1:-1;;;;;8485:21:72;;8477:65;;;;-1:-1:-1;;;8477:65:72;;6231:2:78;8477:65:72;;;6213:21:78;6270:2;6250:18;;;6243:30;6309:33;6289:18;;;6282:61;6360:18;;8477:65:72;6029:355:78;8477:65:72;8629:6;8613:12;;:22;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;;;8645:18:72;;:9;:18;;;;;;;;;;:28;;8667:6;;8645:9;:28;;8667:6;;8645:28;:::i;:::-;;;;-1:-1:-1;;8688:37:72;;1391:25:78;;;-1:-1:-1;;;;;8688:37:72;;;8705:1;;8688:37;;1379:2:78;1364:18;8688:37:72;;;;;;;448:85:67;;:::o;9111:576:72:-;-1:-1:-1;;;;;9194:21:72;;9186:67;;;;-1:-1:-1;;;9186:67:72;;6591:2:78;9186:67:72;;;6573:21:78;6630:2;6610:18;;;6603:30;6669:34;6649:18;;;6642:62;-1:-1:-1;;;6720:18:78;;;6713:31;6761:19;;9186:67:72;6389:397:78;9186:67:72;-1:-1:-1;;;;;9349:18:72;;9324:22;9349:18;;;;;;;;;;;9385:24;;;;9377:71;;;;-1:-1:-1;;;9377:71:72;;6993:2:78;9377:71:72;;;6975:21:78;7032:2;7012:18;;;7005:30;7071:34;7051:18;;;7044:62;-1:-1:-1;;;7122:18:78;;;7115:32;7164:19;;9377:71:72;6791:398:78;9377:71:72;-1:-1:-1;;;;;9482:18:72;;:9;:18;;;;;;;;;;9503:23;;;9482:44;;9546:12;:22;;9520:6;;9482:9;9546:22;;9520:6;;9546:22;:::i;:::-;;;;-1:-1:-1;;9584:37:72;;1391:25:78;;;9610:1:72;;-1:-1:-1;;;;;9584:37:72;;;;;1379:2:78;1364:18;9584:37:72;1245:177:78;14:597;126:4;155:2;184;173:9;166:21;216:6;210:13;259:6;254:2;243:9;239:18;232:34;284:1;294:140;308:6;305:1;302:13;294:140;;;403:14;;;399:23;;393:30;369:17;;;388:2;365:26;358:66;323:10;;294:140;;;452:6;449:1;446:13;443:91;;;522:1;517:2;508:6;497:9;493:22;489:31;482:42;443:91;-1:-1:-1;595:2:78;574:15;-1:-1:-1;;570:29:78;555:45;;;;602:2;551:54;;14:597;-1:-1:-1;;;14:597:78:o;616:173::-;684:20;;-1:-1:-1;;;;;733:31:78;;723:42;;713:70;;779:1;776;769:12;713:70;616:173;;;:::o;794:254::-;862:6;870;923:2;911:9;902:7;898:23;894:32;891:52;;;939:1;936;929:12;891:52;962:29;981:9;962:29;:::i;:::-;952:39;1038:2;1023:18;;;;1010:32;;-1:-1:-1;;;794:254:78:o;1427:328::-;1504:6;1512;1520;1573:2;1561:9;1552:7;1548:23;1544:32;1541:52;;;1589:1;1586;1579:12;1541:52;1612:29;1631:9;1612:29;:::i;:::-;1602:39;;1660:38;1694:2;1683:9;1679:18;1660:38;:::i;:::-;1650:48;;1745:2;1734:9;1730:18;1717:32;1707:42;;1427:328;;;;;:::o;1949:180::-;2008:6;2061:2;2049:9;2040:7;2036:23;2032:32;2029:52;;;2077:1;2074;2067:12;2029:52;-1:-1:-1;2100:23:78;;1949:180;-1:-1:-1;1949:180:78:o;2134:186::-;2193:6;2246:2;2234:9;2225:7;2221:23;2217:32;2214:52;;;2262:1;2259;2252:12;2214:52;2285:29;2304:9;2285:29;:::i;:::-;2275:39;2134:186;-1:-1:-1;;;2134:186:78:o;2325:260::-;2393:6;2401;2454:2;2442:9;2433:7;2429:23;2425:32;2422:52;;;2470:1;2467;2460:12;2422:52;2493:29;2512:9;2493:29;:::i;:::-;2483:39;;2541:38;2575:2;2564:9;2560:18;2541:38;:::i;:::-;2531:48;;2325:260;;;;;:::o;2590:380::-;2669:1;2665:12;;;;2712;;;2733:61;;2787:4;2779:6;2775:17;2765:27;;2733:61;2840:2;2832:6;2829:14;2809:18;2806:38;2803:161;;2886:10;2881:3;2877:20;2874:1;2867:31;2921:4;2918:1;2911:15;2949:4;2946:1;2939:15;2803:161;;2590:380;;;:::o;2975:127::-;3036:10;3031:3;3027:20;3024:1;3017:31;3067:4;3064:1;3057:15;3091:4;3088:1;3081:15;3107:128;3147:3;3178:1;3174:6;3171:1;3168:13;3165:39;;;3184:18;;:::i;:::-;-1:-1:-1;3220:9:78;;3107:128::o;7194:125::-;7234:4;7262:1;7259;7256:8;7253:34;;;7267:18;;:::i;:::-;-1:-1:-1;7304:9:78;;7194:125::o", "linkReferences": {}}, "methodIdentifiers": {"allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "burn(uint256)": "42966c68", "burnFrom(address,uint256)": "79cc6790", "decimals()": "313ce567", "decreaseAllowance(address,uint256)": "a457c2d7", "increaseAllowance(address,uint256)": "39509351", "mint(address,uint256)": "40c10f19", "name()": "06fdde03", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"tokenName\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"tokenSymbol\",\"type\":\"string\"},{\"internalType\":\"uint8\",\"name\":\"decimals_\",\"type\":\"uint8\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"burnFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"subtractedValue\",\"type\":\"uint256\"}],\"name\":\"decreaseAllowance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"addedValue\",\"type\":\"uint256\"}],\"name\":\"increaseAllowance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"See {IERC20-allowance}.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"See {IERC20-balanceOf}.\"},\"burn(uint256)\":{\"details\":\"Destroys `amount` tokens from the caller. See {ERC20-_burn}.\"},\"burnFrom(address,uint256)\":{\"details\":\"Destroys `amount` tokens from `account`, deducting from the caller's allowance. See {ERC20-_burn} and {ERC20-allowance}. Requirements: - the caller must have allowance for ``accounts``'s tokens of at least `amount`.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the value {ERC20} uses, unless this function is overridden; NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"decreaseAllowance(address,uint256)\":{\"details\":\"Atomically decreases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address. - `spender` must have allowance for the caller of at least `subtractedValue`.\"},\"increaseAllowance(address,uint256)\":{\"details\":\"Atomically increases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"See {IERC20-totalSupply}.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `amount`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `amount`. - the caller must have allowance for ``from``'s tokens of at least `amount`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/test/Token20.sol\":\"Token20\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/test/Token20.sol\":{\"keccak256\":\"0xff2aff0eba78800423a173775af54f201db93bf38ca3b76da491b75aab2fcf2c\",\"license\":\"Unlicense\",\"urls\":[\"bzz-raw://8e9a50094aa297acf26dace75573e31dd105146aeb2cfefc2b5d5f4820c83faa\",\"dweb:/ipfs/Qmb7eu25KoHWL5x4dpqCAt5zXiSsWn5RfCeFy8FQQzGR1R\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0\",\"dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34\",\"dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"keccak256\":\"0x0d19410453cda55960a818e02bd7c18952a5c8fe7a3036e81f0d599f34487a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4c0f62d3d5bef22b5ca00cc3903e7de6152cb68d2d22401a463f373cda54c00f\",\"dweb:/ipfs/QmSfzjZux7LC7NW2f7rjCXTHeFMUCWERqDkhpCTBy7kxTe\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd\",\"dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92\",\"dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "tokenName", "type": "string"}, {"internalType": "string", "name": "tokenSymbol", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burnFrom"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"allowance(address,address)": {"details": "See {IERC20-allowance}."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "See {IERC20-balanceOf}."}, "burn(uint256)": {"details": "Destroys `amount` tokens from the caller. See {ERC20-_burn}."}, "burnFrom(address,uint256)": {"details": "Destroys `amount` tokens from `account`, deducting from the caller's allowance. See {ERC20-_burn} and {ERC20-allowance}. Requirements: - the caller must have allowance for ``accounts``'s tokens of at least `amount`."}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>. This is the value {ERC20} uses, unless this function is overridden; NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "decreaseAllowance(address,uint256)": {"details": "Atomically decreases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address. - `spender` must have allowance for the caller of at least `subtractedValue`."}, "increaseAllowance(address,uint256)": {"details": "Atomically increases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address."}, "name()": {"details": "Returns the name of the token."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"details": "See {IERC20-totalSupply}."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `amount`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `amount`. - the caller must have allowance for ``from``'s tokens of at least `amount`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/test/Token20.sol": "Token20"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/test/Token20.sol": {"keccak256": "0xff2aff0eba78800423a173775af54f201db93bf38ca3b76da491b75aab2fcf2c", "urls": ["bzz-raw://8e9a50094aa297acf26dace75573e31dd105146aeb2cfefc2b5d5f4820c83faa", "dweb:/ipfs/Qmb7eu25KoHWL5x4dpqCAt5zXiSsWn5RfCeFy8FQQzGR1R"], "license": "Unlicense"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238", "urls": ["bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0", "dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b", "urls": ["bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34", "dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"keccak256": "0x0d19410453cda55960a818e02bd7c18952a5c8fe7a3036e81f0d599f34487a7b", "urls": ["bzz-raw://4c0f62d3d5bef22b5ca00cc3903e7de6152cb68d2d22401a463f373cda54c00f", "dweb:/ipfs/QmSfzjZux7LC7NW2f7rjCXTHeFMUCWERqDkhpCTBy7kxTe"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca", "urls": ["bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd", "dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7", "urls": ["bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92", "dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3"], "license": "MIT"}}, "version": 1}, "id": 67}