{"abi": [{"type": "event", "name": "PoolCreated", "inputs": [{"name": "pool", "type": "address", "indexed": false, "internalType": "address"}, {"name": "token", "type": "address", "indexed": false, "internalType": "address"}, {"name": "token0", "type": "address", "indexed": true, "internalType": "address"}, {"name": "token1", "type": "address", "indexed": true, "internalType": "address"}, {"name": "swapFee", "type": "uint16", "indexed": true, "internalType": "uint16"}, {"name": "tickSpacing", "type": "int16", "indexed": false, "internalType": "int16"}, {"name": "poolTypeId", "type": "uint16", "indexed": false, "internalType": "uint16"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"poolTypeId\",\"type\":\"uint16\"}],\"name\":\"PoolCreated\",\"type\":\"event\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/base/events/LimitPoolFactoryEvents.sol\":\"LimitPoolFactoryEvents\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/base/events/LimitPoolFactoryEvents.sol\":{\"keccak256\":\"0x35cab619b1e4aabd39e6469fed087e07da1c44c9eb527508d11217b4edc396ea\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://b4a6de023a8d68691505f42745947e5aa833a4b5a20c53e7dfea5615db25e9ad\",\"dweb:/ipfs/QmWQ4CxBiWiX6SZMZ5Cefuhw69ANYddsmwgia2vpaBAQke\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "pool", "type": "address", "indexed": false}, {"internalType": "address", "name": "token", "type": "address", "indexed": false}, {"internalType": "address", "name": "token0", "type": "address", "indexed": true}, {"internalType": "address", "name": "token1", "type": "address", "indexed": true}, {"internalType": "uint16", "name": "swapFee", "type": "uint16", "indexed": true}, {"internalType": "int16", "name": "tickSpacing", "type": "int16", "indexed": false}, {"internalType": "uint16", "name": "poolTypeId", "type": "uint16", "indexed": false}], "type": "event", "name": "PoolCreated", "anonymous": false}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/base/events/LimitPoolFactoryEvents.sol": "LimitPoolFactoryEvents"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/base/events/LimitPoolFactoryEvents.sol": {"keccak256": "0x35cab619b1e4aabd39e6469fed087e07da1c44c9eb527508d11217b4edc396ea", "urls": ["bzz-raw://b4a6de023a8d68691505f42745947e5aa833a4b5a20c53e7dfea5615db25e9ad", "dweb:/ipfs/QmWQ4CxBiWiX6SZMZ5Cefuhw69ANYddsmwgia2vpaBAQke"], "license": "GPL-3.0-or-later"}}, "version": 1}, "id": 4}