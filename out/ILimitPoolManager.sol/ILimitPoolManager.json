{"abi": [{"type": "function", "name": "feeTiers", "inputs": [{"name": "swapFee", "type": "uint16", "internalType": "uint16"}], "outputs": [{"name": "tickSpacing", "type": "int16", "internalType": "int16"}], "stateMutability": "view"}, {"type": "function", "name": "feeTo", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "poolTypes", "inputs": [{"name": "poolType", "type": "uint16", "internalType": "uint16"}], "outputs": [{"name": "poolImpl", "type": "address", "internalType": "address"}, {"name": "tokenImpl", "type": "address", "internalType": "address"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"feeTiers(uint16)": "bc5093da", "feeTo()": "017e7e58", "owner()": "8da5cb5b", "poolTypes(uint16)": "b1947a19"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"}],\"name\":\"feeTiers\",\"outputs\":[{\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feeTo\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"poolType\",\"type\":\"uint16\"}],\"name\":\"poolTypes\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenImpl\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"LimitPoolManager interface\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/limit/ILimitPoolManager.sol\":\"ILimitPoolManager\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/limit/ILimitPoolManager.sol\":{\"keccak256\":\"0x3569f41d49e23238d34b3dc91e7fe7587c8fa141cacc97bf382caa1b580837de\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://aa5545c53994d586c1e7e6aaeb7d0522a678ae012867268e93e3f65060e7360f\",\"dweb:/ipfs/QmU34ZDnYxCDV6i8fxq13AU5LptW3rXgY5ZYpLy2t8sLqk\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint16", "name": "swapFee", "type": "uint16"}], "stateMutability": "view", "type": "function", "name": "feeTiers", "outputs": [{"internalType": "int16", "name": "tickSpacing", "type": "int16"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feeTo", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint16", "name": "poolType", "type": "uint16"}], "stateMutability": "view", "type": "function", "name": "poolTypes", "outputs": [{"internalType": "address", "name": "poolImpl", "type": "address"}, {"internalType": "address", "name": "tokenImpl", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/limit/ILimitPoolManager.sol": "ILimitPoolManager"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/limit/ILimitPoolManager.sol": {"keccak256": "0x3569f41d49e23238d34b3dc91e7fe7587c8fa141cacc97bf382caa1b580837de", "urls": ["bzz-raw://aa5545c53994d586c1e7e6aaeb7d0522a678ae012867268e93e3f65060e7360f", "dweb:/ipfs/QmU34ZDnYxCDV6i8fxq13AU5LptW3rXgY5ZYpLy2t8sLqk"], "license": "GPL-3.0-or-later"}}, "version": 1}, "id": 28}