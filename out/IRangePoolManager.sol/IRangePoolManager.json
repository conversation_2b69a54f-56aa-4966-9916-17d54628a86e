{"abi": [{"type": "function", "name": "feeTiers", "inputs": [{"name": "swapFee", "type": "uint16", "internalType": "uint16"}], "outputs": [{"name": "", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "feeTo", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "protocolFees", "inputs": [{"name": "pool", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"feeTiers(uint16)": "bc5093da", "feeTo()": "017e7e58", "owner()": "8da5cb5b", "protocolFees(address)": "dcf844a7"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"}],\"name\":\"feeTiers\",\"outputs\":[{\"internalType\":\"int24\",\"name\":\"\",\"type\":\"int24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feeTo\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"}],\"name\":\"protocolFees\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/range/IRangePoolManager.sol\":\"IRangePoolManager\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/range/IRangePoolManager.sol\":{\"keccak256\":\"0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065\",\"dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/interfaces/structs/RangePoolStructs.sol\":{\"keccak256\":\"0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9\",\"dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint16", "name": "swapFee", "type": "uint16"}], "stateMutability": "view", "type": "function", "name": "feeTiers", "outputs": [{"internalType": "int24", "name": "", "type": "int24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feeTo", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "pool", "type": "address"}], "stateMutability": "view", "type": "function", "name": "protocolFees", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/range/IRangePoolManager.sol": "IRangePoolManager"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePoolManager.sol": {"keccak256": "0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139", "urls": ["bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065", "dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/interfaces/structs/RangePoolStructs.sol": {"keccak256": "0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd", "urls": ["bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9", "dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx"], "license": "GPLv3"}}, "version": 1}, "id": 33}