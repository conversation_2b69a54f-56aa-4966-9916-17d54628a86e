{"abi": [{"type": "event", "name": "FeeToTransfer", "inputs": [{"name": "previousFeeTo", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newFeeTo", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnerTransfer", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "StakeFin", "inputs": [{"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "StakeFinAccrued", "inputs": [{"name": "owner", "type": "address", "indexed": false, "internalType": "address"}, {"name": "stakingPointsAccrued", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "UnstakeFin", "inputs": [{"name": "pool", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousFeeTo\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newFeeTo\",\"type\":\"address\"}],\"name\":\"FeeToTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnerTransfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"StakeFin\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"stakingPointsAccrued\",\"type\":\"uint128\"}],\"name\":\"StakeFinAccrued\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"UnstakeFin\",\"type\":\"event\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/base/events/FinStakerEvents.sol\":\"FinStakerEvents\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/base/events/FinStakerEvents.sol\":{\"keccak256\":\"0xfc6f6e3ca3354f2d96a1e8db10005e99b521f2364e7b5f569f5871d3b4caaf36\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://a525534fd0d337e7404d6d4bae8f0ac2853d289691b3301a080efd41c1fbc8bd\",\"dweb:/ipfs/QmTPE4sDXYjHGmtBQFiteTWhCYtPhRChZVUAi1KXMt9dnM\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "previousFeeTo", "type": "address", "indexed": true}, {"internalType": "address", "name": "newFeeTo", "type": "address", "indexed": true}], "type": "event", "name": "FeeToTransfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnerTransfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "StakeFin", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": false}, {"internalType": "uint128", "name": "stakingPointsAccrued", "type": "uint128", "indexed": false}], "type": "event", "name": "StakeFinAccrued", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "pool", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": false}], "type": "event", "name": "UnstakeFin", "anonymous": false}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/base/events/FinStakerEvents.sol": "FinStakerEvents"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/base/events/FinStakerEvents.sol": {"keccak256": "0xfc6f6e3ca3354f2d96a1e8db10005e99b521f2364e7b5f569f5871d3b4caaf36", "urls": ["bzz-raw://a525534fd0d337e7404d6d4bae8f0ac2853d289691b3301a080efd41c1fbc8bd", "dweb:/ipfs/QmTPE4sDXYjHGmtBQFiteTWhCYtPhRChZVUAi1KXMt9dnM"], "license": "GPL-3.0-or-later"}}, "version": 1}, "id": 2}