{"abi": [{"type": "function", "name": "burnLimit", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.BurnLimitParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "burnPercent", "type": "uint128", "internalType": "uint128"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "claim", "type": "int24", "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "fees", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.FeesParams", "components": [{"name": "protocolSwapFee0", "type": "uint16", "internalType": "uint16"}, {"name": "protocolSwapFee1", "type": "uint16", "internalType": "uint16"}, {"name": "protocolFillFee0", "type": "uint16", "internalType": "uint16"}, {"name": "protocolFillFee1", "type": "uint16", "internalType": "uint16"}, {"name": "set<PERSON><PERSON><PERSON><PERSON>s", "type": "uint8", "internalType": "uint8"}]}], "outputs": [{"name": "token0Fees", "type": "uint128", "internalType": "uint128"}, {"name": "token1Fees", "type": "uint128", "internalType": "uint128"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "startPrice", "type": "uint160", "internalType": "uint160"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mintLimit", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.MintLimitParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}, {"name": "mintPercent", "type": "uint96", "internalType": "uint96"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}, {"name": "callbackData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"burnLimit((address,uint128,uint32,int24,bool))": "63aca53c", "fees((uint16,uint16,uint16,uint16,uint8))": "810b1d09", "initialize(uint160)": "f637731d", "mintLimit((address,uint128,uint96,uint32,int24,int24,bool,bytes))": "270ffd7d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"burnPercent\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"claim\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.BurnLimitParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"burnLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee0\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee1\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee0\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee1\",\"type\":\"uint16\"},{\"internalType\":\"uint8\",\"name\":\"setFeesFlags\",\"type\":\"uint8\"}],\"internalType\":\"struct PoolsharkStructs.FeesParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"fees\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"token0Fees\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"token1Fees\",\"type\":\"uint128\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint160\",\"name\":\"startPrice\",\"type\":\"uint160\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"uint96\",\"name\":\"mintPercent\",\"type\":\"uint96\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"callbackData\",\"type\":\"bytes\"}],\"internalType\":\"struct PoolsharkStructs.MintLimitParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"mintLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/limit/ILimitPool.sol\":\"ILimitPool\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/limit/ILimitPool.sol\":{\"keccak256\":\"0x8754512ae636a8871b11412ad16735be460142a48ee9f0fc50310f4d4aa45227\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://9621227f4ab660043534cb124559d96d42cefad3441b61a7a82be81ef8674dcf\",\"dweb:/ipfs/QmYZsFqXwFW6dcYSrjc4R1Lu1r9MbMbo6EQH6Rk2yqztKA\"]},\"contracts/interfaces/structs/LimitPoolStructs.sol\":{\"keccak256\":\"0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef\",\"dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct PoolsharkStructs.BurnLimitParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint128", "name": "burnPercent", "type": "uint128"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "int24", "name": "claim", "type": "int24"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}]}], "stateMutability": "nonpayable", "type": "function", "name": "burnLimit"}, {"inputs": [{"internalType": "struct PoolsharkStructs.FeesParams", "name": "params", "type": "tuple", "components": [{"internalType": "uint16", "name": "protocolSwapFee0", "type": "uint16"}, {"internalType": "uint16", "name": "protocolSwapFee1", "type": "uint16"}, {"internalType": "uint16", "name": "protocolFillFee0", "type": "uint16"}, {"internalType": "uint16", "name": "protocolFillFee1", "type": "uint16"}, {"internalType": "uint8", "name": "set<PERSON><PERSON><PERSON><PERSON>s", "type": "uint8"}]}], "stateMutability": "nonpayable", "type": "function", "name": "fees", "outputs": [{"internalType": "uint128", "name": "token0Fees", "type": "uint128"}, {"internalType": "uint128", "name": "token1Fees", "type": "uint128"}]}, {"inputs": [{"internalType": "uint160", "name": "startPrice", "type": "uint160"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "struct PoolsharkStructs.MintLimitParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint96", "name": "mintPercent", "type": "uint96"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"internalType": "bytes", "name": "callbackData", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "mintLimit"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/limit/ILimitPool.sol": "ILimitPool"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPool.sol": {"keccak256": "0x8754512ae636a8871b11412ad16735be460142a48ee9f0fc50310f4d4aa45227", "urls": ["bzz-raw://9621227f4ab660043534cb124559d96d42cefad3441b61a7a82be81ef8674dcf", "dweb:/ipfs/QmYZsFqXwFW6dcYSrjc4R1Lu1r9MbMbo6EQH6Rk2yqztKA"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/LimitPoolStructs.sol": {"keccak256": "0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4", "urls": ["bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef", "dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}}, "version": 1}, "id": 26}