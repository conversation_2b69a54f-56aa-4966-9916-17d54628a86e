{"abi": [], "bytecode": {"object": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212206179ef7fee692612c080dd0ac171411d9bff6c5d23b8bf6773047673cf0dc99d64736f6c634300080d0033", "sourceMap": "335:5527:56:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;335:5527:56;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212206179ef7fee692612c080dd0ac171411d9bff6c5d23b8bf6773047673cf0dc99d64736f6c634300080d0033", "sourceMap": "335:5527:56:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Math library that facilitates fee handling.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/range/math/FeeMath.sol\":\"FeeMath\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/range/IRangePool.sol\":{\"keccak256\":\"0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab\",\"dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt\"]},\"contracts/interfaces/range/IRangePoolManager.sol\":{\"keccak256\":\"0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065\",\"dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP\"]},\"contracts/interfaces/structs/LimitPoolStructs.sol\":{\"keccak256\":\"0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef\",\"dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/interfaces/structs/RangePoolStructs.sol\":{\"keccak256\":\"0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9\",\"dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx\"]},\"contracts/libraries/Samples.sol\":{\"keccak256\":\"0xae162c0e142bbe444c3748135d74bf272849768abca62c1908794bb72551c0eb\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://cf38a14a2a9653dc9b44ad37f091b7c6117be6ca8a76b5f9e279998bd9060763\",\"dweb:/ipfs/QmYxJm1w9YL4bYHJqNcLTRxiwYKH68dTzNGGQiQxFmnADE\"]},\"contracts/libraries/math/ConstantProduct.sol\":{\"keccak256\":\"0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8\",\"dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED\"]},\"contracts/libraries/math/OverflowMath.sol\":{\"keccak256\":\"0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29\",\"dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T\"]},\"contracts/libraries/range/math/FeeMath.sol\":{\"keccak256\":\"0x81c976abdc41c8af4e3276741b1824157d82eb60eca512dee17a28539832df4f\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://7435150736fdc891d7c503adc4d027334045d82e67d3112edc30f6dc41892c49\",\"dweb:/ipfs/QmTNAoETKtyBSmq2PaPcFQdxG8VhA56EHfPc3WpeRLPKHH\"]},\"contracts/libraries/utils/SafeCast.sol\":{\"keccak256\":\"0xc4ec846404c1111c6353c8b2251469e07f32570b1ffafc58701c385c458c21e2\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://51b7397ff450c2aedafdcb40a6ca181c8e1947fb409dbbcd8b000b8eee2e9fcf\",\"dweb:/ipfs/QmQ3wPdCtuLreoEkvFoifsx4drC5kT3p2CwGKBBn2qQso4\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/range/math/FeeMath.sol": "<PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePool.sol": {"keccak256": "0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf", "urls": ["bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab", "dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePoolManager.sol": {"keccak256": "0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139", "urls": ["bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065", "dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/LimitPoolStructs.sol": {"keccak256": "0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4", "urls": ["bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef", "dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/interfaces/structs/RangePoolStructs.sol": {"keccak256": "0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd", "urls": ["bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9", "dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx"], "license": "GPLv3"}, "contracts/libraries/Samples.sol": {"keccak256": "0xae162c0e142bbe444c3748135d74bf272849768abca62c1908794bb72551c0eb", "urls": ["bzz-raw://cf38a14a2a9653dc9b44ad37f091b7c6117be6ca8a76b5f9e279998bd9060763", "dweb:/ipfs/QmYxJm1w9YL4bYHJqNcLTRxiwYKH68dTzNGGQiQxFmnADE"], "license": "GPLv3"}, "contracts/libraries/math/ConstantProduct.sol": {"keccak256": "0x1ba5d89419f77c8a58645d5a6a7b376c58c32819b8395211f674f41e265971d3", "urls": ["bzz-raw://02bd3418f875cf360adb7154b89a78e75bca1a6347504cc48dd729744fcb5cb8", "dweb:/ipfs/QmRzDf67jhE7GkdaBiyuBqdsFLexU5vNdDZsmk85XHrEED"], "license": "GPL-3.0-or-later"}, "contracts/libraries/math/OverflowMath.sol": {"keccak256": "0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b", "urls": ["bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29", "dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T"], "license": "MIT"}, "contracts/libraries/range/math/FeeMath.sol": {"keccak256": "0x81c976abdc41c8af4e3276741b1824157d82eb60eca512dee17a28539832df4f", "urls": ["bzz-raw://7435150736fdc891d7c503adc4d027334045d82e67d3112edc30f6dc41892c49", "dweb:/ipfs/QmTNAoETKtyBSmq2PaPcFQdxG8VhA56EHfPc3WpeRLPKHH"], "license": "GPLv3"}, "contracts/libraries/utils/SafeCast.sol": {"keccak256": "0xc4ec846404c1111c6353c8b2251469e07f32570b1ffafc58701c385c458c21e2", "urls": ["bzz-raw://51b7397ff450c2aedafdcb40a6ca181c8e1947fb409dbbcd8b000b8eee2e9fcf", "dweb:/ipfs/QmQ3wPdCtuLreoEkvFoifsx4drC5kT3p2CwGKBBn2qQso4"], "license": "GPL-2.0-or-later"}}, "version": 1}, "id": 56}