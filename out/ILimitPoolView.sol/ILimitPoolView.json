{"abi": [{"type": "function", "name": "immutables", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitImmutables", "components": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "poolImpl", "type": "address", "internalType": "address"}, {"name": "factory", "type": "address", "internalType": "address"}, {"name": "bounds", "type": "tuple", "internalType": "struct PoolsharkStructs.PriceBounds", "components": [{"name": "min", "type": "uint160", "internalType": "uint160"}, {"name": "max", "type": "uint160", "internalType": "uint160"}]}, {"name": "token0", "type": "address", "internalType": "address"}, {"name": "token1", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "genesisTime", "type": "uint32", "internalType": "uint32"}, {"name": "tickSpacing", "type": "int16", "internalType": "int16"}, {"name": "swapFee", "type": "uint16", "internalType": "uint16"}]}], "stateMutability": "view"}, {"type": "function", "name": "priceBounds", "inputs": [{"name": "tickSpacing", "type": "int16", "internalType": "int16"}], "outputs": [{"name": "minPrice", "type": "uint160", "internalType": "uint160"}, {"name": "maxPrice", "type": "uint160", "internalType": "uint160"}], "stateMutability": "pure"}, {"type": "function", "name": "sample", "inputs": [{"name": "secondsAgo", "type": "uint32[]", "internalType": "uint32[]"}], "outputs": [{"name": "tickSecondsAccum", "type": "int56[]", "internalType": "int56[]"}, {"name": "secondsPerLiquidityAccum", "type": "uint160[]", "internalType": "uint160[]"}, {"name": "averagePrice", "type": "uint160", "internalType": "uint160"}, {"name": "averageLiquidity", "type": "uint128", "internalType": "uint128"}, {"name": "averageTick", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "snapshotLimit", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.SnapshotLimitParams", "components": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "burnPercent", "type": "uint128", "internalType": "uint128"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "claim", "type": "int24", "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}]}], "outputs": [{"name": "", "type": "uint128", "internalType": "uint128"}, {"name": "", "type": "uint128", "internalType": "uint128"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"immutables()": "fdf53665", "priceBounds(int16)": "240dda1f", "sample(uint32[])": "f62167fc", "snapshotLimit((address,uint128,uint32,int24,bool))": "8a5ee2c3"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"immutables\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"factory\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"min\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"max\",\"type\":\"uint160\"}],\"internalType\":\"struct PoolsharkStructs.PriceBounds\",\"name\":\"bounds\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"genesisTime\",\"type\":\"uint32\"},{\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.LimitImmutables\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"}],\"name\":\"priceBounds\",\"outputs\":[{\"internalType\":\"uint160\",\"name\":\"minPrice\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"maxPrice\",\"type\":\"uint160\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32[]\",\"name\":\"secondsAgo\",\"type\":\"uint32[]\"}],\"name\":\"sample\",\"outputs\":[{\"internalType\":\"int56[]\",\"name\":\"tickSecondsAccum\",\"type\":\"int56[]\"},{\"internalType\":\"uint160[]\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160[]\"},{\"internalType\":\"uint160\",\"name\":\"averagePrice\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"averageLiquidity\",\"type\":\"uint128\"},{\"internalType\":\"int24\",\"name\":\"averageTick\",\"type\":\"int24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"burnPercent\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"claim\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.SnapshotLimitParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"snapshotLimit\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"\",\"type\":\"uint128\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/limit/ILimitPoolView.sol\":\"ILimitPoolView\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/limit/ILimitPoolView.sol\":{\"keccak256\":\"0x298606a582d43209c74b9abf469e3576876444b964ce02be0b5d52d8662fd88a\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://adad5cd4a5cbf1c874e8351def6f31877d305e28b6aaf6d9dc539c9d7417c420\",\"dweb:/ipfs/QmZTbR2jJzQe2T6GpDoGLPD5gAgLks6XFqzPewSUR1kBpw\"]},\"contracts/interfaces/structs/LimitPoolStructs.sol\":{\"keccak256\":\"0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef\",\"dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "immutables", "outputs": [{"internalType": "struct PoolsharkStructs.LimitImmutables", "name": "", "type": "tuple", "components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "poolImpl", "type": "address"}, {"internalType": "address", "name": "factory", "type": "address"}, {"internalType": "struct PoolsharkStructs.PriceBounds", "name": "bounds", "type": "tuple", "components": [{"internalType": "uint160", "name": "min", "type": "uint160"}, {"internalType": "uint160", "name": "max", "type": "uint160"}]}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "uint32", "name": "genesisTime", "type": "uint32"}, {"internalType": "int16", "name": "tickSpacing", "type": "int16"}, {"internalType": "uint16", "name": "swapFee", "type": "uint16"}]}]}, {"inputs": [{"internalType": "int16", "name": "tickSpacing", "type": "int16"}], "stateMutability": "pure", "type": "function", "name": "priceBounds", "outputs": [{"internalType": "uint160", "name": "minPrice", "type": "uint160"}, {"internalType": "uint160", "name": "maxPrice", "type": "uint160"}]}, {"inputs": [{"internalType": "uint32[]", "name": "secondsAgo", "type": "uint32[]"}], "stateMutability": "view", "type": "function", "name": "sample", "outputs": [{"internalType": "int56[]", "name": "tickSecondsAccum", "type": "int56[]"}, {"internalType": "uint160[]", "name": "secondsPerLiquidityAccum", "type": "uint160[]"}, {"internalType": "uint160", "name": "averagePrice", "type": "uint160"}, {"internalType": "uint128", "name": "averageLiquidity", "type": "uint128"}, {"internalType": "int24", "name": "averageTick", "type": "int24"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.SnapshotLimitParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint128", "name": "burnPercent", "type": "uint128"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "int24", "name": "claim", "type": "int24"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}]}], "stateMutability": "view", "type": "function", "name": "snapshotLimit", "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}, {"internalType": "uint128", "name": "", "type": "uint128"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/limit/ILimitPoolView.sol": "ILimitPoolView"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPoolView.sol": {"keccak256": "0x298606a582d43209c74b9abf469e3576876444b964ce02be0b5d52d8662fd88a", "urls": ["bzz-raw://adad5cd4a5cbf1c874e8351def6f31877d305e28b6aaf6d9dc539c9d7417c420", "dweb:/ipfs/QmZTbR2jJzQe2T6GpDoGLPD5gAgLks6XFqzPewSUR1kBpw"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/LimitPoolStructs.sol": {"keccak256": "0x666d85b00374ea1c6a30a0aef0ab5ccb60348c1060092ad86dd11346f385b0d4", "urls": ["bzz-raw://ae11cc93153d13fa64513c9200441add3b234a2e3d1f223666a983cc864bbaef", "dweb:/ipfs/QmbS3BhG1SSAC8EPf9JAgGZdpCwRbWCJMPJqpLd59PJwrV"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}}, "version": 1}, "id": 30}