{"abi": [{"type": "function", "name": "limitPoolMintLimitCallback", "inputs": [{"name": "amount0Delta", "type": "int256", "internalType": "int256"}, {"name": "amount1Delta", "type": "int256", "internalType": "int256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"limitPoolMintLimitCallback(int256,int256,bytes)": "030c66d5"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"amount0Delta\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"amount1Delta\",\"type\":\"int256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"limitPoolMintLimitCallback\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"limitPoolMintLimitCallback(int256,int256,bytes)\":{\"params\":{\"amount0Delta\":\"The amount of token0 either received by (positive) or sent from (negative) the user.\",\"amount1Delta\":\"The amount of token1 either received by (positive) or sent from (negative) the user.\"}}},\"title\":\"Callback for limit mints\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"limitPoolMintLimitCallback(int256,int256,bytes)\":{\"notice\":\"Called to `msg.sender` after executing a mint.\"}},\"notice\":\"Any contract that calls the `mintLimit` function must implement this interface.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/callbacks/ILimitPoolCallback.sol\":\"ILimitPoolMintLimitCallback\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/callbacks/ILimitPoolCallback.sol\":{\"keccak256\":\"0xbdd400055110618e0e90afe1064193676db3fcdcc621573b45c3512e8f062821\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://86c3b364c617fec20bfd96a843feca91d950bd2289b7650bd2a96ac9e7c380a7\",\"dweb:/ipfs/QmWR5PJQu6TuQb7ipouKkiwN5Lq11khdTTDAtaXxJyGnrX\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "int256", "name": "amount0Delta", "type": "int256"}, {"internalType": "int256", "name": "amount1Delta", "type": "int256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "limitPoolMintLimitCallback"}], "devdoc": {"kind": "dev", "methods": {"limitPoolMintLimitCallback(int256,int256,bytes)": {"params": {"amount0Delta": "The amount of token0 either received by (positive) or sent from (negative) the user.", "amount1Delta": "The amount of token1 either received by (positive) or sent from (negative) the user."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"limitPoolMintLimitCallback(int256,int256,bytes)": {"notice": "Called to `msg.sender` after executing a mint."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/callbacks/ILimitPoolCallback.sol": "ILimitPoolMintLimitCallback"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/callbacks/ILimitPoolCallback.sol": {"keccak256": "0xbdd400055110618e0e90afe1064193676db3fcdcc621573b45c3512e8f062821", "urls": ["bzz-raw://86c3b364c617fec20bfd96a843feca91d950bd2289b7650bd2a96ac9e7c380a7", "dweb:/ipfs/QmWR5PJQu6TuQb7ipouKkiwN5Lq11khdTTDAtaXxJyGnrX"], "license": "GPL-3.0-or-later"}}, "version": 1}, "id": 22}