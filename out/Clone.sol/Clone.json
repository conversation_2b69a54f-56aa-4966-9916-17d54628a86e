{"abi": [], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"author\":\"Solady (https://github.com/vectorized/solady/blob/main/src/utils/Clone.sol)Adapted from clones with immutable args by zefram.eth, <PERSON>-<PERSON> & <PERSON> (https://github.com/<PERSON>-<PERSON>-and-<PERSON>/clones-with-immutable-args)\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Class with helper read functions for clone with immutable args.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/external/solady/Clone.sol\":\"Clone\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/external/solady/Clone.sol\":{\"keccak256\":\"0xe7dc35cc81529e1e43b249f99de3a26e595ffd209450b85f3e8bc74a0d9aea01\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6d0471fad81c71706936f7b014b0cfcd5f03337450838409c8c02fedc38a8560\",\"dweb:/ipfs/Qmede4qMoU29saLxaBxaA72kuWt4794HLcB7ZdXHRrBFXC\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/external/solady/Clone.sol": "<PERSON><PERSON>"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/external/solady/Clone.sol": {"keccak256": "0xe7dc35cc81529e1e43b249f99de3a26e595ffd209450b85f3e8bc74a0d9aea01", "urls": ["bzz-raw://6d0471fad81c71706936f7b014b0cfcd5f03337450838409c8c02fedc38a8560", "dweb:/ipfs/Qmede4qMoU29saLxaBxaA72kuWt4794HLcB7ZdXHRrBFXC"], "license": "MIT"}}, "version": 1}, "id": 15}