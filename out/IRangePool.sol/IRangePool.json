{"abi": [{"type": "function", "name": "burn<PERSON>ang<PERSON>", "inputs": [{"name": "burnParams", "type": "tuple", "internalType": "struct PoolsharkStructs.BurnRangeParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "burnPercent", "type": "uint128", "internalType": "uint128"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "increaseSampleCount", "inputs": [{"name": "newSampleCountMax", "type": "uint16", "internalType": "uint16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mintRange", "inputs": [{"name": "mintParams", "type": "tuple", "internalType": "struct PoolsharkStructs.MintRangeParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "amount0", "type": "uint128", "internalType": "uint128"}, {"name": "amount1", "type": "uint128", "internalType": "uint128"}, {"name": "callbackData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "positions", "inputs": [{"name": "positionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "feeGrowthInside0Last", "type": "uint256", "internalType": "uint256"}, {"name": "feeGrowthInside1Last", "type": "uint256", "internalType": "uint256"}, {"name": "liquidity", "type": "uint128", "internalType": "uint128"}, {"name": "lower", "type": "int24", "internalType": "int24"}, {"name": "upper", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "quote", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.QuoteParams", "components": [{"name": "priceLimit", "type": "uint160", "internalType": "uint160"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}, {"name": "exactIn", "type": "bool", "internalType": "bool"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}]}], "outputs": [{"name": "inAmount", "type": "uint256", "internalType": "uint256"}, {"name": "outAmount", "type": "uint256", "internalType": "uint256"}, {"name": "priceAfter", "type": "uint160", "internalType": "uint160"}], "stateMutability": "view"}, {"type": "function", "name": "sample", "inputs": [{"name": "secondsAgo", "type": "uint32[]", "internalType": "uint32[]"}], "outputs": [{"name": "tickSecondsAccum", "type": "int56[]", "internalType": "int56[]"}, {"name": "secondsPerLiquidityAccum", "type": "uint160[]", "internalType": "uint160[]"}, {"name": "averagePrice", "type": "uint160", "internalType": "uint160"}, {"name": "averageLiquidity", "type": "uint128", "internalType": "uint128"}, {"name": "averageTick", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "samples", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}, {"name": "", "type": "int56", "internalType": "int56"}, {"name": "", "type": "uint160", "internalType": "uint160"}], "stateMutability": "view"}, {"type": "function", "name": "snapshotRange", "inputs": [{"name": "positionId", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "tickSecondsAccum", "type": "int56", "internalType": "int56"}, {"name": "secondsPerLiquidityAccum", "type": "uint160", "internalType": "uint160"}, {"name": "feesOwed0", "type": "uint128", "internalType": "uint128"}, {"name": "feesOwed1", "type": "uint128", "internalType": "uint128"}], "stateMutability": "view"}, {"type": "function", "name": "swap", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.SwapParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "priceLimit", "type": "uint160", "internalType": "uint160"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}, {"name": "exactIn", "type": "bool", "internalType": "bool"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}, {"name": "callbackData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "amount0", "type": "int256", "internalType": "int256"}, {"name": "amount1", "type": "int256", "internalType": "int256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "ticks", "inputs": [{"name": "", "type": "int24", "internalType": "int24"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct PoolsharkStructs.RangeTick", "components": [{"name": "feeGrowthOutside0", "type": "uint200", "internalType": "uint200"}, {"name": "feeGrowthOutside1", "type": "uint200", "internalType": "uint200"}, {"name": "secondsPerLiquidityAccumOutside", "type": "uint160", "internalType": "uint160"}, {"name": "tickSecondsAccumOutside", "type": "int56", "internalType": "int56"}, {"name": "liquidityDelta", "type": "int128", "internalType": "int128"}, {"name": "liquidityAbsolute", "type": "uint128", "internalType": "uint128"}]}, {"name": "", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitTick", "components": [{"name": "priceAt", "type": "uint160", "internalType": "uint160"}, {"name": "liquidityDelta", "type": "int128", "internalType": "int128"}, {"name": "liquidityAbsolute", "type": "uint128", "internalType": "uint128"}]}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"burnRange((address,uint32,uint128))": "fadd58a2", "increaseSampleCount(uint16)": "d468e71f", "mintRange((address,int24,int24,uint32,uint128,uint128,bytes))": "f0a20aec", "positions(uint256)": "99fbab88", "quote((uint160,uint128,bool,bool))": "43865d4e", "sample(uint32[])": "f62167fc", "samples(uint256)": "07e72129", "snapshotRange(uint32)": "5cbb18d1", "swap((address,uint160,uint128,bool,bool,bytes))": "e323eb0e", "ticks(int24)": "f30dba93"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"uint128\",\"name\":\"burnPercent\",\"type\":\"uint128\"}],\"internalType\":\"struct PoolsharkStructs.BurnRangeParams\",\"name\":\"burnParams\",\"type\":\"tuple\"}],\"name\":\"burnRange\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"newSampleCountMax\",\"type\":\"uint16\"}],\"name\":\"increaseSampleCount\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"uint128\",\"name\":\"amount0\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"amount1\",\"type\":\"uint128\"},{\"internalType\":\"bytes\",\"name\":\"callbackData\",\"type\":\"bytes\"}],\"internalType\":\"struct PoolsharkStructs.MintRangeParams\",\"name\":\"mintParams\",\"type\":\"tuple\"}],\"name\":\"mintRange\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"positionId\",\"type\":\"uint256\"}],\"name\":\"positions\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"feeGrowthInside0Last\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"feeGrowthInside1Last\",\"type\":\"uint256\"},{\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint160\",\"name\":\"priceLimit\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"bool\",\"name\":\"exactIn\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.QuoteParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"quote\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"inAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"outAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint160\",\"name\":\"priceAfter\",\"type\":\"uint160\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32[]\",\"name\":\"secondsAgo\",\"type\":\"uint32[]\"}],\"name\":\"sample\",\"outputs\":[{\"internalType\":\"int56[]\",\"name\":\"tickSecondsAccum\",\"type\":\"int56[]\"},{\"internalType\":\"uint160[]\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160[]\"},{\"internalType\":\"uint160\",\"name\":\"averagePrice\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"averageLiquidity\",\"type\":\"uint128\"},{\"internalType\":\"int24\",\"name\":\"averageTick\",\"type\":\"int24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"samples\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"},{\"internalType\":\"int56\",\"name\":\"\",\"type\":\"int56\"},{\"internalType\":\"uint160\",\"name\":\"\",\"type\":\"uint160\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"}],\"name\":\"snapshotRange\",\"outputs\":[{\"internalType\":\"int56\",\"name\":\"tickSecondsAccum\",\"type\":\"int56\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"feesOwed0\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"feesOwed1\",\"type\":\"uint128\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint160\",\"name\":\"priceLimit\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"bool\",\"name\":\"exactIn\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"callbackData\",\"type\":\"bytes\"}],\"internalType\":\"struct PoolsharkStructs.SwapParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"swap\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"amount0\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"amount1\",\"type\":\"int256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int24\",\"name\":\"\",\"type\":\"int24\"}],\"name\":\"ticks\",\"outputs\":[{\"components\":[{\"internalType\":\"uint200\",\"name\":\"feeGrowthOutside0\",\"type\":\"uint200\"},{\"internalType\":\"uint200\",\"name\":\"feeGrowthOutside1\",\"type\":\"uint200\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccumOutside\",\"type\":\"uint160\"},{\"internalType\":\"int56\",\"name\":\"tickSecondsAccumOutside\",\"type\":\"int56\"},{\"internalType\":\"int128\",\"name\":\"liquidityDelta\",\"type\":\"int128\"},{\"internalType\":\"uint128\",\"name\":\"liquidityAbsolute\",\"type\":\"uint128\"}],\"internalType\":\"struct PoolsharkStructs.RangeTick\",\"name\":\"\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"priceAt\",\"type\":\"uint160\"},{\"internalType\":\"int128\",\"name\":\"liquidityDelta\",\"type\":\"int128\"},{\"internalType\":\"uint128\",\"name\":\"liquidityAbsolute\",\"type\":\"uint128\"}],\"internalType\":\"struct PoolsharkStructs.LimitTick\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/range/IRangePool.sol\":\"IRangePool\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/range/IRangePool.sol\":{\"keccak256\":\"0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab\",\"dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt\"]},\"contracts/interfaces/range/IRangePoolManager.sol\":{\"keccak256\":\"0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065\",\"dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/interfaces/structs/RangePoolStructs.sol\":{\"keccak256\":\"0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9\",\"dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct PoolsharkStructs.BurnRangeParams", "name": "burnParams", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "uint128", "name": "burnPercent", "type": "uint128"}]}], "stateMutability": "nonpayable", "type": "function", "name": "burn<PERSON>ang<PERSON>"}, {"inputs": [{"internalType": "uint16", "name": "newSampleCountMax", "type": "uint16"}], "stateMutability": "nonpayable", "type": "function", "name": "increaseSampleCount"}, {"inputs": [{"internalType": "struct PoolsharkStructs.MintRangeParams", "name": "mintParams", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "uint128", "name": "amount0", "type": "uint128"}, {"internalType": "uint128", "name": "amount1", "type": "uint128"}, {"internalType": "bytes", "name": "callbackData", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "mintRange"}, {"inputs": [{"internalType": "uint256", "name": "positionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "positions", "outputs": [{"internalType": "uint256", "name": "feeGrowthInside0Last", "type": "uint256"}, {"internalType": "uint256", "name": "feeGrowthInside1Last", "type": "uint256"}, {"internalType": "uint128", "name": "liquidity", "type": "uint128"}, {"internalType": "int24", "name": "lower", "type": "int24"}, {"internalType": "int24", "name": "upper", "type": "int24"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.QuoteParams", "name": "params", "type": "tuple", "components": [{"internalType": "uint160", "name": "priceLimit", "type": "uint160"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "bool", "name": "exactIn", "type": "bool"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}]}], "stateMutability": "view", "type": "function", "name": "quote", "outputs": [{"internalType": "uint256", "name": "inAmount", "type": "uint256"}, {"internalType": "uint256", "name": "outAmount", "type": "uint256"}, {"internalType": "uint160", "name": "priceAfter", "type": "uint160"}]}, {"inputs": [{"internalType": "uint32[]", "name": "secondsAgo", "type": "uint32[]"}], "stateMutability": "view", "type": "function", "name": "sample", "outputs": [{"internalType": "int56[]", "name": "tickSecondsAccum", "type": "int56[]"}, {"internalType": "uint160[]", "name": "secondsPerLiquidityAccum", "type": "uint160[]"}, {"internalType": "uint160", "name": "averagePrice", "type": "uint160"}, {"internalType": "uint128", "name": "averageLiquidity", "type": "uint128"}, {"internalType": "int24", "name": "averageTick", "type": "int24"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "samples", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}, {"internalType": "int56", "name": "", "type": "int56"}, {"internalType": "uint160", "name": "", "type": "uint160"}]}, {"inputs": [{"internalType": "uint32", "name": "positionId", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "snapshotRange", "outputs": [{"internalType": "int56", "name": "tickSecondsAccum", "type": "int56"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160"}, {"internalType": "uint128", "name": "feesOwed0", "type": "uint128"}, {"internalType": "uint128", "name": "feesOwed1", "type": "uint128"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.SwapParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint160", "name": "priceLimit", "type": "uint160"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "bool", "name": "exactIn", "type": "bool"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"internalType": "bytes", "name": "callbackData", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "swap", "outputs": [{"internalType": "int256", "name": "amount0", "type": "int256"}, {"internalType": "int256", "name": "amount1", "type": "int256"}]}, {"inputs": [{"internalType": "int24", "name": "", "type": "int24"}], "stateMutability": "view", "type": "function", "name": "ticks", "outputs": [{"internalType": "struct PoolsharkStructs.RangeTick", "name": "", "type": "tuple", "components": [{"internalType": "uint200", "name": "feeGrowthOutside0", "type": "uint200"}, {"internalType": "uint200", "name": "feeGrowthOutside1", "type": "uint200"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccumOutside", "type": "uint160"}, {"internalType": "int56", "name": "tickSecondsAccumOutside", "type": "int56"}, {"internalType": "int128", "name": "liquidityDelta", "type": "int128"}, {"internalType": "uint128", "name": "liquidityAbsolute", "type": "uint128"}]}, {"internalType": "struct PoolsharkStructs.LimitTick", "name": "", "type": "tuple", "components": [{"internalType": "uint160", "name": "priceAt", "type": "uint160"}, {"internalType": "int128", "name": "liquidityDelta", "type": "int128"}, {"internalType": "uint128", "name": "liquidityAbsolute", "type": "uint128"}]}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/range/IRangePool.sol": "IRangePool"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePool.sol": {"keccak256": "0x09370229dcc66161d4b6eeb0c6b5153614e361395972b36795f7a483e99456bf", "urls": ["bzz-raw://132591c8b537971a6d73505990c52da56cf4fe607b3a34aed53fb473d925ddab", "dweb:/ipfs/QmUR8pB1Xt1hU6z8fcCg68hUST53fNhQvvegMQGimQdwLt"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePoolManager.sol": {"keccak256": "0xd288cd9098ebc2b55051185f4076cd3cf5fde14906469a7bda403cec27119139", "urls": ["bzz-raw://1ecca02fdae852621ecbed88cf0ab0d7682548c6307153a55864628f6fead065", "dweb:/ipfs/QmYuk2qZE6huVYM6GCDh2ZeCzXHv7YbDztWRiBGcc5u1AP"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/interfaces/structs/RangePoolStructs.sol": {"keccak256": "0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd", "urls": ["bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9", "dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx"], "license": "GPLv3"}}, "version": 1}, "id": 31}