{"abi": [{"type": "function", "name": "createLimitPool", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitPoolParams", "components": [{"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "startPrice", "type": "uint160", "internalType": "uint160"}, {"name": "swapFee", "type": "uint16", "internalType": "uint16"}, {"name": "poolTypeId", "type": "uint16", "internalType": "uint16"}]}], "outputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getLimitPool", "inputs": [{"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "swapFee", "type": "uint16", "internalType": "uint16"}, {"name": "poolTypeId", "type": "uint16", "internalType": "uint16"}], "outputs": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pools", "inputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"createLimitPool((address,address,uint160,uint16,uint16))": "aa5c269b", "getLimitPool(address,address,uint16,uint16)": "2f6fce98", "pools(bytes32)": "b5217bb4"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"tokenIn\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenOut\",\"type\":\"address\"},{\"internalType\":\"uint160\",\"name\":\"startPrice\",\"type\":\"uint160\"},{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"poolTypeId\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.LimitPoolParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"createLimitPool\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenIn\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenOut\",\"type\":\"address\"},{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"poolTypeId\",\"type\":\"uint16\"}],\"name\":\"getLimitPool\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"pool\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"pools\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/limit/ILimitPoolFactory.sol\":\"ILimitPoolFactory\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/base/storage/LimitPoolFactoryStorage.sol\":{\"keccak256\":\"0xec8efbb85bf6d831dc0c3910eccbe1b6def550ac0b88eef62b4a7c0c314a31ae\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://363a51daf8ce8d46c62bb8d9821b99ea2ec7d4f81a0512f5b83901d3a9a4631f\",\"dweb:/ipfs/QmeKyWJwsX9LwB5A8ovGm7J2nToJvR1dnE3sMww46ERb55\"]},\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/limit/ILimitPoolFactory.sol\":{\"keccak256\":\"0x89fc412319c30451896a1db8d77bb528c6df383523cf7f278534a645a4a49733\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://495509c3a546e0b85b34a8ef7fc114ca5bb98d831c8bbd176b42d36703f00fd0\",\"dweb:/ipfs/QmcFR6a32Fp7s4hRDmJfp7GaC65uQUtRo6VhE6ygCpZefm\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct PoolsharkStructs.LimitPoolParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "address", "name": "tokenOut", "type": "address"}, {"internalType": "uint160", "name": "startPrice", "type": "uint160"}, {"internalType": "uint16", "name": "swapFee", "type": "uint16"}, {"internalType": "uint16", "name": "poolTypeId", "type": "uint16"}]}], "stateMutability": "nonpayable", "type": "function", "name": "createLimitPool", "outputs": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "tokenIn", "type": "address"}, {"internalType": "address", "name": "tokenOut", "type": "address"}, {"internalType": "uint16", "name": "swapFee", "type": "uint16"}, {"internalType": "uint16", "name": "poolTypeId", "type": "uint16"}], "stateMutability": "view", "type": "function", "name": "getLimitPool", "outputs": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "pools", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/limit/ILimitPoolFactory.sol": "ILimitPoolFactory"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/base/storage/LimitPoolFactoryStorage.sol": {"keccak256": "0xec8efbb85bf6d831dc0c3910eccbe1b6def550ac0b88eef62b4a7c0c314a31ae", "urls": ["bzz-raw://363a51daf8ce8d46c62bb8d9821b99ea2ec7d4f81a0512f5b83901d3a9a4631f", "dweb:/ipfs/QmeKyWJwsX9LwB5A8ovGm7J2nToJvR1dnE3sMww46ERb55"], "license": "BUSL-1.1"}, "contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/limit/ILimitPoolFactory.sol": {"keccak256": "0x89fc412319c30451896a1db8d77bb528c6df383523cf7f278534a645a4a49733", "urls": ["bzz-raw://495509c3a546e0b85b34a8ef7fc114ca5bb98d831c8bbd176b42d36703f00fd0", "dweb:/ipfs/QmcFR6a32Fp7s4hRDmJfp7GaC65uQUtRo6VhE6ygCpZefm"], "license": "BUSL-1.1"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}}, "version": 1}, "id": 27}