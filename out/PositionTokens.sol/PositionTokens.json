{"abi": [], "bytecode": {"object": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212207a94f0cb41e77c175c61026fd2bd8bf7ec9785e6d718796baaa175b5fa6feb8464736f6c634300080d0033", "sourceMap": "401:989:62:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;401:989:62;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212207a94f0cb41e77c175c61026fd2bd8bf7ec9785e6d718796baaa175b5fa6feb8464736f6c634300080d0033", "sourceMap": "401:989:62:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Token library for ERC-1155 calls.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/libraries/utils/PositionTokens.sol\":\"PositionTokens\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/IPositionERC1155.sol\":{\"keccak256\":\"0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11\",\"dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe\"]},\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/range/IRangePoolFactory.sol\":{\"keccak256\":\"0xa38c314350de1c59bd90e521fe0920d1e5008009aca592697b07194346da8cc1\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://53551b95615f6a6259753c7e9fc189a46dfe971b83cb417af4d4c593278f9d60\",\"dweb:/ipfs/QmXFnKtuRVJgs9HbNaNZna7mkmBRhPxtiVfkJKWuqytscS\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]},\"contracts/interfaces/structs/RangePoolStructs.sol\":{\"keccak256\":\"0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9\",\"dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx\"]},\"contracts/libraries/math/OverflowMath.sol\":{\"keccak256\":\"0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29\",\"dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T\"]},\"contracts/libraries/utils/Bytes.sol\":{\"keccak256\":\"0x461969264908704100e3195ed51ca299ddcfa8bafcd8c2bf55e159e44d4b6b73\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ded39fcbe8dd11f7ba5aa5bb319a733529076c03a2d332812913fbab4dbecc00\",\"dweb:/ipfs/QmeZmu4exQa8hSbfEabw3hxcS9n1qNf8Uiun5hxao9GZT9\"]},\"contracts/libraries/utils/PositionTokens.sol\":{\"keccak256\":\"0xf08d7ba95f79d677e2b4e32abc41c0f86ca11fbf6d056d1de06fe5c3ebdc9960\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://fabb91eb2b0f6823b48bdb245f9ac650958b526179e86b44683ba7b691843d79\",\"dweb:/ipfs/QmWsGVaBvQ3RFwu94BvAebRHP1NCzJ5dLfX7VTeWXjG4if\"]},\"contracts/libraries/utils/String.sol\":{\"keccak256\":\"0x0d03855bfeabee266fa8252cbcac9bab81b5a951d3c0a99ae51f2e4a1942dbf2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2fba9aae08e1406ee8c7c21da98bd72c4f34e55323ffbb0146e9ec8f0a4a06e3\",\"dweb:/ipfs/QmbT189JtHc8esSwJ9TCkLhYVBVguJr2noepxKh5XrjEfj\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0\",\"dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34\",\"dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd\",\"dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92\",\"dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f\",\"dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/libraries/utils/PositionTokens.sol": "PositionTokens"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/IPositionERC1155.sol": {"keccak256": "0x935ebf6b752e726e744efb55525b5e265bd8ed9396f30f3819f903713c00888c", "urls": ["bzz-raw://d94209718cecddd3e11753f185633774525e17524a883553ecb295c743a71a11", "dweb:/ipfs/QmNgBm9tnyqDjRksS2uKi9bpma7Y3qbve7hAgjzP3LctEe"], "license": "MIT"}, "contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/range/IRangePoolFactory.sol": {"keccak256": "0xa38c314350de1c59bd90e521fe0920d1e5008009aca592697b07194346da8cc1", "urls": ["bzz-raw://53551b95615f6a6259753c7e9fc189a46dfe971b83cb417af4d4c593278f9d60", "dweb:/ipfs/QmXFnKtuRVJgs9HbNaNZna7mkmBRhPxtiVfkJKWuqytscS"], "license": "GPLv3"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}, "contracts/interfaces/structs/RangePoolStructs.sol": {"keccak256": "0x885571a4896c293f0aa5cc52bbfa4ccb32bf4b3aef9451ee64305b67f53b45dd", "urls": ["bzz-raw://4b415f59d83aeba16df3864dfd106d7a993121fa894331eedfa773dc8ed9cad9", "dweb:/ipfs/Qmb87a45ghfGgkPpL3bCwoLEH4aTQEr14Phnkwxew769fx"], "license": "GPLv3"}, "contracts/libraries/math/OverflowMath.sol": {"keccak256": "0x94c7e9cac2258436c887550e990b93957333e195734359da5b69afbbe83b1c5b", "urls": ["bzz-raw://92aed2d702d534e51f2cfee0ec0edc35a066f334142601db9cfb0a7421088b29", "dweb:/ipfs/QmdFmf9AB6DtkpEmooNwVLe3W4NThf9dzJnGatwRZJHC8T"], "license": "MIT"}, "contracts/libraries/utils/Bytes.sol": {"keccak256": "0x461969264908704100e3195ed51ca299ddcfa8bafcd8c2bf55e159e44d4b6b73", "urls": ["bzz-raw://ded39fcbe8dd11f7ba5aa5bb319a733529076c03a2d332812913fbab4dbecc00", "dweb:/ipfs/QmeZmu4exQa8hSbfEabw3hxcS9n1qNf8Uiun5hxao9GZT9"], "license": "MIT"}, "contracts/libraries/utils/PositionTokens.sol": {"keccak256": "0xf08d7ba95f79d677e2b4e32abc41c0f86ca11fbf6d056d1de06fe5c3ebdc9960", "urls": ["bzz-raw://fabb91eb2b0f6823b48bdb245f9ac650958b526179e86b44683ba7b691843d79", "dweb:/ipfs/QmWsGVaBvQ3RFwu94BvAebRHP1NCzJ5dLfX7VTeWXjG4if"], "license": "GPLv3"}, "contracts/libraries/utils/String.sol": {"keccak256": "0x0d03855bfeabee266fa8252cbcac9bab81b5a951d3c0a99ae51f2e4a1942dbf2", "urls": ["bzz-raw://2fba9aae08e1406ee8c7c21da98bd72c4f34e55323ffbb0146e9ec8f0a4a06e3", "dweb:/ipfs/QmbT189JtHc8esSwJ9TCkLhYVBVguJr2noepxKh5XrjEfj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x24b04b8aacaaf1a4a0719117b29c9c3647b1f479c5ac2a60f5ff1bb6d839c238", "urls": ["bzz-raw://43e46da9d9f49741ecd876a269e71bc7494058d7a8e9478429998adb5bc3eaa0", "dweb:/ipfs/QmUtp4cqzf22C5rJ76AabKADquGWcjsc33yjYXxXC4sDvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0x9750c6b834f7b43000631af5cc30001c5f547b3ceb3635488f140f60e897ea6b", "urls": ["bzz-raw://5a7d5b1ef5d8d5889ad2ed89d8619c09383b80b72ab226e0fe7bde1636481e34", "dweb:/ipfs/QmebXWgtEfumQGBdVeM6c71McLixYXQP5Bk6kKXuoY4Bmr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0x8de418a5503946cabe331f35fe242d3201a73f67f77aaeb7110acb1f30423aca", "urls": ["bzz-raw://5a376d3dda2cb70536c0a45c208b29b34ac560c4cb4f513a42079f96ba47d2dd", "dweb:/ipfs/QmZQg6gn1sUpM8wHzwNvSnihumUCAhxD119MpXeKp8B9s8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0xe2e337e6dde9ef6b680e07338c493ebea1b5fd09b43424112868e9cc1706bca7", "urls": ["bzz-raw://6df0ddf21ce9f58271bdfaa85cde98b200ef242a05a3f85c2bc10a8294800a92", "dweb:/ipfs/QmRK2Y5Yc6BK7tGKkgsgn3aJEQGi5aakeSPZvS65PV8Xp3"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x447a5f3ddc18419d41ff92b3773fb86471b1db25773e07f877f548918a185bf1", "urls": ["bzz-raw://be161e54f24e5c6fae81a12db1a8ae87bc5ae1b0ddc805d82a1440a68455088f", "dweb:/ipfs/QmP7C3CHdY9urF4dEMb9wmsp1wMxHF6nhA2yQE5SKiPAdy"], "license": "MIT"}}, "version": 1}, "id": 62}