{"abi": [{"type": "function", "name": "createRangePool", "inputs": [{"name": "fromToken", "type": "address", "internalType": "address"}, {"name": "destToken", "type": "address", "internalType": "address"}, {"name": "fee", "type": "uint16", "internalType": "uint16"}, {"name": "startPrice", "type": "uint160", "internalType": "uint160"}], "outputs": [{"name": "book", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getRangePool", "inputs": [{"name": "fromToken", "type": "address", "internalType": "address"}, {"name": "destToken", "type": "address", "internalType": "address"}, {"name": "fee", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"createRangePool(address,address,uint16,uint160)": "282b14da", "getRangePool(address,address,uint256)": "0e403ecf", "owner()": "8da5cb5b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"fromToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"destToken\",\"type\":\"address\"},{\"internalType\":\"uint16\",\"name\":\"fee\",\"type\":\"uint16\"},{\"internalType\":\"uint160\",\"name\":\"startPrice\",\"type\":\"uint160\"}],\"name\":\"createRangePool\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"book\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"fromToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"destToken\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"fee\",\"type\":\"uint256\"}],\"name\":\"getRangePool\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/range/IRangePoolFactory.sol\":\"IRangePoolFactory\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/range/IRangePoolFactory.sol\":{\"keccak256\":\"0xa38c314350de1c59bd90e521fe0920d1e5008009aca592697b07194346da8cc1\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://53551b95615f6a6259753c7e9fc189a46dfe971b83cb417af4d4c593278f9d60\",\"dweb:/ipfs/QmXFnKtuRVJgs9HbNaNZna7mkmBRhPxtiVfkJKWuqytscS\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "fromToken", "type": "address"}, {"internalType": "address", "name": "destToken", "type": "address"}, {"internalType": "uint16", "name": "fee", "type": "uint16"}, {"internalType": "uint160", "name": "startPrice", "type": "uint160"}], "stateMutability": "nonpayable", "type": "function", "name": "createRangePool", "outputs": [{"internalType": "address", "name": "book", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "fromToken", "type": "address"}, {"internalType": "address", "name": "destToken", "type": "address"}, {"internalType": "uint256", "name": "fee", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getRangePool", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/range/IRangePoolFactory.sol": "IRangePoolFactory"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/range/IRangePoolFactory.sol": {"keccak256": "0xa38c314350de1c59bd90e521fe0920d1e5008009aca592697b07194346da8cc1", "urls": ["bzz-raw://53551b95615f6a6259753c7e9fc189a46dfe971b83cb417af4d4c593278f9d60", "dweb:/ipfs/QmXFnKtuRVJgs9HbNaNZna7mkmBRhPxtiVfkJKWuqytscS"], "license": "GPLv3"}}, "version": 1}, "id": 32}