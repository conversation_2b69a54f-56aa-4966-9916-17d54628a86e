{"abi": [{"type": "function", "name": "fees", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.FeesParams", "components": [{"name": "protocolSwapFee0", "type": "uint16", "internalType": "uint16"}, {"name": "protocolSwapFee1", "type": "uint16", "internalType": "uint16"}, {"name": "protocolFillFee0", "type": "uint16", "internalType": "uint16"}, {"name": "protocolFillFee1", "type": "uint16", "internalType": "uint16"}, {"name": "set<PERSON><PERSON><PERSON><PERSON>s", "type": "uint8", "internalType": "uint8"}]}], "outputs": [{"name": "token0Fees", "type": "uint128", "internalType": "uint128"}, {"name": "token1Fees", "type": "uint128", "internalType": "uint128"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "immutables", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct PoolsharkStructs.LimitImmutables", "components": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "poolImpl", "type": "address", "internalType": "address"}, {"name": "factory", "type": "address", "internalType": "address"}, {"name": "bounds", "type": "tuple", "internalType": "struct PoolsharkStructs.PriceBounds", "components": [{"name": "min", "type": "uint160", "internalType": "uint160"}, {"name": "max", "type": "uint160", "internalType": "uint160"}]}, {"name": "token0", "type": "address", "internalType": "address"}, {"name": "token1", "type": "address", "internalType": "address"}, {"name": "poolToken", "type": "address", "internalType": "address"}, {"name": "genesisTime", "type": "uint32", "internalType": "uint32"}, {"name": "tickSpacing", "type": "int16", "internalType": "int16"}, {"name": "swapFee", "type": "uint16", "internalType": "uint16"}]}], "stateMutability": "view"}, {"type": "function", "name": "poolToken", "inputs": [], "outputs": [{"name": "poolToken", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "quote", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.QuoteParams", "components": [{"name": "priceLimit", "type": "uint160", "internalType": "uint160"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}, {"name": "exactIn", "type": "bool", "internalType": "bool"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}]}], "outputs": [{"name": "inAmount", "type": "int256", "internalType": "int256"}, {"name": "outAmount", "type": "int256", "internalType": "int256"}, {"name": "priceAfter", "type": "uint160", "internalType": "uint160"}], "stateMutability": "view"}, {"type": "function", "name": "sample", "inputs": [{"name": "secondsAgo", "type": "uint32[]", "internalType": "uint32[]"}], "outputs": [{"name": "tickSecondsAccum", "type": "int56[]", "internalType": "int56[]"}, {"name": "secondsPerLiquidityAccum", "type": "uint160[]", "internalType": "uint160[]"}, {"name": "averagePrice", "type": "uint160", "internalType": "uint160"}, {"name": "averageLiquidity", "type": "uint128", "internalType": "uint128"}, {"name": "averageTick", "type": "int24", "internalType": "int24"}], "stateMutability": "view"}, {"type": "function", "name": "snapshotLimit", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.SnapshotLimitParams", "components": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "burnPercent", "type": "uint128", "internalType": "uint128"}, {"name": "positionId", "type": "uint32", "internalType": "uint32"}, {"name": "claim", "type": "int24", "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}]}], "outputs": [{"name": "amountIn", "type": "uint128", "internalType": "uint128"}, {"name": "amountOut", "type": "uint128", "internalType": "uint128"}], "stateMutability": "view"}, {"type": "function", "name": "snapshotRange", "inputs": [{"name": "positionId", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "tickSecondsAccum", "type": "int56", "internalType": "int56"}, {"name": "secondsPerLiquidityAccum", "type": "uint160", "internalType": "uint160"}, {"name": "feesOwed0", "type": "uint128", "internalType": "uint128"}, {"name": "feesOwed1", "type": "uint128", "internalType": "uint128"}], "stateMutability": "view"}, {"type": "function", "name": "swap", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct PoolsharkStructs.SwapParams", "components": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "priceLimit", "type": "uint160", "internalType": "uint160"}, {"name": "amount", "type": "uint128", "internalType": "uint128"}, {"name": "exactIn", "type": "bool", "internalType": "bool"}, {"name": "zeroForOne", "type": "bool", "internalType": "bool"}, {"name": "callbackData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "amount0", "type": "int256", "internalType": "int256"}, {"name": "amount1", "type": "int256", "internalType": "int256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "token0", "inputs": [], "outputs": [{"name": "token0", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "token1", "inputs": [], "outputs": [{"name": "token1", "type": "address", "internalType": "address"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"fees((uint16,uint16,uint16,uint16,uint8))": "810b1d09", "immutables()": "fdf53665", "poolToken()": "cbdf382c", "quote((uint160,uint128,bool,bool))": "43865d4e", "sample(uint32[])": "f62167fc", "snapshotLimit((address,uint128,uint32,int24,bool))": "8a5ee2c3", "snapshotRange(uint32)": "5cbb18d1", "swap((address,uint160,uint128,bool,bool,bytes))": "e323eb0e", "token0()": "0dfe1681", "token1()": "d21220a7"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee0\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolSwapFee1\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee0\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"protocolFillFee1\",\"type\":\"uint16\"},{\"internalType\":\"uint8\",\"name\":\"setFeesFlags\",\"type\":\"uint8\"}],\"internalType\":\"struct PoolsharkStructs.FeesParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"fees\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"token0Fees\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"token1Fees\",\"type\":\"uint128\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"immutables\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolImpl\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"factory\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint160\",\"name\":\"min\",\"type\":\"uint160\"},{\"internalType\":\"uint160\",\"name\":\"max\",\"type\":\"uint160\"}],\"internalType\":\"struct PoolsharkStructs.PriceBounds\",\"name\":\"bounds\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"genesisTime\",\"type\":\"uint32\"},{\"internalType\":\"int16\",\"name\":\"tickSpacing\",\"type\":\"int16\"},{\"internalType\":\"uint16\",\"name\":\"swapFee\",\"type\":\"uint16\"}],\"internalType\":\"struct PoolsharkStructs.LimitImmutables\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"poolToken\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"poolToken\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint160\",\"name\":\"priceLimit\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"bool\",\"name\":\"exactIn\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.QuoteParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"quote\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"inAmount\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"outAmount\",\"type\":\"int256\"},{\"internalType\":\"uint160\",\"name\":\"priceAfter\",\"type\":\"uint160\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32[]\",\"name\":\"secondsAgo\",\"type\":\"uint32[]\"}],\"name\":\"sample\",\"outputs\":[{\"internalType\":\"int56[]\",\"name\":\"tickSecondsAccum\",\"type\":\"int56[]\"},{\"internalType\":\"uint160[]\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160[]\"},{\"internalType\":\"uint160\",\"name\":\"averagePrice\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"averageLiquidity\",\"type\":\"uint128\"},{\"internalType\":\"int24\",\"name\":\"averageTick\",\"type\":\"int24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"uint128\",\"name\":\"burnPercent\",\"type\":\"uint128\"},{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"internalType\":\"int24\",\"name\":\"claim\",\"type\":\"int24\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"internalType\":\"struct PoolsharkStructs.SnapshotLimitParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"snapshotLimit\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"amountIn\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"amountOut\",\"type\":\"uint128\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"}],\"name\":\"snapshotRange\",\"outputs\":[{\"internalType\":\"int56\",\"name\":\"tickSecondsAccum\",\"type\":\"int56\"},{\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"feesOwed0\",\"type\":\"uint128\"},{\"internalType\":\"uint128\",\"name\":\"feesOwed1\",\"type\":\"uint128\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint160\",\"name\":\"priceLimit\",\"type\":\"uint160\"},{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"bool\",\"name\":\"exactIn\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"callbackData\",\"type\":\"bytes\"}],\"internalType\":\"struct PoolsharkStructs.SwapParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"swap\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"amount0\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"amount1\",\"type\":\"int256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token0\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"token0\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"token1\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"token1\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/interfaces/IPool.sol\":\"IPool\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/interfaces/IPool.sol\":{\"keccak256\":\"0x67f42bc51b5a8fe379805a5c07826872c4503163d53894c59173e8f6d72a2b53\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://1662beddbe5fec900079fcb76eb1371b9af88f3a90db0bd828c4b57c460a659e\",\"dweb:/ipfs/QmQXx2t6iSgyjaXEsdaXMpaStjmVuRMiCjJA8x1CWtnEYu\"]},\"contracts/interfaces/cover/ITwapSource.sol\":{\"keccak256\":\"0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826\",\"dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS\"]},\"contracts/interfaces/structs/PoolsharkStructs.sol\":{\"keccak256\":\"0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32\",\"license\":\"GPLv3\",\"urls\":[\"bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501\",\"dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct PoolsharkStructs.FeesParams", "name": "params", "type": "tuple", "components": [{"internalType": "uint16", "name": "protocolSwapFee0", "type": "uint16"}, {"internalType": "uint16", "name": "protocolSwapFee1", "type": "uint16"}, {"internalType": "uint16", "name": "protocolFillFee0", "type": "uint16"}, {"internalType": "uint16", "name": "protocolFillFee1", "type": "uint16"}, {"internalType": "uint8", "name": "set<PERSON><PERSON><PERSON><PERSON>s", "type": "uint8"}]}], "stateMutability": "nonpayable", "type": "function", "name": "fees", "outputs": [{"internalType": "uint128", "name": "token0Fees", "type": "uint128"}, {"internalType": "uint128", "name": "token1Fees", "type": "uint128"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "immutables", "outputs": [{"internalType": "struct PoolsharkStructs.LimitImmutables", "name": "", "type": "tuple", "components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "poolImpl", "type": "address"}, {"internalType": "address", "name": "factory", "type": "address"}, {"internalType": "struct PoolsharkStructs.PriceBounds", "name": "bounds", "type": "tuple", "components": [{"internalType": "uint160", "name": "min", "type": "uint160"}, {"internalType": "uint160", "name": "max", "type": "uint160"}]}, {"internalType": "address", "name": "token0", "type": "address"}, {"internalType": "address", "name": "token1", "type": "address"}, {"internalType": "address", "name": "poolToken", "type": "address"}, {"internalType": "uint32", "name": "genesisTime", "type": "uint32"}, {"internalType": "int16", "name": "tickSpacing", "type": "int16"}, {"internalType": "uint16", "name": "swapFee", "type": "uint16"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "poolToken", "outputs": [{"internalType": "address", "name": "poolToken", "type": "address"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.QuoteParams", "name": "params", "type": "tuple", "components": [{"internalType": "uint160", "name": "priceLimit", "type": "uint160"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "bool", "name": "exactIn", "type": "bool"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}]}], "stateMutability": "view", "type": "function", "name": "quote", "outputs": [{"internalType": "int256", "name": "inAmount", "type": "int256"}, {"internalType": "int256", "name": "outAmount", "type": "int256"}, {"internalType": "uint160", "name": "priceAfter", "type": "uint160"}]}, {"inputs": [{"internalType": "uint32[]", "name": "secondsAgo", "type": "uint32[]"}], "stateMutability": "view", "type": "function", "name": "sample", "outputs": [{"internalType": "int56[]", "name": "tickSecondsAccum", "type": "int56[]"}, {"internalType": "uint160[]", "name": "secondsPerLiquidityAccum", "type": "uint160[]"}, {"internalType": "uint160", "name": "averagePrice", "type": "uint160"}, {"internalType": "uint128", "name": "averageLiquidity", "type": "uint128"}, {"internalType": "int24", "name": "averageTick", "type": "int24"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.SnapshotLimitParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint128", "name": "burnPercent", "type": "uint128"}, {"internalType": "uint32", "name": "positionId", "type": "uint32"}, {"internalType": "int24", "name": "claim", "type": "int24"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}]}], "stateMutability": "view", "type": "function", "name": "snapshotLimit", "outputs": [{"internalType": "uint128", "name": "amountIn", "type": "uint128"}, {"internalType": "uint128", "name": "amountOut", "type": "uint128"}]}, {"inputs": [{"internalType": "uint32", "name": "positionId", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "snapshotRange", "outputs": [{"internalType": "int56", "name": "tickSecondsAccum", "type": "int56"}, {"internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160"}, {"internalType": "uint128", "name": "feesOwed0", "type": "uint128"}, {"internalType": "uint128", "name": "feesOwed1", "type": "uint128"}]}, {"inputs": [{"internalType": "struct PoolsharkStructs.SwapParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint160", "name": "priceLimit", "type": "uint160"}, {"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "bool", "name": "exactIn", "type": "bool"}, {"internalType": "bool", "name": "zeroForOne", "type": "bool"}, {"internalType": "bytes", "name": "callbackData", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "swap", "outputs": [{"internalType": "int256", "name": "amount0", "type": "int256"}, {"internalType": "int256", "name": "amount1", "type": "int256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "token0", "outputs": [{"internalType": "address", "name": "token0", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "token1", "outputs": [{"internalType": "address", "name": "token1", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/interfaces/IPool.sol": "IPool"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/interfaces/IPool.sol": {"keccak256": "0x67f42bc51b5a8fe379805a5c07826872c4503163d53894c59173e8f6d72a2b53", "urls": ["bzz-raw://1662beddbe5fec900079fcb76eb1371b9af88f3a90db0bd828c4b57c460a659e", "dweb:/ipfs/QmQXx2t6iSgyjaXEsdaXMpaStjmVuRMiCjJA8x1CWtnEYu"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/cover/ITwapSource.sol": {"keccak256": "0x24ed5bf18cab6a3b1e2d78023c17bbb97524d59ea179e9c90594f697a1915509", "urls": ["bzz-raw://38a8ebb24b324d2241527ee423f7372b05bee5664a80fb6e6b881aaad4e3f826", "dweb:/ipfs/Qme3qtffJ7cMQM6JVu2WfpHgXsZGnD215SNrQ44CGnEVrS"], "license": "GPL-3.0-or-later"}, "contracts/interfaces/structs/PoolsharkStructs.sol": {"keccak256": "0xd1d06f34471f714ab6627ad435bd133251bf4813b2bec33e913af2d3d1d00e32", "urls": ["bzz-raw://721a19699a4dd5e52a97d8455a1fb67dee6602d4d78e698cc249dabeb4fa0501", "dweb:/ipfs/QmUs6nNW3LBtFmDeJgmxZHqGtyCCXQr1T8JnF4cpu9kP64"], "license": "GPLv3"}}, "version": 1}, "id": 18}