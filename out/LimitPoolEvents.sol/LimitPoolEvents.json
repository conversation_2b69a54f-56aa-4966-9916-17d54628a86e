{"abi": [{"type": "event", "name": "BurnLimit", "inputs": [{"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "positionId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "lower", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "upper", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "newClaim", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "liquidityBurned", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "tokenInClaimed", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "tokenOutBurned", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "BurnRange", "inputs": [{"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "positionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "liquidityBurned", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "amount0", "type": "int128", "indexed": false, "internalType": "int128"}, {"name": "amount1", "type": "int128", "indexed": false, "internalType": "int128"}], "anonymous": false}, {"type": "event", "name": "CollectRange0", "inputs": [{"name": "amount0", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "CollectRange1", "inputs": [{"name": "amount1", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "CompoundRange", "inputs": [{"name": "positionId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "liquidityCompounded", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "Initialize", "inputs": [{"name": "minTick", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "maxTick", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "startPrice", "type": "uint160", "indexed": false, "internalType": "uint160"}, {"name": "startTick", "type": "int24", "indexed": false, "internalType": "int24"}], "anonymous": false}, {"type": "event", "name": "MintLimit", "inputs": [{"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "lower", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "upper", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "positionId", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "epochLast", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "amountIn", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "liquidityMinted", "type": "uint128", "indexed": false, "internalType": "uint128"}], "anonymous": false}, {"type": "event", "name": "MintRange", "inputs": [{"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "lower", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "upper", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "positionId", "type": "uint32", "indexed": true, "internalType": "uint32"}, {"name": "liquidityMinted", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "amount0Delta", "type": "int128", "indexed": false, "internalType": "int128"}, {"name": "amount1Delta", "type": "int128", "indexed": false, "internalType": "int128"}], "anonymous": false}, {"type": "event", "name": "SampleCountIncreased", "inputs": [{"name": "newSampleCountMax", "type": "uint16", "indexed": false, "internalType": "uint16"}], "anonymous": false}, {"type": "event", "name": "SampleRecorded", "inputs": [{"name": "tickSecondsAccum", "type": "int56", "indexed": false, "internalType": "int56"}, {"name": "secondsPerLiquidityAccum", "type": "uint160", "indexed": false, "internalType": "uint160"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "feeGrowthGlobal0", "type": "uint200", "indexed": false, "internalType": "uint200"}, {"name": "feeGrowthGlobal1", "type": "uint200", "indexed": false, "internalType": "uint200"}, {"name": "price", "type": "uint160", "indexed": false, "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "feeAmount", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "tickAtPrice", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "indexed": true, "internalType": "bool"}, {"name": "exactIn", "type": "bool", "indexed": true, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "SyncLimitLiquidity", "inputs": [{"name": "liquidityAdded", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "tick", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "SyncLimitPool", "inputs": [{"name": "price", "type": "uint160", "indexed": false, "internalType": "uint160"}, {"name": "liquidity", "type": "uint128", "indexed": false, "internalType": "uint128"}, {"name": "epoch", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "tickAtPrice", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "isPool0", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "SyncLimitTick", "inputs": [{"name": "epoch", "type": "uint32", "indexed": false, "internalType": "uint32"}, {"name": "tick", "type": "int24", "indexed": false, "internalType": "int24"}, {"name": "zeroForOne", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "SyncRangeTick", "inputs": [{"name": "feeGrowthOutside0", "type": "uint200", "indexed": false, "internalType": "uint200"}, {"name": "feeGrowthOutside1", "type": "uint200", "indexed": false, "internalType": "uint200"}, {"name": "tick", "type": "int24", "indexed": false, "internalType": "int24"}], "anonymous": false}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.13+commit.abaa5c0e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"oldClaim\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"newClaim\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"liquidityBurned\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"tokenInClaimed\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"tokenOutBurned\",\"type\":\"uint128\"}],\"name\":\"BurnLimit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"positionId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"liquidityBurned\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"int128\",\"name\":\"amount0\",\"type\":\"int128\"},{\"indexed\":false,\"internalType\":\"int128\",\"name\":\"amount1\",\"type\":\"int128\"}],\"name\":\"BurnRange\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"amount0\",\"type\":\"uint128\"}],\"name\":\"CollectRange0\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"amount1\",\"type\":\"uint128\"}],\"name\":\"CollectRange1\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"liquidityCompounded\",\"type\":\"uint128\"}],\"name\":\"CompoundRange\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"minTick\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"maxTick\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"uint160\",\"name\":\"startPrice\",\"type\":\"uint160\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"startTick\",\"type\":\"int24\"}],\"name\":\"Initialize\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"epochLast\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"amountIn\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"liquidityMinted\",\"type\":\"uint128\"}],\"name\":\"MintLimit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"lower\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"upper\",\"type\":\"int24\"},{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"positionId\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"liquidityMinted\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"int128\",\"name\":\"amount0Delta\",\"type\":\"int128\"},{\"indexed\":false,\"internalType\":\"int128\",\"name\":\"amount1Delta\",\"type\":\"int128\"}],\"name\":\"MintRange\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"newSampleCountMax\",\"type\":\"uint16\"}],\"name\":\"SampleCountIncreased\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int56\",\"name\":\"tickSecondsAccum\",\"type\":\"int56\"},{\"indexed\":false,\"internalType\":\"uint160\",\"name\":\"secondsPerLiquidityAccum\",\"type\":\"uint160\"}],\"name\":\"SampleRecorded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountIn\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amountOut\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint200\",\"name\":\"feeGrowthGlobal0\",\"type\":\"uint200\"},{\"indexed\":false,\"internalType\":\"uint200\",\"name\":\"feeGrowthGlobal1\",\"type\":\"uint200\"},{\"indexed\":false,\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"feeAmount\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"},{\"indexed\":true,\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"},{\"indexed\":true,\"internalType\":\"bool\",\"name\":\"exactIn\",\"type\":\"bool\"}],\"name\":\"Swap\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"liquidityAdded\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"tick\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"name\":\"SyncLimitLiquidity\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint160\",\"name\":\"price\",\"type\":\"uint160\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"tickAtPrice\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"isPool0\",\"type\":\"bool\"}],\"name\":\"SyncLimitPool\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"tick\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"zeroForOne\",\"type\":\"bool\"}],\"name\":\"SyncLimitTick\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint200\",\"name\":\"feeGrowthOutside0\",\"type\":\"uint200\"},{\"indexed\":false,\"internalType\":\"uint200\",\"name\":\"feeGrowthOutside1\",\"type\":\"uint200\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"tick\",\"type\":\"int24\"}],\"name\":\"SyncRangeTick\",\"type\":\"event\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/base/events/LimitPoolEvents.sol\":\"LimitPoolEvents\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ds-test/=lib/ds-test/src/\",\":forge-std/=lib/forge-std/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"contracts/base/events/LimitPoolEvents.sol\":{\"keccak256\":\"0x43b5f4be369c6df18be4898210445d50f914470ebd2616aa7cdb090d12062b6f\",\"license\":\"GPL-3.0-or-later\",\"urls\":[\"bzz-raw://94ed73294dacda894e78793f53afd11a8c0edf47586c2c70ef826689bd37ad61\",\"dweb:/ipfs/Qmf7BsHw2fLQ5dei8WuYWGkamcZn3rFaFKtx2cuJPRYLXW\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.13+commit.abaa5c0e"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint32", "name": "positionId", "type": "uint32", "indexed": false}, {"internalType": "int24", "name": "lower", "type": "int24", "indexed": false}, {"internalType": "int24", "name": "upper", "type": "int24", "indexed": false}, {"internalType": "int24", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "int24", "indexed": false}, {"internalType": "int24", "name": "newClaim", "type": "int24", "indexed": false}, {"internalType": "bool", "name": "zeroForOne", "type": "bool", "indexed": false}, {"internalType": "uint128", "name": "liquidityBurned", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "tokenInClaimed", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "tokenOutBurned", "type": "uint128", "indexed": false}], "type": "event", "name": "BurnLimit", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "positionId", "type": "uint256", "indexed": true}, {"internalType": "uint128", "name": "liquidityBurned", "type": "uint128", "indexed": false}, {"internalType": "int128", "name": "amount0", "type": "int128", "indexed": false}, {"internalType": "int128", "name": "amount1", "type": "int128", "indexed": false}], "type": "event", "name": "BurnRange", "anonymous": false}, {"inputs": [{"internalType": "uint128", "name": "amount0", "type": "uint128", "indexed": false}], "type": "event", "name": "CollectRange0", "anonymous": false}, {"inputs": [{"internalType": "uint128", "name": "amount1", "type": "uint128", "indexed": false}], "type": "event", "name": "CollectRange1", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "positionId", "type": "uint32", "indexed": true}, {"internalType": "uint128", "name": "liquidityCompounded", "type": "uint128", "indexed": false}], "type": "event", "name": "CompoundRange", "anonymous": false}, {"inputs": [{"internalType": "int24", "name": "minTick", "type": "int24", "indexed": false}, {"internalType": "int24", "name": "maxTick", "type": "int24", "indexed": false}, {"internalType": "uint160", "name": "startPrice", "type": "uint160", "indexed": false}, {"internalType": "int24", "name": "startTick", "type": "int24", "indexed": false}], "type": "event", "name": "Initialize", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "int24", "name": "lower", "type": "int24", "indexed": false}, {"internalType": "int24", "name": "upper", "type": "int24", "indexed": false}, {"internalType": "bool", "name": "zeroForOne", "type": "bool", "indexed": false}, {"internalType": "uint32", "name": "positionId", "type": "uint32", "indexed": false}, {"internalType": "uint32", "name": "epochLast", "type": "uint32", "indexed": false}, {"internalType": "uint128", "name": "amountIn", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "liquidityMinted", "type": "uint128", "indexed": false}], "type": "event", "name": "MintLimit", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address", "indexed": true}, {"internalType": "int24", "name": "lower", "type": "int24", "indexed": false}, {"internalType": "int24", "name": "upper", "type": "int24", "indexed": false}, {"internalType": "uint32", "name": "positionId", "type": "uint32", "indexed": true}, {"internalType": "uint128", "name": "liquidityMinted", "type": "uint128", "indexed": false}, {"internalType": "int128", "name": "amount0Delta", "type": "int128", "indexed": false}, {"internalType": "int128", "name": "amount1Delta", "type": "int128", "indexed": false}], "type": "event", "name": "MintRange", "anonymous": false}, {"inputs": [{"internalType": "uint16", "name": "newSampleCountMax", "type": "uint16", "indexed": false}], "type": "event", "name": "SampleCountIncreased", "anonymous": false}, {"inputs": [{"internalType": "int56", "name": "tickSecondsAccum", "type": "int56", "indexed": false}, {"internalType": "uint160", "name": "secondsPerLiquidityAccum", "type": "uint160", "indexed": false}], "type": "event", "name": "SampleRecorded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amountIn", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "amountOut", "type": "uint256", "indexed": false}, {"internalType": "uint200", "name": "feeGrowthGlobal0", "type": "uint200", "indexed": false}, {"internalType": "uint200", "name": "feeGrowthGlobal1", "type": "uint200", "indexed": false}, {"internalType": "uint160", "name": "price", "type": "uint160", "indexed": false}, {"internalType": "uint128", "name": "liquidity", "type": "uint128", "indexed": false}, {"internalType": "uint128", "name": "feeAmount", "type": "uint128", "indexed": false}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24", "indexed": false}, {"internalType": "bool", "name": "zeroForOne", "type": "bool", "indexed": true}, {"internalType": "bool", "name": "exactIn", "type": "bool", "indexed": true}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "uint128", "name": "liquidityAdded", "type": "uint128", "indexed": false}, {"internalType": "int24", "name": "tick", "type": "int24", "indexed": false}, {"internalType": "bool", "name": "zeroForOne", "type": "bool", "indexed": false}], "type": "event", "name": "SyncLimitLiquidity", "anonymous": false}, {"inputs": [{"internalType": "uint160", "name": "price", "type": "uint160", "indexed": false}, {"internalType": "uint128", "name": "liquidity", "type": "uint128", "indexed": false}, {"internalType": "uint32", "name": "epoch", "type": "uint32", "indexed": false}, {"internalType": "int24", "name": "tickAtPrice", "type": "int24", "indexed": false}, {"internalType": "bool", "name": "isPool0", "type": "bool", "indexed": false}], "type": "event", "name": "SyncLimitPool", "anonymous": false}, {"inputs": [{"internalType": "uint32", "name": "epoch", "type": "uint32", "indexed": false}, {"internalType": "int24", "name": "tick", "type": "int24", "indexed": false}, {"internalType": "bool", "name": "zeroForOne", "type": "bool", "indexed": false}], "type": "event", "name": "SyncLimitTick", "anonymous": false}, {"inputs": [{"internalType": "uint200", "name": "feeGrowthOutside0", "type": "uint200", "indexed": false}, {"internalType": "uint200", "name": "feeGrowthOutside1", "type": "uint200", "indexed": false}, {"internalType": "int24", "name": "tick", "type": "int24", "indexed": false}], "type": "event", "name": "SyncRangeTick", "anonymous": false}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/=lib/openzeppelin-contracts/", "ds-test/=lib/ds-test/src/", "forge-std/=lib/forge-std/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/base/events/LimitPoolEvents.sol": "LimitPoolEvents"}, "evmVersion": "london", "libraries": {}}, "sources": {"contracts/base/events/LimitPoolEvents.sol": {"keccak256": "0x43b5f4be369c6df18be4898210445d50f914470ebd2616aa7cdb090d12062b6f", "urls": ["bzz-raw://94ed73294dacda894e78793f53afd11a8c0edf47586c2c70ef826689bd37ad61", "dweb:/ipfs/Qmf7BsHw2fLQ5dei8WuYWGkamcZn3rFaFKtx2cuJPRYLXW"], "license": "GPL-3.0-or-later"}}, "version": 1}, "id": 3}